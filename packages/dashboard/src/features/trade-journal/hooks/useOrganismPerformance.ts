/**
 * Organism Performance Hook
 *
 * Tracks and analyzes the performance of trading organisms (setups) by connecting
 * to the existing trade analysis system. Provides real-time performance metrics
 * for each organism to enhance the smart validation display.
 */

import { CompleteTradeData, tradeStorageService } from '@adhd-trading-dashboard/shared';
import { useEffect, useState } from 'react';

export interface OrganismPerformanceMetrics {
  organismId: string;
  organismName: string;
  totalTrades: number;
  winRate: number;
  avgRMultiple: number;
  totalPnL: number;
  lastUsed: Date | null;
  performance: 'excellent' | 'good' | 'average' | 'poor' | 'untested';
  confidenceScore: number; // 0-100
  trend: 'improving' | 'declining' | 'stable' | 'unknown';
}

export interface UseOrganismPerformanceReturn {
  performanceMetrics: Map<string, OrganismPerformanceMetrics>;
  isLoading: boolean;
  error: string | null;
  refreshPerformance: () => Promise<void>;
  getOrganismPerformance: (organismId: string) => OrganismPerformanceMetrics | null;
}

/**
 * Hook to track organism performance metrics
 */
export const useOrganismPerformance = (): UseOrganismPerformanceReturn => {
  const [performanceMetrics, setPerformanceMetrics] = useState<
    Map<string, OrganismPerformanceMetrics>
  >(new Map());
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Calculate performance rating based on metrics
   */
  const calculatePerformanceRating = (
    winRate: number,
    avgRMultiple: number,
    totalTrades: number
  ): 'excellent' | 'good' | 'average' | 'poor' | 'untested' => {
    if (totalTrades === 0) return 'untested';
    if (totalTrades < 5) return 'untested'; // Need minimum sample size

    if (winRate >= 0.7 && avgRMultiple >= 1.5) return 'excellent';
    if (winRate >= 0.6 && avgRMultiple >= 1.2) return 'good';
    if (winRate >= 0.5 && avgRMultiple >= 1.0) return 'average';
    return 'poor';
  };

  /**
   * Calculate confidence score (0-100) based on sample size and performance
   */
  const calculateConfidenceScore = (
    totalTrades: number,
    winRate: number,
    avgRMultiple: number
  ): number => {
    if (totalTrades === 0) return 0;

    // Base confidence on sample size (max 50 points)
    const sampleConfidence = Math.min(totalTrades * 2, 50);

    // Performance confidence (max 50 points)
    const performanceConfidence = Math.min(winRate * avgRMultiple * 50, 50);

    return Math.round(sampleConfidence + performanceConfidence);
  };

  /**
   * Analyze trend based on recent vs historical performance
   */
  const analyzeTrend = (
    trades: CompleteTradeData[]
  ): 'improving' | 'declining' | 'stable' | 'unknown' => {
    if (trades.length < 10) return 'unknown';

    const sortedTrades = trades.sort(
      (a, b) =>
        new Date(a.trade.entry_time || '').getTime() - new Date(b.trade.entry_time || '').getTime()
    );

    const midPoint = Math.floor(sortedTrades.length / 2);
    const recentTrades = sortedTrades.slice(midPoint);
    const historicalTrades = sortedTrades.slice(0, midPoint);

    const recentWinRate =
      recentTrades.filter(t => (t.trade.achieved_pl || 0) > 0).length / recentTrades.length;
    const historicalWinRate =
      historicalTrades.filter(t => (t.trade.achieved_pl || 0) > 0).length / historicalTrades.length;

    const difference = recentWinRate - historicalWinRate;

    if (difference > 0.1) return 'improving';
    if (difference < -0.1) return 'declining';
    return 'stable';
  };

  /**
   * Process trades to extract organism performance
   */
  const processOrganismPerformance = async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch all trades with atomic design data
      const allTrades = await tradeStorageService.getAllTrades();

      // Filter trades that have atomic design selections
      const atomicTrades = allTrades.filter(trade => trade.trade.atomicDesign?.matchedOrganism?.id);

      // Group trades by organism ID
      const tradesByOrganism = new Map<string, CompleteTradeData[]>();

      atomicTrades.forEach(trade => {
        const organismId = trade.trade.atomicDesign?.matchedOrganism?.id;
        if (organismId) {
          if (!tradesByOrganism.has(organismId)) {
            tradesByOrganism.set(organismId, []);
          }
          tradesByOrganism.get(organismId)!.push(trade);
        }
      });

      // Calculate performance metrics for each organism
      const newMetrics = new Map<string, OrganismPerformanceMetrics>();

      tradesByOrganism.forEach((trades, organismId) => {
        const winningTrades = trades.filter(t => (t.trade.achieved_pl || 0) > 0);
        const totalTrades = trades.length;
        const winRate = totalTrades > 0 ? winningTrades.length / totalTrades : 0;

        // Calculate average R-multiple
        const rMultiples = trades
          .filter(t => t.trade.r_multiple && t.trade.r_multiple > 0)
          .map(t => t.trade.r_multiple!);
        const avgRMultiple =
          rMultiples.length > 0 ? rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;

        const totalPnL = trades.reduce((sum, t) => sum + (t.trade.achieved_pl || 0), 0);

        // Find most recent trade
        const lastUsed =
          trades.length > 0
            ? new Date(Math.max(...trades.map(t => new Date(t.trade.entry_time || '').getTime())))
            : null;

        const performance = calculatePerformanceRating(winRate, avgRMultiple, totalTrades);
        const confidenceScore = calculateConfidenceScore(totalTrades, winRate, avgRMultiple);
        const trend = analyzeTrend(trades);

        // Get organism name from the first trade (they should all have the same organism)
        const organismName =
          trades[0]?.trade.atomicDesign?.matchedOrganism?.name || `Organism ${organismId}`;

        newMetrics.set(organismId, {
          organismId,
          organismName,
          totalTrades,
          winRate,
          avgRMultiple,
          totalPnL,
          lastUsed,
          performance,
          confidenceScore,
          trend,
        });
      });

      setPerformanceMetrics(newMetrics);
    } catch (err) {
      console.error('Error processing organism performance:', err);
      setError(err instanceof Error ? err.message : 'Failed to load organism performance');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Get performance metrics for a specific organism
   */
  const getOrganismPerformance = (organismId: string): OrganismPerformanceMetrics | null => {
    return performanceMetrics.get(organismId) || null;
  };

  /**
   * Refresh performance data
   */
  const refreshPerformance = async (): Promise<void> => {
    await processOrganismPerformance();
  };

  // Load performance data on mount
  useEffect(() => {
    processOrganismPerformance();
  }, []);

  return {
    performanceMetrics,
    isLoading,
    error,
    refreshPerformance,
    getOrganismPerformance,
  };
};
