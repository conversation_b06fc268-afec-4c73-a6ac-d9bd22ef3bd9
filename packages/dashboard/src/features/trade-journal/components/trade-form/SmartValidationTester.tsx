/**
 * Smart Validation Tester Component
 * 
 * Comprehensive testing component for the smart validation engine.
 * Tests all possible molecule combinations and validates suggestion functionality.
 */

import React, { useState, useCallback } from 'react';
import styled from 'styled-components';
import { SmartValidationDisplay } from './SmartValidationDisplay';
import type { SelectedMolecule } from './SmartValidationDisplay';
import type { SmartSuggestion, ModelTemplate } from '@adhd-trading-dashboard/shared/constants/atomicDesign';

const TestContainer = styled.div`
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  margin: ${({ theme }) => theme.spacing?.lg || '24px'};
`;

const TestSection = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing?.xl || '32px'};
  padding: ${({ theme }) => theme.spacing?.md || '16px'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
`;

const TestTitle = styled.h3`
  color: ${({ theme }) => theme.colors?.primary || '#00D2BE'};
  font-family: 'Orbitron', monospace;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '16px'};
`;

const TestButton = styled.button`
  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '16px'};
  margin: ${({ theme }) => theme.spacing?.xs || '4px'};
  background: ${({ theme }) => theme.colors?.primary || '#00D2BE'};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme }) => theme.colors?.primaryDark || '#00B8A6'};
    transform: translateY(-1px);
  }
`;

const TestResults = styled.div`
  margin-top: ${({ theme }) => theme.spacing?.md || '16px'};
  padding: ${({ theme }) => theme.spacing?.md || '16px'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  font-family: 'JetBrains Mono', monospace;
  font-size: 0.8rem;
`;

const MoleculeDisplay = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  margin: ${({ theme }) => theme.spacing?.sm || '8px'} 0;
`;

const MoleculeChip = styled.span`
  padding: 2px 6px;
  background: ${({ theme }) => theme.colors?.info || '#0600EF'}20;
  color: ${({ theme }) => theme.colors?.info || '#0600EF'};
  border-radius: 3px;
  font-size: 0.7rem;
  border: 1px solid ${({ theme }) => theme.colors?.info || '#0600EF'}40;
`;

/**
 * Test scenarios for comprehensive validation testing
 */
const TEST_SCENARIOS = [
  {
    name: 'Empty Selection',
    molecules: [],
    description: 'Test initial suggestions when no molecules are selected',
  },
  {
    name: 'Single State Molecule',
    molecules: [{ atom: 'Market Structure', type: 'state' as const, expression: 'Bullish' }],
    description: 'Test suggestions after selecting one state molecule',
  },
  {
    name: 'Single Behavior Molecule',
    molecules: [{ atom: 'FVG', type: 'behavior' as const, expression: 'Bullish FVG' }],
    description: 'Test suggestions after selecting one behavior molecule',
  },
  {
    name: 'Partial FVG Setup',
    molecules: [
      { atom: 'Market Structure', type: 'state' as const, expression: 'Bullish' },
      { atom: 'FVG', type: 'behavior' as const, expression: 'Bullish FVG' },
    ],
    description: 'Test partial organism match suggestions',
  },
  {
    name: 'Complete FVG Setup',
    molecules: [
      { atom: 'Market Structure', type: 'state' as const, expression: 'Bullish' },
      { atom: 'FVG', type: 'behavior' as const, expression: 'Bullish FVG' },
      { atom: 'Liquidity', type: 'target' as const, expression: 'BSL' },
    ],
    description: 'Test complete organism detection',
  },
  {
    name: 'Order Block Setup',
    molecules: [
      { atom: 'Market Structure', type: 'state' as const, expression: 'Bullish' },
      { atom: 'Order Block', type: 'behavior' as const, expression: 'Bullish OB' },
      { atom: 'Liquidity', type: 'target' as const, expression: 'SSL' },
    ],
    description: 'Test Order Block organism detection',
  },
];

/**
 * SmartValidationTester Component
 */
export const SmartValidationTester: React.FC = () => {
  const [currentMolecules, setCurrentMolecules] = useState<SelectedMolecule[]>([]);
  const [currentModelTemplate, setCurrentModelTemplate] = useState<ModelTemplate | undefined>();
  const [testResults, setTestResults] = useState<string>('');

  /**
   * Handle suggestion click for testing
   */
  const handleSuggestionClick = useCallback((suggestion: SmartSuggestion) => {
    const result = `🎯 Suggestion clicked: ${suggestion.message}\n` +
                  `Type: ${suggestion.type}\n` +
                  `Action: ${JSON.stringify(suggestion.action, null, 2)}\n\n`;
    
    setTestResults(prev => prev + result);

    // Apply the suggestion if it has an action
    if (suggestion.action) {
      if (suggestion.action.atom && suggestion.action.type && suggestion.action.expression) {
        const newMolecule: SelectedMolecule = {
          atom: suggestion.action.atom,
          type: suggestion.action.type,
          expression: suggestion.action.expression,
        };

        // Check if already selected
        const isAlreadySelected = currentMolecules.some(
          mol => mol.atom === newMolecule.atom && 
                 mol.type === newMolecule.type && 
                 mol.expression === newMolecule.expression
        );

        if (!isAlreadySelected) {
          setCurrentMolecules(prev => [...prev, newMolecule]);
        }
      }

      if (suggestion.action.modelTemplate) {
        setCurrentModelTemplate(suggestion.action.modelTemplate);
      }
    }
  }, [currentMolecules]);

  /**
   * Load test scenario
   */
  const loadTestScenario = useCallback((scenario: typeof TEST_SCENARIOS[0]) => {
    setCurrentMolecules(scenario.molecules);
    setCurrentModelTemplate(undefined);
    setTestResults(`📋 Loaded test scenario: ${scenario.name}\n${scenario.description}\n\n`);
  }, []);

  /**
   * Clear current selection
   */
  const clearSelection = useCallback(() => {
    setCurrentMolecules([]);
    setCurrentModelTemplate(undefined);
    setTestResults('🧹 Cleared selection\n\n');
  }, []);

  /**
   * Run all test scenarios
   */
  const runAllTests = useCallback(() => {
    setTestResults('🚀 Running comprehensive validation tests...\n\n');
    
    TEST_SCENARIOS.forEach((scenario, index) => {
      setTimeout(() => {
        loadTestScenario(scenario);
      }, index * 2000); // 2 second delay between tests
    });
  }, [loadTestScenario]);

  return (
    <TestContainer>
      <TestTitle>🧪 Smart Validation Engine Tester</TestTitle>
      
      {/* Test Controls */}
      <TestSection>
        <h4>Test Scenarios</h4>
        {TEST_SCENARIOS.map((scenario, index) => (
          <TestButton key={index} onClick={() => loadTestScenario(scenario)}>
            {scenario.name}
          </TestButton>
        ))}
        <TestButton onClick={clearSelection} style={{ background: '#e10600' }}>
          Clear Selection
        </TestButton>
        <TestButton onClick={runAllTests} style={{ background: '#FF8700' }}>
          Run All Tests
        </TestButton>
      </TestSection>

      {/* Current Selection Display */}
      <TestSection>
        <h4>Current Selection</h4>
        <div>
          <strong>Model Template:</strong> {currentModelTemplate || 'None'}
        </div>
        <div>
          <strong>Molecules ({currentMolecules.length}):</strong>
        </div>
        <MoleculeDisplay>
          {currentMolecules.map((mol, index) => (
            <MoleculeChip key={index}>
              {mol.atom} → {mol.type} → {mol.expression}
            </MoleculeChip>
          ))}
        </MoleculeDisplay>
      </TestSection>

      {/* Smart Validation Display */}
      <TestSection>
        <h4>Smart Validation Output</h4>
        <SmartValidationDisplay
          selectedMolecules={currentMolecules}
          modelTemplate={currentModelTemplate}
          onSuggestionClick={handleSuggestionClick}
          disabled={false}
        />
      </TestSection>

      {/* Test Results Log */}
      <TestSection>
        <h4>Test Results Log</h4>
        <TestResults>
          {testResults || 'No test results yet. Click a test scenario to begin.'}
        </TestResults>
      </TestSection>
    </TestContainer>
  );
};

export default SmartValidationTester;
