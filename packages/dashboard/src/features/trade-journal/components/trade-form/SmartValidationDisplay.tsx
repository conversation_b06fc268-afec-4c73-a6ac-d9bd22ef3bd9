/**
 * SmartValidationDisplay Component
 *
 * Enhanced validation display with F1 racing theme integration and performance tracking.
 * Displays real-time validation messages, organism matches, and smart suggestions
 * for the atomic design trading system. Implements Task 3 requirements with F1 aesthetics.
 */

import type {
  ModelTemplate,
  MoleculeType,
  OrganismMatch,
  SmartSuggestion,
  ValidationResult,
} from '@adhd-trading-dashboard/shared/constants/atomicDesign';
import {
  generateSmartSuggestions,
  getOrganismMatches,
  validateMoleculeSelection,
} from '@adhd-trading-dashboard/shared/constants/atomicDesign';
import React, { useMemo } from 'react';
import styled, { css, keyframes } from 'styled-components';
import { useOrganismPerformance } from '../../hooks/useOrganismPerformance';

/**
 * F1 Racing Theme Animations
 */
const f1Pulse = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
`;

const f1Glow = keyframes`
  0%, 100% { box-shadow: 0 0 5px currentColor; }
  50% { box-shadow: 0 0 20px currentColor, 0 0 30px currentColor; }
`;

const racingStripe = keyframes`
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
`;

export interface SelectedMolecule {
  atom: string;
  type: MoleculeType;
  expression: string;
}

export interface SmartValidationDisplayProps {
  /** Currently selected molecules */
  selectedMolecules: SelectedMolecule[];
  /** Current model template */
  modelTemplate?: ModelTemplate;
  /** Callback when suggestion is clicked */
  onSuggestionClick?: (suggestion: SmartSuggestion) => void;
  /** Whether the component is disabled */
  disabled?: boolean;
  /** Custom className */
  className?: string;
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const ValidationMessage = styled.div<{ level: 'error' | 'warning' | 'info' | 'success' }>`
  padding: ${({ theme }) => theme.spacing?.sm || '8px'}
    ${({ theme }) => theme.spacing?.md || '12px'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  border: 1px solid;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;

  /* F1 Racing Theme Colors */
  ${({ level, theme }) => {
    switch (level) {
      case 'error':
        return css`
          background: ${theme.colors?.error || '#e10600'}15;
          color: ${theme.colors?.error || '#e10600'};
          border-color: ${theme.colors?.error || '#e10600'};
          animation: ${f1Pulse} 2s ease-in-out infinite;
        `;
      case 'warning':
        return css`
          background: ${theme.colors?.warning || '#FF8700'}15;
          color: ${theme.colors?.warning || '#FF8700'};
          border-color: ${theme.colors?.warning || '#FF8700'};
        `;
      case 'success':
        return css`
          background: ${theme.colors?.success || '#00D2BE'}15;
          color: ${theme.colors?.success || '#00D2BE'};
          border-color: ${theme.colors?.success || '#00D2BE'};
          animation: ${f1Glow} 3s ease-in-out infinite;
        `;
      default:
        return css`
          background: ${theme.colors?.info || '#0600EF'}15;
          color: ${theme.colors?.info || '#0600EF'};
          border-color: ${theme.colors?.info || '#0600EF'};
        `;
    }
  }}

  /* Racing stripe effect for success messages */
  ${({ level }) =>
    level === 'success' &&
    css`
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 2px;
        background: linear-gradient(90deg, transparent, #00d2be, transparent);
        animation: ${racingStripe} 2s ease-in-out infinite;
      }
    `}
`;

const OrganismMatchCard = styled.div<{
  status: 'complete' | 'partial' | 'possible';
  performance?: 'excellent' | 'good' | 'average' | 'poor' | 'untested';
}>`
  padding: ${({ theme }) => theme.spacing?.md || '12px'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  border: 1px solid;
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  position: relative;
  transition: all 0.3s ease;
  margin-bottom: ${({ theme }) => theme.spacing?.sm || '8px'};

  /* F1 Racing Status Colors */
  ${({ status, theme }) => {
    const getStatusColor = () => {
      switch (status) {
        case 'complete':
          return theme.colors?.success || '#00D2BE'; // Mercedes Green
        case 'partial':
          return theme.colors?.warning || '#FF8700'; // McLaren Orange
        default:
          return theme.colors?.border || 'var(--border-primary)';
      }
    };

    const statusColor = getStatusColor();

    return css`
      border-color: ${statusColor};
      box-shadow: 0 2px 8px ${statusColor}20;

      ${status === 'complete' &&
      css`
        animation: ${f1Glow} 4s ease-in-out infinite;
      `}

      ${status === 'partial' &&
      css`
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 2px;
          background: linear-gradient(90deg, ${statusColor}, transparent, ${statusColor});
          animation: ${racingStripe} 3s linear infinite;
        }
      `}

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px ${statusColor}30;
      }
    `;
  }}
`;

const OrganismTitle = styled.div<{ status: 'complete' | 'partial' | 'possible' }>`
  font-weight: 700;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  margin-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};

  ${({ status, theme }) => {
    switch (status) {
      case 'complete':
        return `color: ${theme.colors?.success || '#28a745'};`;
      case 'partial':
        return `color: ${theme.colors?.warning || '#ffc107'};`;
      default:
        return `color: ${theme.colors?.textSecondary || 'var(--text-secondary)'};`;
    }
  }}
`;

const ProgressBar = styled.div<{ completeness: number }>`
  width: 100%;
  height: 4px;
  background: ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: 2px;
  overflow: hidden;
  margin: ${({ theme }) => theme.spacing?.xs || '4px'} 0;

  &::after {
    content: '';
    display: block;
    height: 100%;
    width: ${({ completeness }) => completeness}%;
    background: ${({ completeness, theme }) =>
      completeness === 100
        ? theme.colors?.success || '#28a745'
        : theme.colors?.warning || '#ffc107'};
    transition: width 0.3s ease;
  }
`;

const OrganismDetails = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin-top: ${({ theme }) => theme.spacing?.xs || '4px'};
`;

const SuggestionCard = styled.div`
  padding: ${({ theme }) => theme.spacing?.sm || '8px'}
    ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}10;
  border: 1px solid ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}30;
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}50;
  }
`;

const SectionTitle = styled.h5`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  font-family: 'Orbitron', monospace; /* F1 header font */
  position: relative;

  /* F1 Racing accent line */
  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30px;
    height: 2px;
    background: ${({ theme }) => theme.colors?.primary || '#00D2BE'};
    border-radius: 1px;
  }
`;

const PerformanceBadge = styled.span<{
  performance: 'excellent' | 'good' | 'average' | 'poor' | 'untested';
}>`
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-left: 8px;

  ${({ performance, theme }) => {
    switch (performance) {
      case 'excellent':
        return css`
          background: ${theme.colors?.success || '#00D2BE'}20;
          color: ${theme.colors?.success || '#00D2BE'};
          border: 1px solid ${theme.colors?.success || '#00D2BE'}40;
        `;
      case 'good':
        return css`
          background: ${theme.colors?.info || '#0600EF'}20;
          color: ${theme.colors?.info || '#0600EF'};
          border: 1px solid ${theme.colors?.info || '#0600EF'}40;
        `;
      case 'average':
        return css`
          background: ${theme.colors?.warning || '#FF8700'}20;
          color: ${theme.colors?.warning || '#FF8700'};
          border: 1px solid ${theme.colors?.warning || '#FF8700'}40;
        `;
      case 'poor':
        return css`
          background: ${theme.colors?.error || '#e10600'}20;
          color: ${theme.colors?.error || '#e10600'};
          border: 1px solid ${theme.colors?.error || '#e10600'}40;
        `;
      default:
        return css`
          background: ${theme.colors?.border || 'var(--border-primary)'}20;
          color: ${theme.colors?.textSecondary || 'var(--text-secondary)'};
          border: 1px solid ${theme.colors?.border || 'var(--border-primary)'}40;
        `;
    }
  }}
`;

/**
 * SmartValidationDisplay Component
 */
export const SmartValidationDisplay: React.FC<SmartValidationDisplayProps> = ({
  selectedMolecules,
  modelTemplate,
  onSuggestionClick,
  disabled = false,
  className,
}) => {
  // Get organism performance data
  const { getOrganismPerformance, isLoading: performanceLoading } = useOrganismPerformance();

  /**
   * Get validation result for current selection
   */
  const validationResult = useMemo((): ValidationResult => {
    return validateMoleculeSelection(selectedMolecules, modelTemplate);
  }, [selectedMolecules, modelTemplate]);

  /**
   * Get organism matches for current selection with performance data
   */
  const organismMatches = useMemo((): OrganismMatch[] => {
    return getOrganismMatches(selectedMolecules).slice(0, 3); // Show top 3 matches
  }, [selectedMolecules]);

  /**
   * Get smart suggestions for current selection
   */
  const smartSuggestions = useMemo((): SmartSuggestion[] => {
    return generateSmartSuggestions(selectedMolecules, modelTemplate).slice(0, 2); // Show top 2 suggestions
  }, [selectedMolecules, modelTemplate]);

  /**
   * Handle suggestion click
   */
  const handleSuggestionClick = (suggestion: SmartSuggestion) => {
    if (!disabled && onSuggestionClick) {
      onSuggestionClick(suggestion);
    }
  };

  return (
    <Container className={className}>
      {/* Validation Message */}
      <ValidationMessage level={validationResult.level}>
        {validationResult.message}
        {validationResult.suggestions && validationResult.suggestions.length > 0 && (
          <div style={{ marginTop: '8px', fontSize: '0.8em', opacity: 0.9 }}>
            {validationResult.suggestions.map((suggestion, index) => (
              <div key={index}>• {suggestion}</div>
            ))}
          </div>
        )}
      </ValidationMessage>

      {/* Organism Matches with Performance Data */}
      {organismMatches.length > 0 && (
        <div>
          <SectionTitle>🎯 Setup Detection</SectionTitle>
          {organismMatches.map((match, index) => {
            const performanceData = getOrganismPerformance(match.organism.id);

            return (
              <OrganismMatchCard
                key={index}
                status={match.status}
                performance={performanceData?.performance}
              >
                <OrganismTitle status={match.status}>
                  {match.status === 'complete' && '✅ '}
                  {match.status === 'partial' && '🔄 '}
                  {match.status === 'possible' && '💡 '}
                  {match.organism.name}
                  {performanceData && (
                    <PerformanceBadge performance={performanceData.performance}>
                      {performanceData.performance}
                    </PerformanceBadge>
                  )}
                </OrganismTitle>

                <ProgressBar completeness={match.completeness} />

                <OrganismDetails>
                  {match.completeness}% complete • {match.organism.modelTemplate}
                  {match.missingMolecules.length > 0 && (
                    <> • Missing: {match.missingMolecules.length} molecules</>
                  )}
                  {match.status === 'complete' && <> • Ready to use!</>}
                  {performanceData && performanceData.totalTrades > 0 && (
                    <>
                      <br />
                      📊 {performanceData.totalTrades} trades •{' '}
                      {(performanceData.winRate * 100).toFixed(0)}% win rate
                      {performanceData.avgRMultiple > 0 && (
                        <> • {performanceData.avgRMultiple.toFixed(1)}R avg</>
                      )}
                      {performanceData.trend !== 'unknown' && (
                        <>
                          {' '}
                          •{' '}
                          {performanceData.trend === 'improving'
                            ? '📈'
                            : performanceData.trend === 'declining'
                            ? '📉'
                            : '➡️'}{' '}
                          {performanceData.trend}
                        </>
                      )}
                    </>
                  )}
                </OrganismDetails>
              </OrganismMatchCard>
            );
          })}
        </div>
      )}

      {/* Smart Suggestions */}
      {smartSuggestions.length > 0 && (
        <div>
          <SectionTitle>💡 Smart Suggestions</SectionTitle>
          {smartSuggestions.map((suggestion, index) => (
            <SuggestionCard
              key={index}
              onClick={() => handleSuggestionClick(suggestion)}
              style={{ opacity: disabled ? 0.5 : 1, cursor: disabled ? 'not-allowed' : 'pointer' }}
            >
              {suggestion.message}
            </SuggestionCard>
          ))}
        </div>
      )}
    </Container>
  );
};

export default SmartValidationDisplay;
