/**
 * Organism Performance Display Component
 *
 * F1 racing-themed component that displays real-time performance analytics
 * for trading organisms with telemetry-style data visualization.
 */

import React from 'react';
import styled, { css, keyframes } from 'styled-components';
import type { OrganismPerformanceMetrics } from '../../hooks/useOrganismPerformance';

interface OrganismPerformanceDisplayProps {
  performanceData: OrganismPerformanceMetrics;
  compact?: boolean;
  showTrend?: boolean;
  className?: string;
}

/**
 * F1 Racing Animations
 */
const telemetryPulse = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
`;

const dataStream = keyframes`
  0% { transform: translateX(-10px); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateX(0); opacity: 1; }
`;

const performanceGlow = keyframes`
  0%, 100% { box-shadow: 0 0 5px currentColor; }
  50% { box-shadow: 0 0 15px currentColor, 0 0 25px currentColor; }
`;

/**
 * F1 Telemetry Styled Components
 */
const TelemetryContainer = styled.div<{ performance: string }>`
  background: ${({ theme }) => theme.colors?.surface || '#1A1A1A'};
  border: 1px solid;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  padding: ${({ theme }) => theme.spacing?.sm || '8px'};
  position: relative;
  overflow: hidden;
  font-family: 'JetBrains Mono', monospace;

  ${({ performance, theme }) => {
    switch (performance) {
      case 'excellent':
        return css`
          border-color: ${theme.colors?.success || '#00D2BE'};
          background: ${theme.colors?.success || '#00D2BE'}08;
          animation: ${performanceGlow} 3s ease-in-out infinite;
        `;
      case 'good':
        return css`
          border-color: ${theme.colors?.info || '#0600EF'};
          background: ${theme.colors?.info || '#0600EF'}08;
        `;
      case 'average':
        return css`
          border-color: ${theme.colors?.warning || '#FF8700'};
          background: ${theme.colors?.warning || '#FF8700'}08;
        `;
      case 'poor':
        return css`
          border-color: ${theme.colors?.error || '#e10600'};
          background: ${theme.colors?.error || '#e10600'}08;
          animation: ${telemetryPulse} 2s ease-in-out infinite;
        `;
      default:
        return css`
          border-color: ${theme.colors?.border || 'var(--border-primary)'};
          background: ${theme.colors?.border || 'var(--border-primary)'}05;
        `;
    }
  }}

  /* F1 Racing stripe */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: ${({ performance, theme }) => {
      switch (performance) {
        case 'excellent':
          return theme.colors?.success || '#00D2BE';
        case 'good':
          return theme.colors?.info || '#0600EF';
        case 'average':
          return theme.colors?.warning || '#FF8700';
        case 'poor':
          return theme.colors?.error || '#e10600';
        default:
          return theme.colors?.border || 'var(--border-primary)';
      }
    }};
  }
`;

const TelemetryHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};
`;

const PerformanceLabel = styled.span<{ performance: string }>`
  font-size: 0.7rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 2px 6px;
  border-radius: 3px;
  animation: ${dataStream} 0.5s ease-out;

  ${({ performance, theme }) => {
    switch (performance) {
      case 'excellent':
        return css`
          background: ${theme.colors?.success || '#00D2BE'}20;
          color: ${theme.colors?.success || '#00D2BE'};
        `;
      case 'good':
        return css`
          background: ${theme.colors?.info || '#0600EF'}20;
          color: ${theme.colors?.info || '#0600EF'};
        `;
      case 'average':
        return css`
          background: ${theme.colors?.warning || '#FF8700'}20;
          color: ${theme.colors?.warning || '#FF8700'};
        `;
      case 'poor':
        return css`
          background: ${theme.colors?.error || '#e10600'}20;
          color: ${theme.colors?.error || '#e10600'};
        `;
      default:
        return css`
          background: ${theme.colors?.border || 'var(--border-primary)'}20;
          color: ${theme.colors?.textSecondary || 'var(--text-secondary)'};
        `;
    }
  }}
`;

const ConfidenceBar = styled.div<{ confidence: number }>`
  width: 40px;
  height: 4px;
  background: ${({ theme }) => theme.colors?.border || 'var(--border-primary)'}30;
  border-radius: 2px;
  overflow: hidden;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: ${({ confidence }) => confidence}%;
    background: ${({ confidence, theme }) => {
      if (confidence >= 80) return theme.colors?.success || '#00D2BE';
      if (confidence >= 60) return theme.colors?.info || '#0600EF';
      if (confidence >= 40) return theme.colors?.warning || '#FF8700';
      return theme.colors?.error || '#e10600';
    }};
    transition: width 0.3s ease;
    animation: ${dataStream} 0.8s ease-out;
  }
`;

const TelemetryData = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  font-size: 0.7rem;
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
`;

const DataPoint = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  animation: ${dataStream} 0.6s ease-out;
`;

const DataLabel = styled.span`
  opacity: 0.8;
`;

const DataValue = styled.span<{ highlight?: boolean }>`
  font-weight: 600;
  color: ${({ highlight, theme }) =>
    highlight
      ? theme.colors?.primary || '#00D2BE'
      : theme.colors?.textPrimary || 'var(--text-primary)'};
`;

const TrendIndicator = styled.div<{ trend: string }>`
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.65rem;
  margin-top: ${({ theme }) => theme.spacing?.xs || '4px'};
  padding-top: ${({ theme }) => theme.spacing?.xs || '4px'};
  border-top: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'}20;

  ${({ trend, theme }) => {
    switch (trend) {
      case 'improving':
        return css`
          color: ${theme.colors?.success || '#00D2BE'};
        `;
      case 'declining':
        return css`
          color: ${theme.colors?.error || '#e10600'};
        `;
      default:
        return css`
          color: ${theme.colors?.textSecondary || 'var(--text-secondary)'};
        `;
    }
  }}
`;

/**
 * Organism Performance Display Component
 */
export const OrganismPerformanceDisplay: React.FC<OrganismPerformanceDisplayProps> = ({
  performanceData,
  showTrend = true,
  className,
}) => {
  const {
    performance,
    totalTrades,
    winRate,
    avgRMultiple,
    totalPnL,
    confidenceScore,
    trend,
    lastUsed,
  } = performanceData;

  const formatPnL = (pnl: number): string => {
    const sign = pnl >= 0 ? '+' : '';
    return `${sign}${pnl.toFixed(0)}`;
  };

  const formatDate = (date: Date | null): string => {
    if (!date) return 'Never';
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  const getTrendIcon = (trend: string): string => {
    switch (trend) {
      case 'improving':
        return '📈';
      case 'declining':
        return '📉';
      case 'stable':
        return '➡️';
      default:
        return '❓';
    }
  };

  if (totalTrades === 0) {
    return (
      <TelemetryContainer performance='untested' className={className}>
        <TelemetryHeader>
          <PerformanceLabel performance='untested'>UNTESTED</PerformanceLabel>
          <ConfidenceBar confidence={0} />
        </TelemetryHeader>
        <div style={{ fontSize: '0.7rem', opacity: 0.6, textAlign: 'center' }}>
          No trading data available
        </div>
      </TelemetryContainer>
    );
  }

  return (
    <TelemetryContainer performance={performance} className={className}>
      <TelemetryHeader>
        <PerformanceLabel performance={performance}>{performance.toUpperCase()}</PerformanceLabel>
        <ConfidenceBar confidence={confidenceScore} />
      </TelemetryHeader>

      <TelemetryData>
        <DataPoint>
          <DataLabel>Trades:</DataLabel>
          <DataValue>{totalTrades}</DataValue>
        </DataPoint>

        <DataPoint>
          <DataLabel>Win Rate:</DataLabel>
          <DataValue highlight={winRate >= 0.6}>{(winRate * 100).toFixed(0)}%</DataValue>
        </DataPoint>

        <DataPoint>
          <DataLabel>Avg R:</DataLabel>
          <DataValue highlight={avgRMultiple >= 1.5}>{avgRMultiple.toFixed(1)}R</DataValue>
        </DataPoint>

        <DataPoint>
          <DataLabel>P&L:</DataLabel>
          <DataValue highlight={totalPnL > 0}>{formatPnL(totalPnL)}</DataValue>
        </DataPoint>
      </TelemetryData>

      {showTrend && trend !== 'unknown' && (
        <TrendIndicator trend={trend}>
          {getTrendIcon(trend)} {trend.toUpperCase()}
          <span style={{ marginLeft: 'auto', opacity: 0.6 }}>Last: {formatDate(lastUsed)}</span>
        </TrendIndicator>
      )}
    </TelemetryContainer>
  );
};

export default OrganismPerformanceDisplay;
