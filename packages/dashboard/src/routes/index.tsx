/**
 * Application Routes
 *
 * This file defines the application routes using React Router.
 */

import React, { lazy, Suspense } from 'react';
import { Navigate, Route, Routes } from 'react-router-dom';
import LoadingScreen from './components/molecules/LoadingScreen';
import MainLayout from './layouts/MainLayout';

// Lazy-loaded pages for code splitting
const Dashboard = lazy(() => import('../pages/Dashboard'));
const DailyGuide = lazy(() => import('../pages/DailyGuide'));
const TradeJournal = lazy(() => import('../pages/TradeJournal'));
const TradeAnalysis = lazy(() => import('../pages/TradeAnalysis'));
const TradeForm = lazy(() => import('../pages/TradeForm'));
const Settings = lazy(() => import('../pages/Settings'));
const NotFound = lazy(() => import('../pages/NotFound'));

// Testing components
const SmartValidationTester = lazy(
  () => import('../features/trade-journal/components/trade-form/SmartValidationTester')
);

/**
 * AppRoutes Component
 *
 * Defines the application routes using React Router.
 */
const AppRoutes: React.FC = () => {
  return (
    <Suspense fallback={<LoadingScreen />}>
      <Routes>
        {/* Main layout routes */}
        <Route path='/' element={<MainLayout />}>
          <Route index element={<Dashboard />} />
          <Route path='daily-guide' element={<DailyGuide />} />
          <Route path='journal' element={<TradeJournal />} />
          <Route path='trade-analysis' element={<TradeAnalysis />} />
          {/*
            TRADE ROUTES
            Using nested routes for better organization and to prevent path matching conflicts.
            The order of routes is important - more specific routes must come before less specific ones.
          */}
          <Route path='trade'>
            {/* New trade route - most specific, comes first */}
            <Route path='new' element={<TradeForm />} />
            {/* Edit trade route - specific path with ID parameter */}
            <Route path='edit/:id' element={<TradeForm />} />
            {/* View trade route - least specific, comes last */}
            <Route path=':id' element={<TradeForm />} />
          </Route>
          <Route path='settings' element={<Settings />} />
          {/* Testing routes - only available in development */}
          <Route path='test/smart-validation' element={<SmartValidationTester />} />
          <Route path='*' element={<NotFound />} />
        </Route>

        {/* Redirect from legacy paths */}
        <Route path='/dashboard' element={<Navigate to='/' replace />} />
      </Routes>
    </Suspense>
  );
};

export default AppRoutes;
