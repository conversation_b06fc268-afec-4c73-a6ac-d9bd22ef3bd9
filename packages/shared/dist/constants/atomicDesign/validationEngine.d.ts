import { ModelTemplate, MoleculeType, TradingOrganism } from '../../types/trading';
export interface ValidationResult {
    isValid: boolean;
    level: 'error' | 'warning' | 'info' | 'success';
    message: string;
    suggestions?: string[];
    blockedReasons?: string[];
}
export interface OrganismMatch {
    organism: TradingOrganism;
    completeness: number;
    missingMolecules: Array<{
        atom: string;
        type: MoleculeType;
        expression: string;
    }>;
    status: 'complete' | 'partial' | 'possible';
}
export interface SmartSuggestion {
    type: 'completion' | 'alternative' | 'model-change';
    message: string;
    action?: {
        atom?: string;
        type?: MoleculeType;
        expression?: string;
        modelTemplate?: ModelTemplate;
    };
}
/**
 * Validation Engine Class
 */
export declare class AtomicDesignValidationEngine {
    /**
     * Validate molecule selection for model eligibility
     */
    static validateMoleculeForModel(atom: string, type: MoleculeType, expression: string, modelTemplate: ModelTemplate): ValidationResult;
    /**
     * Get alternative molecules when current selection is blocked
     */
    static getModelAlternatives(atom: string, type: MoleculeType, expression: string, modelTemplate: ModelTemplate): string[];
    /**
     * Validate complete molecule selection
     */
    static validateMoleculeSelection(molecules: Array<{
        atom: string;
        type: MoleculeType;
        expression: string;
    }>, modelTemplate?: ModelTemplate): ValidationResult;
    /**
     * Find duplicate molecules in selection
     */
    static findDuplicateMolecules(molecules: Array<{
        atom: string;
        type: MoleculeType;
        expression: string;
    }>): string[];
    /**
     * Analyze molecule type balance
     */
    static analyzeMoleculeTypeBalance(molecules: Array<{
        atom: string;
        type: MoleculeType;
        expression: string;
    }>): {
        issues: string[];
        suggestions: string[];
    };
    /**
     * Get real-time organism matches with completeness
     */
    static getOrganismMatches(molecules: Array<{
        atom: string;
        type: MoleculeType;
        expression: string;
    }>): OrganismMatch[];
    /**
     * Generate smart suggestions based on current selection
     */
    static generateSmartSuggestions(molecules: Array<{
        atom: string;
        type: MoleculeType;
        expression: string;
    }>, modelTemplate?: ModelTemplate): SmartSuggestion[];
    /**
     * Suggest model template based on molecule selection
     */
    static suggestModelTemplate(molecules: Array<{
        atom: string;
        type: MoleculeType;
        expression: string;
    }>): ModelTemplate | null;
}
/**
 * Export validation functions for easy use
 */
export declare const validateMoleculeForModel: typeof AtomicDesignValidationEngine.validateMoleculeForModel;
export declare const validateMoleculeSelection: typeof AtomicDesignValidationEngine.validateMoleculeSelection;
export declare const getOrganismMatches: typeof AtomicDesignValidationEngine.getOrganismMatches;
export declare const generateSmartSuggestions: typeof AtomicDesignValidationEngine.generateSmartSuggestions;
//# sourceMappingURL=validationEngine.d.ts.map