import { BiasAssistance, ModelTemplate, MoleculeType } from '../../types/trading';
import { AtomicDesignValidationEngine, OrganismMatch, SmartSuggestion } from './validationEngine';
export interface EnhancedOrganismMatch extends OrganismMatch {
    biasAssistance?: BiasAssistance[];
    confidenceBoost?: number;
    recommendedNextMolecules?: Array<{
        atom: string;
        type: MoleculeType;
        expression: string;
        priority: 'high' | 'medium' | 'low';
        reason: string;
    }>;
}
export interface EnhancedSmartSuggestion extends SmartSuggestion {
    priority: 'critical' | 'high' | 'medium' | 'low';
    confidence: number;
    biasSupport?: boolean;
    f1Theme?: {
        color: 'mercedes-green' | 'mclaren-orange' | 'ferrari-red' | 'racing-blue';
        animation?: 'pulse' | 'glow' | 'stripe';
    };
}
/**
 * Enhanced Validation Engine Class
 */
export declare class EnhancedAtomicDesignValidationEngine extends AtomicDesignValidationEngine {
    /**
     * Get enhanced organism matches with bias assistance analysis
     */
    static getEnhancedOrganismMatches(molecules: Array<{
        atom: string;
        type: MoleculeType;
        expression: string;
    }>): EnhancedOrganismMatch[];
    /**
     * Generate recommended next molecules based on organism requirements
     */
    private static generateRecommendedMolecules;
    /**
     * Generate enhanced smart suggestions with F1 theme integration
     */
    static generateEnhancedSmartSuggestions(molecules: Array<{
        atom: string;
        type: MoleculeType;
        expression: string;
    }>, modelTemplate?: ModelTemplate): EnhancedSmartSuggestion[];
    /**
     * Calculate suggestion priority based on context
     */
    private static calculateSuggestionPriority;
    /**
     * Calculate suggestion confidence score
     */
    private static calculateSuggestionConfidence;
    /**
     * Check if suggestion has bias support
     */
    private static checkBiasSupport;
    /**
     * Get F1 theme styling for suggestion
     */
    private static getF1ThemeForSuggestion;
}
/**
 * Export enhanced validation functions
 */
export declare const getEnhancedOrganismMatches: typeof EnhancedAtomicDesignValidationEngine.getEnhancedOrganismMatches;
export declare const generateEnhancedSmartSuggestions: typeof EnhancedAtomicDesignValidationEngine.generateEnhancedSmartSuggestions;
//# sourceMappingURL=enhancedValidationEngine.d.ts.map