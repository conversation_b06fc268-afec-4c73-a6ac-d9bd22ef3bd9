/**
 * Smart Validation Engine for Atomic Design Trading System
 *
 * This file implements intelligent validation that prevents invalid molecule combinations
 * and guides users toward valid setups, making the atomic design system bulletproof.
 */
import { TRADING_MOLECULES, TRADING_ORGANISMS } from './index';
/**
 * Validation Engine Class
 */
export class AtomicDesignValidationEngine {
    /**
     * Validate molecule selection for model eligibility
     */
    static validateMoleculeForModel(atom, type, expression, modelTemplate) {
        const molecule = TRADING_MOLECULES.find(m => m.atom === atom && m.type === type && m.expression === expression);
        if (!molecule) {
            return {
                isValid: false,
                level: 'error',
                message: `Invalid molecule combination: ${atom} → ${type} → ${expression}`,
                blockedReasons: ['Molecule combination does not exist in library'],
            };
        }
        // Check model eligibility
        if (!molecule.modelEligibility[modelTemplate]) {
            const suggestions = this.getModelAlternatives(atom, type, expression, modelTemplate);
            return {
                isValid: false,
                level: 'error',
                message: `❌ ${atom} ${type} molecules cannot be used in ${modelTemplate} setups`,
                suggestions,
                blockedReasons: [`${atom} is not eligible for ${modelTemplate} model`],
            };
        }
        return {
            isValid: true,
            level: 'success',
            message: `✅ Valid ${modelTemplate} molecule: ${atom} → ${type} → ${expression}`,
        };
    }
    /**
     * Get alternative molecules when current selection is blocked
     */
    static getModelAlternatives(atom, type, expression, modelTemplate) {
        const alternatives = [];
        // Find alternative atoms for the same type and expression
        const alternativeAtoms = TRADING_MOLECULES.filter(m => m.type === type &&
            m.expression === expression &&
            m.modelEligibility[modelTemplate] &&
            m.atom !== atom).map(m => m.atom);
        if (alternativeAtoms.length > 0) {
            alternatives.push(`Try these ${type} atoms instead: ${alternativeAtoms.join(', ')}`);
        }
        // Suggest model change if no alternatives
        if (alternatives.length === 0) {
            const otherModel = modelTemplate === 'FVG-RD' ? 'RD-Cont' : 'FVG-RD';
            alternatives.push(`Consider switching to ${otherModel} model for this molecule`);
        }
        return alternatives;
    }
    /**
     * Validate complete molecule selection
     */
    static validateMoleculeSelection(molecules, modelTemplate) {
        if (molecules.length === 0) {
            return {
                isValid: true,
                level: 'info',
                message: '💡 Start by selecting your first molecule above',
            };
        }
        // Check for model conflicts if model is specified
        if (modelTemplate) {
            for (const mol of molecules) {
                const validation = this.validateMoleculeForModel(mol.atom, mol.type, mol.expression, modelTemplate);
                if (!validation.isValid) {
                    return validation;
                }
            }
        }
        // Check for duplicate molecules
        const duplicates = this.findDuplicateMolecules(molecules);
        if (duplicates.length > 0) {
            return {
                isValid: false,
                level: 'warning',
                message: `⚠️ Duplicate molecules detected: ${duplicates.join(', ')}`,
                suggestions: ['Remove duplicate molecules to continue'],
            };
        }
        // Check molecule type balance
        const typeBalance = this.analyzeMoleculeTypeBalance(molecules);
        if (typeBalance.issues.length > 0) {
            return {
                isValid: true,
                level: 'warning',
                message: `⚠️ Molecule balance: ${typeBalance.issues.join(', ')}`,
                suggestions: typeBalance.suggestions,
            };
        }
        return {
            isValid: true,
            level: 'success',
            message: `✅ Valid combination - ${molecules.length} molecules selected`,
        };
    }
    /**
     * Find duplicate molecules in selection
     */
    static findDuplicateMolecules(molecules) {
        const seen = new Set();
        const duplicates = [];
        for (const mol of molecules) {
            const key = `${mol.atom}-${mol.type}-${mol.expression}`;
            if (seen.has(key)) {
                duplicates.push(`${mol.atom} ${mol.type}`);
            }
            seen.add(key);
        }
        return duplicates;
    }
    /**
     * Analyze molecule type balance
     */
    static analyzeMoleculeTypeBalance(molecules) {
        const typeCounts = {
            state: molecules.filter(m => m.type === 'state').length,
            behavior: molecules.filter(m => m.type === 'behavior').length,
            target: molecules.filter(m => m.type === 'target').length,
        };
        const issues = [];
        const suggestions = [];
        // Check for missing target
        if (typeCounts.target === 0 && molecules.length >= 2) {
            issues.push('No target molecule');
            suggestions.push('Add a target molecule to define your objective');
        }
        // Check for too many of one type
        if (typeCounts.state > 2) {
            issues.push('Too many state molecules');
            suggestions.push('Consider reducing state molecules for cleaner setup');
        }
        if (typeCounts.behavior > 2) {
            issues.push('Too many behavior molecules');
            suggestions.push('Focus on 1-2 key behavior molecules');
        }
        return { issues, suggestions };
    }
    /**
     * Get real-time organism matches with completeness
     */
    static getOrganismMatches(molecules) {
        const matches = [];
        for (const organism of TRADING_ORGANISMS) {
            const requiredMolecules = organism.molecules;
            let matchedCount = 0;
            const missingMolecules = [];
            // Check how many required molecules are matched
            for (const required of requiredMolecules) {
                const isMatched = molecules.some(selected => selected.atom === required.atom &&
                    selected.type === required.type &&
                    selected.expression === required.expression);
                if (isMatched) {
                    matchedCount++;
                }
                else {
                    missingMolecules.push(required);
                }
            }
            const completeness = Math.round((matchedCount / requiredMolecules.length) * 100);
            // Determine status
            let status;
            if (completeness === 100 && molecules.length === requiredMolecules.length) {
                status = 'complete';
            }
            else if (completeness > 0) {
                status = 'partial';
            }
            else {
                status = 'possible';
            }
            // Only include organisms with some level of match or potential
            if (completeness > 0 || molecules.length === 0) {
                matches.push({
                    organism,
                    completeness,
                    missingMolecules,
                    status,
                });
            }
        }
        // Sort by completeness (highest first)
        return matches.sort((a, b) => b.completeness - a.completeness);
    }
    /**
     * Generate smart suggestions based on current selection
     */
    static generateSmartSuggestions(molecules, modelTemplate) {
        const suggestions = [];
        const matches = this.getOrganismMatches(molecules);
        // If no molecules selected, suggest starting points
        if (molecules.length === 0) {
            suggestions.push({
                type: 'completion',
                message: '🎯 Start with "Market Structure" state molecule for trend context',
                action: {
                    atom: 'Market Structure',
                    type: 'state',
                    expression: 'Bullish',
                },
            });
            suggestions.push({
                type: 'completion',
                message: '💡 Or start with "FVG" behavior molecule for gap-based setups',
                action: {
                    atom: 'FVG',
                    type: 'behavior',
                    expression: 'Bullish FVG',
                },
            });
            return suggestions;
        }
        // Check for partial matches and suggest completion
        const bestPartialMatch = matches.find(m => m.status === 'partial' && m.completeness >= 50);
        if (bestPartialMatch && bestPartialMatch.missingMolecules.length > 0) {
            const missing = bestPartialMatch.missingMolecules[0];
            suggestions.push({
                type: 'completion',
                message: `🎯 ${bestPartialMatch.completeness}% complete: Add ${missing.atom} ${missing.type} to complete "${bestPartialMatch.organism.name}"`,
                action: {
                    atom: missing.atom,
                    type: missing.type,
                    expression: missing.expression,
                },
            });
        }
        // If only one molecule selected, suggest complementary molecules
        if (molecules.length === 1) {
            const currentMolecule = molecules[0];
            // If they started with a state molecule, suggest a behavior molecule
            if (currentMolecule.type === 'state') {
                suggestions.push({
                    type: 'completion',
                    message: '🏎️ Add a behavior molecule to define your entry pattern',
                    action: {
                        atom: 'Order Block',
                        type: 'behavior',
                        expression: 'Bullish OB',
                    },
                });
            }
            // If they started with a behavior molecule, suggest a target molecule
            if (currentMolecule.type === 'behavior') {
                suggestions.push({
                    type: 'completion',
                    message: '🎯 Add a target molecule to define your profit objective',
                    action: {
                        atom: 'Liquidity',
                        type: 'target',
                        expression: 'BSL',
                    },
                });
            }
        }
        // Suggest model template if not set
        if (!modelTemplate && molecules.length >= 2) {
            const suggestedModel = this.suggestModelTemplate(molecules);
            if (suggestedModel) {
                suggestions.push({
                    type: 'model-change',
                    message: `🏎️ Based on your molecules, ${suggestedModel} model is recommended`,
                    action: { modelTemplate: suggestedModel },
                });
            }
        }
        return suggestions;
    }
    /**
     * Suggest model template based on molecule selection
     */
    static suggestModelTemplate(molecules) {
        // Check if any molecules are RD-Cont only
        const hasRDContOnly = molecules.some(mol => {
            const molecule = TRADING_MOLECULES.find(m => m.atom === mol.atom && m.type === mol.type && m.expression === mol.expression);
            return (molecule && !molecule.modelEligibility['FVG-RD'] && molecule.modelEligibility['RD-Cont']);
        });
        if (hasRDContOnly) {
            return 'RD-Cont';
        }
        // Check for FVG-focused molecules
        const hasFVGMolecules = molecules.some(mol => mol.atom.includes('FVG'));
        if (hasFVGMolecules) {
            return 'FVG-RD';
        }
        return null;
    }
}
/**
 * Export validation functions for easy use
 */
export const validateMoleculeForModel = AtomicDesignValidationEngine.validateMoleculeForModel;
export const validateMoleculeSelection = AtomicDesignValidationEngine.validateMoleculeSelection;
export const getOrganismMatches = AtomicDesignValidationEngine.getOrganismMatches;
export const generateSmartSuggestions = AtomicDesignValidationEngine.generateSmartSuggestions;
//# sourceMappingURL=validationEngine.js.map