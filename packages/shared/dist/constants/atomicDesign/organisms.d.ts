import { TradingOrganism, ModelTemplate } from '../../types/trading';
/**
 * Complete Organisms Library (7 Templates)
 */
export declare const TRADING_ORGANISMS: TradingOrganism[];
/**
 * Get organism by ID
 */
export declare const getOrganismById: (id: string) => TradingOrganism | undefined;
/**
 * Get organisms by model template
 */
export declare const getOrganismsByModel: (model: ModelTemplate) => TradingOrganism[];
/**
 * Get organisms by confidence rating
 */
export declare const getOrganismsByConfidence: (confidence: "high" | "medium" | "low") => TradingOrganism[];
/**
 * Match molecules to organisms
 * Returns organisms that match the provided molecules with confidence level
 */
export declare const matchMoleculesToOrganisms: (selectedMolecules: Array<{
    atom: string;
    type: string;
    expression: string;
}>) => Array<{
    organism: TradingOrganism;
    confidence: "exact" | "partial" | "none";
}>;
/**
 * Get suggested setup name based on molecule selection
 */
export declare const getSuggestedSetupName: (selectedMolecules: Array<{
    atom: string;
    type: string;
    expression: string;
}>) => {
    name: string;
    confidence: "exact" | "partial" | "none";
    organism?: TradingOrganism;
};
//# sourceMappingURL=organisms.d.ts.map