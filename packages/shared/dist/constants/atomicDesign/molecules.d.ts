import { TradingMolecule, MoleculeType } from '../../types/trading';
/**
 * Complete Molecules Library (20 entries)
 */
export declare const TRADING_MOLECULES: TradingMolecule[];
/**
 * Get molecules by type
 */
export declare const getMoleculesByType: (type: MoleculeType) => TradingMolecule[];
/**
 * Get molecules by atom
 */
export declare const getMoleculesByAtom: (atom: string) => TradingMolecule[];
/**
 * Get molecule by ID
 */
export declare const getMoleculeById: (id: string) => TradingMolecule | undefined;
/**
 * Get molecules eligible for model
 */
export declare const getMoleculesForModel: (model: "FVG-RD" | "RD-Cont") => TradingMolecule[];
/**
 * Get expressions for atom and type combination
 */
export declare const getExpressionsForAtomType: (atom: string, type: MoleculeType) => string[];
/**
 * Validate molecule combination for model eligibility
 */
export declare const validateMoleculeForModel: (atom: string, type: MoleculeType, expression: string, model: "FVG-RD" | "RD-Cont") => boolean;
//# sourceMappingURL=molecules.d.ts.map