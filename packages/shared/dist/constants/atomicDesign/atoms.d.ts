import { TradingAtom, AtomCategory } from '../../types/trading';
/**
 * Complete Atoms Library
 */
export declare const TRADING_ATOMS: TradingAtom[];
/**
 * Get atoms by category
 */
export declare const getAtomsByCategory: (category: AtomCategory) => TradingAtom[];
/**
 * Get atom by ID
 */
export declare const getAtomById: (id: string) => TradingAtom | undefined;
/**
 * Get atoms eligible for model
 */
export declare const getAtomsForModel: (model: "FVG-RD" | "RD-Cont") => TradingAtom[];
//# sourceMappingURL=atoms.d.ts.map