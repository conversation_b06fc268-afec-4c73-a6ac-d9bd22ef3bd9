"use strict";var Ro=Object.defineProperty;var jo=(e,t,r)=>t in e?Ro(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var be=(e,t,r)=>(jo(e,typeof t!="symbol"?t+"":t,r),r);Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const x=require("react"),s=require("styled-components"),ko=require("react-dom");var Cr=(e=>(e.LONG="LONG",e.SHORT="SHORT",e))(Cr||{}),Tr=(e=>(e.OPEN="OPEN",e.CLOSED="CLOSED",e.CANCELED="CANCELED",e.REJECTED="REJECTED",e.PENDING="PENDING",e))(Tr||{}),Er=(e=>(e.MARKET="MARKET",e.LIMIT="LIMIT",e.STOP="STOP",e.STOP_LIMIT="STOP_LIMIT",e))(Er||{}),Ir=(e=>(e.BUY="BUY",e.SELL="SELL",e))(Ir||{}),Rr=(e=>(e.PENDING="PENDING",e.FILLED="FILLED",e.PARTIALLY_FILLED="PARTIALLY_FILLED",e.CANCELED="CANCELED",e.REJECTED="REJECTED",e))(Rr||{}),jr=(e=>(e.GTC="GTC",e.IOC="IOC",e.FOK="FOK",e.DAY="DAY",e))(jr||{});const vt={completeTradeToLegacy:e=>{var r;const t=e.trade;return{id:((r=t.id)==null?void 0:r.toString())||"0",symbol:t.market||"MNQ",date:t.date,direction:t.direction,size:t.no_of_contracts||1,entry:t.entry_price||0,exit:t.exit_price||0,stopLoss:0,takeProfit:0,profitLoss:t.achieved_pl||0,strategy:t.setup||"",notes:t.notes||"",tags:[],images:[]}},legacyToCompleteTrade:e=>({trade:{id:parseInt(e.id)||void 0,date:e.date,model_type:"Unknown",direction:e.direction,market:e.symbol,entry_price:e.entry,exit_price:e.exit,achieved_pl:e.profitLoss,no_of_contracts:e.size,setup:e.strategy,notes:e.notes,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}}),completeTradeArrayToLegacy:e=>e.map(vt.completeTradeToLegacy),legacyArrayToCompleteTrade:e=>e.map(vt.legacyToCompleteTrade)};var U=(e=>(e.LONDON="london",e.NEW_YORK_AM="new-york-am",e.NEW_YORK_PM="new-york-pm",e.ASIA="asia",e.PRE_MARKET="pre-market",e.AFTER_HOURS="after-hours",e.OVERNIGHT="overnight",e))(U||{}),_=(e=>(e.MORNING_BREAKOUT="morning-breakout",e.MID_MORNING_REVERSION="mid-morning-reversion",e.PRE_LUNCH="pre-lunch",e.LUNCH_MACRO_EXTENDED="lunch-macro-extended",e.LUNCH_MACRO="lunch-macro",e.POST_LUNCH="post-lunch",e.PRE_CLOSE="pre-close",e.POWER_HOUR="power-hour",e.MOC="moc",e.LONDON_OPEN="london-open",e.LONDON_NY_OVERLAP="london-ny-overlap",e.CUSTOM="custom",e))(_||{});const No=[{id:"mnor-fpfvg",name:"MNOR-FPFVG",category:"session-fvg",description:"Midnight New York Opening Range First Presented Fair Value Gap",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"am-fpfvg",name:"AM-FPFVG",category:"session-fvg",description:"Asian Morning First Presented Fair Value Gap",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"pm-fpfvg",name:"PM-FPFVG",category:"session-fvg",description:"Post Market First Presented Fair Value Gap",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"asia-fpfvg",name:"Asia-FPFVG",category:"session-fvg",description:"Asian Session First Presented Fair Value Gap",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"premarket-fpfvg",name:"Premarket-FPFVG",category:"session-fvg",description:"Premarket Session First Presented Fair Value Gap",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}}],Do=[{id:"nwog",name:"NWOG",category:"htf-pd-array",description:"New Week Opening Gap",modelEligibility:{"FVG-RD":!1,"RD-Cont":!0}},{id:"old-nwog",name:"Old-NWOG",category:"htf-pd-array",description:"Previous Week Opening Gap",modelEligibility:{"FVG-RD":!1,"RD-Cont":!0}},{id:"ndog",name:"NDOG",category:"htf-pd-array",description:"New Day Opening Gap",modelEligibility:{"FVG-RD":!1,"RD-Cont":!0}},{id:"old-ndog",name:"Old-NDOG",category:"htf-pd-array",description:"Previous Day Opening Gap",modelEligibility:{"FVG-RD":!1,"RD-Cont":!0}},{id:"daily-top-bottom-fvg",name:"Daily-Top/Bottom-FVG",category:"htf-pd-array",description:"Daily Timeframe Top or Bottom Fair Value Gap",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}}],Lo=[{id:"strong-fvg",name:"Strong-FVG",category:"strength-fvg",description:"Strong Fair Value Gap with significant momentum",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"rdrb-fvg",name:"RDRB-FVG",category:"strength-fvg",description:"Redelivery Rejection Block Fair Value Gap",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}}],Mo=[{id:"0930-or-h",name:"09:30-OR-H",category:"liquidity-pool-time",description:"9:30 Opening Range High",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"0930-or-l",name:"09:30-OR-L",category:"liquidity-pool-time",description:"9:30 Opening Range Low",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"london-h",name:"London-H",category:"liquidity-pool-time",description:"London Session High",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"london-l",name:"London-L",category:"liquidity-pool-time",description:"London Session Low",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"premarket-h",name:"Premarket-H",category:"liquidity-pool-time",description:"Premarket Session High",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"premarket-l",name:"Premarket-L",category:"liquidity-pool-time",description:"Premarket Session Low",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"lunch-h",name:"Lunch-H",category:"liquidity-pool-time",description:"Lunch Session High",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"lunch-l",name:"Lunch-L",category:"liquidity-pool-time",description:"Lunch Session Low",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}}],_o=[{id:"monthly-h",name:"Monthly-H",category:"liquidity-pool-htf",description:"Monthly High",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"monthly-l",name:"Monthly-L",category:"liquidity-pool-htf",description:"Monthly Low",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"weekly-h",name:"Weekly-H",category:"liquidity-pool-htf",description:"Weekly High",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"weekly-l",name:"Weekly-L",category:"liquidity-pool-htf",description:"Weekly Low",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"daily-h",name:"Daily-H",category:"liquidity-pool-htf",description:"Daily High",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"daily-l",name:"Daily-L",category:"liquidity-pool-htf",description:"Daily Low",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}}],Po=[{id:"prev-day-h",name:"Prev-Day-H",category:"historical-pd-array",description:"Previous Day High",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"prev-day-l",name:"Prev-Day-L",category:"historical-pd-array",description:"Previous Day Low",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"3day-h",name:"3Day-H",category:"historical-pd-array",description:"3-Day High",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"3day-l",name:"3Day-L",category:"historical-pd-array",description:"3-Day Low",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"prev-week-h",name:"Prev-Week-H",category:"historical-pd-array",description:"Previous Week High",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"prev-week-l",name:"Prev-Week-L",category:"historical-pd-array",description:"Previous Week Low",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}}],st=[...No,...Do,...Lo,...Mo,..._o,...Po],Fo=e=>st.filter(t=>t.category===e),Oo=e=>st.find(t=>t.id===e),$o=e=>st.filter(t=>t.modelEligibility[e]),Ao=[{id:"mol1",atom:"NDOG",type:"state",expression:"balanced_no_wick",description:"NDOG balanced without wick revisit",modelEligibility:{"FVG-RD":!1,"RD-Cont":!0}},{id:"mol2",atom:"MNOR-FPFVG",type:"state",expression:"balanced_no_redelivery",description:"MNOR-FPFVG balanced without redelivery",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"mol3",atom:"Strong-FVG",type:"state",expression:"untouched",description:"Strong-FVG hasn't been interacted with",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"mol4",atom:"NWOG",type:"state",expression:"unengaged",description:"NWOG price hasn't reached the structure",modelEligibility:{"FVG-RD":!1,"RD-Cont":!0}},{id:"mol5",atom:"London-H/L",type:"state",expression:"untaken",description:"London High/Low liquidity level remains intact",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"mol6",atom:"Daily-Top/Bottom-FVG",type:"state",expression:"balanced",description:"Daily Top/Bottom FVG has been balanced/filled",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"mol7",atom:"RDRB-FVG",type:"state",expression:"balanced_no_wick",description:"RDRB-FVG balanced without wick revisit",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}}],zo=[{id:"mol8",atom:"London-H/L",type:"behavior",expression:"swept",description:"London High/Low liquidity taken or structure cleared",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"mol9",atom:"Strong-FVG",type:"behavior",expression:"formed",description:"Strong-FVG new structure created",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"mol10",atom:"RDRB-FVG",type:"behavior",expression:"tapped",description:"RDRB-FVG structure touched/tested",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"mol11",atom:"Premarket-H/L",type:"behavior",expression:"swept",description:"Premarket High/Low liquidity taken",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"mol12",atom:"09:30-OR-H/L",type:"behavior",expression:"swept",description:"9:30 Opening Range High/Low swept",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"mol13",atom:"NWOG",type:"behavior",expression:"reclaimed",description:"NWOG structure regained after loss",modelEligibility:{"FVG-RD":!1,"RD-Cont":!0}},{id:"mol14",atom:"Lunch-H/L",type:"behavior",expression:"swept",description:"Lunch High/Low liquidity taken",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}}],Vo=[{id:"mol15",atom:"London-H/L",type:"target",expression:"draw_on_liquidity",description:"London High/Low targeted for liquidity",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"mol16",atom:"MNOR-FPFVG",type:"target",expression:"expected_redelivery",description:"MNOR-FPFVG anticipated for redelivery",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"mol17",atom:"NDOG",type:"target",expression:"draw_on_liquidity",description:"NDOG targeted for liquidity draw",modelEligibility:{"FVG-RD":!1,"RD-Cont":!0}},{id:"mol18",atom:"Premarket-H/L",type:"target",expression:"return_point",description:"Premarket High/Low expected for price return",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"mol19",atom:"Daily-H/L",type:"target",expression:"draw_on_liquidity",description:"Daily High/Low targeted for liquidity",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}},{id:"mol20",atom:"Weekly-H/L",type:"target",expression:"draw_on_liquidity",description:"Weekly High/Low targeted for liquidity",modelEligibility:{"FVG-RD":!0,"RD-Cont":!0}}],ue=[...Ao,...zo,...Vo],Go=e=>ue.filter(t=>t.type===e),Bo=e=>ue.filter(t=>t.atom===e),Ho=e=>ue.find(t=>t.id===e),qo=e=>ue.filter(t=>t.modelEligibility[e]),Uo=(e,t)=>ue.filter(r=>r.atom===e&&r.type===t).map(r=>r.expression),Yo=(e,t,r,o)=>{const i=ue.find(c=>c.atom===e&&c.type===t&&c.expression===r);return i?i.modelEligibility[o]:!1},Re=[{id:"org001",name:"NDOG Reaction Setup Targeting London Liquidity",modelTemplate:"RD-Cont",modelAlias:"Redelivery Continuation",molecules:[{atom:"NDOG",type:"state",expression:"balanced_no_wick"},{atom:"Strong-FVG",type:"behavior",expression:"formed"},{atom:"London-H/L",type:"target",expression:"draw_on_liquidity"}],biasAssistance:[{atom:"15m Top-FVG",timeframe:"15m",expression:"balanced_no_redelivery",influence:"strengthens setup confidence"}],confidenceRating:"high",validationStatus:"untested",triggerCondition:"All molecules must be active within the same session window"},{id:"org002",name:"Premarket Sweep Continuation Setup Targeting Daily High",modelTemplate:"RD-Cont",modelAlias:"Redelivery Continuation",molecules:[{atom:"Premarket-H/L",type:"behavior",expression:"swept"},{atom:"Strong-FVG",type:"behavior",expression:"formed"},{atom:"Daily-H/L",type:"target",expression:"draw_on_liquidity"}],biasAssistance:[{atom:"Daily-Top/Bottom-FVG",expression:"balanced",influence:"provides directional bias"}],confidenceRating:"medium",validationStatus:"untested",triggerCondition:"Strong-FVG must form within 15m of sweep"},{id:"org003",name:"MNOR Redelivery Reversal Setup Targeting Premarket High",modelTemplate:"FVG-RD",modelAlias:"Fair Value Gap Redelivery",molecules:[{atom:"MNOR-FPFVG",type:"state",expression:"balanced_no_redelivery"},{atom:"MNOR-FPFVG",type:"target",expression:"expected_redelivery"},{atom:"Premarket-H/L",type:"target",expression:"return_point"}],biasAssistance:[{atom:"Daily-Top/Bottom-FVG",expression:"balanced",influence:"confirms directional bias"}],confidenceRating:"high",validationStatus:"untested",triggerCondition:"MNOR must balance without wick, followed by a strong FVG"},{id:"org004",name:"Lunch Sweep Reversal Setup Targeting NDOG",modelTemplate:"RD-Cont",modelAlias:"Redelivery Continuation",molecules:[{atom:"Lunch-H/L",type:"behavior",expression:"swept"},{atom:"Strong-FVG",type:"behavior",expression:"formed"},{atom:"NDOG",type:"target",expression:"draw_on_liquidity"}],biasAssistance:[{atom:"Daily-Top/Bottom-FVG",expression:"balanced",influence:"provides directional context"}],confidenceRating:"medium",validationStatus:"untested",triggerCondition:"Reversal after lunch sweep confirmed by FVG"},{id:"org005",name:"Premarket Reclaim Setup Targeting London High",modelTemplate:"RD-Cont",modelAlias:"Redelivery Continuation",molecules:[{atom:"NWOG",type:"behavior",expression:"reclaimed"},{atom:"Strong-FVG",type:"behavior",expression:"formed"},{atom:"London-H/L",type:"target",expression:"draw_on_liquidity"}],biasAssistance:[{atom:"Daily-Top/Bottom-FVG",expression:"balanced",influence:"supports reclaim direction"}],confidenceRating:"medium",validationStatus:"untested",triggerCondition:"Return above NWOG after premarket sweep"},{id:"org006",name:"09:30 Sweep Redelivery Setup Targeting Premarket High",modelTemplate:"FVG-RD",modelAlias:"Fair Value Gap Redelivery",molecules:[{atom:"09:30-OR-H/L",type:"behavior",expression:"swept"},{atom:"Strong-FVG",type:"behavior",expression:"formed"},{atom:"Premarket-H/L",type:"target",expression:"return_point"}],biasAssistance:[{atom:"MNOR-FPFVG",expression:"balanced_no_wick",influence:"provides context state"}],confidenceRating:"high",validationStatus:"untested",triggerCondition:"Strong-FVG forms off OR sweep after MNOR context"},{id:"org007",name:"RDRB-FVG Continuation Setup Targeting Weekly High",modelTemplate:"RD-Cont",modelAlias:"Redelivery Continuation",molecules:[{atom:"RDRB-FVG",type:"state",expression:"balanced_no_wick"},{atom:"RDRB-FVG",type:"behavior",expression:"tapped"},{atom:"Weekly-H/L",type:"target",expression:"draw_on_liquidity"}],biasAssistance:[{atom:"NWOG",expression:"unengaged",influence:"supports higher timeframe target"}],confidenceRating:"high",validationStatus:"untested",triggerCondition:"Entry off RDRB-FVG + tap, aiming for HTF liquidity"}],Wo=e=>Re.find(t=>t.id===e),Ko=e=>Re.filter(t=>t.modelTemplate===e),Qo=e=>Re.filter(t=>t.confidenceRating===e),kr=e=>{const t=[];for(const r of Re){const o=r.molecules;let i=0;for(const a of o)e.some(d=>d.atom===a.atom&&d.type===a.type&&d.expression===a.expression)&&i++;let c;i===o.length&&e.length===o.length?c="exact":i>0?c="partial":c="none",c!=="none"&&t.push({organism:r,confidence:c})}return t.sort((r,o)=>r.confidence==="exact"&&o.confidence!=="exact"?-1:r.confidence!=="exact"&&o.confidence==="exact"?1:0)},Xo=e=>{const t=kr(e);if(t.length===0)return{name:"Custom Setup",confidence:"none"};const r=t[0];return{name:r.organism.name,confidence:r.confidence,organism:r.organism}};class Oe{static validateMoleculeForModel(t,r,o,i){const c=ue.find(a=>a.atom===t&&a.type===r&&a.expression===o);if(!c)return{isValid:!1,level:"error",message:`Invalid molecule combination: ${t} → ${r} → ${o}`,blockedReasons:["Molecule combination does not exist in library"]};if(!c.modelEligibility[i]){const a=this.getModelAlternatives(t,r,o,i);return{isValid:!1,level:"error",message:`❌ ${t} ${r} molecules cannot be used in ${i} setups`,suggestions:a,blockedReasons:[`${t} is not eligible for ${i} model`]}}return{isValid:!0,level:"success",message:`✅ Valid ${i} molecule: ${t} → ${r} → ${o}`}}static getModelAlternatives(t,r,o,i){const c=[],a=ue.filter(l=>l.type===r&&l.expression===o&&l.modelEligibility[i]&&l.atom!==t).map(l=>l.atom);if(a.length>0&&c.push(`Try these ${r} atoms instead: ${a.join(", ")}`),c.length===0){const l=i==="FVG-RD"?"RD-Cont":"FVG-RD";c.push(`Consider switching to ${l} model for this molecule`)}return c}static validateMoleculeSelection(t,r){if(t.length===0)return{isValid:!0,level:"info",message:"💡 Start by selecting your first molecule above"};if(r)for(const c of t){const a=this.validateMoleculeForModel(c.atom,c.type,c.expression,r);if(!a.isValid)return a}const o=this.findDuplicateMolecules(t);if(o.length>0)return{isValid:!1,level:"warning",message:`⚠️ Duplicate molecules detected: ${o.join(", ")}`,suggestions:["Remove duplicate molecules to continue"]};const i=this.analyzeMoleculeTypeBalance(t);return i.issues.length>0?{isValid:!0,level:"warning",message:`⚠️ Molecule balance: ${i.issues.join(", ")}`,suggestions:i.suggestions}:{isValid:!0,level:"success",message:`✅ Valid combination - ${t.length} molecules selected`}}static findDuplicateMolecules(t){const r=new Set,o=[];for(const i of t){const c=`${i.atom}-${i.type}-${i.expression}`;r.has(c)&&o.push(`${i.atom} ${i.type}`),r.add(c)}return o}static analyzeMoleculeTypeBalance(t){const r={state:t.filter(c=>c.type==="state").length,behavior:t.filter(c=>c.type==="behavior").length,target:t.filter(c=>c.type==="target").length},o=[],i=[];return r.target===0&&t.length>=2&&(o.push("No target molecule"),i.push("Add a target molecule to define your objective")),r.state>2&&(o.push("Too many state molecules"),i.push("Consider reducing state molecules for cleaner setup")),r.behavior>2&&(o.push("Too many behavior molecules"),i.push("Focus on 1-2 key behavior molecules")),{issues:o,suggestions:i}}static getOrganismMatches(t){const r=[];for(const o of Re){const i=o.molecules;let c=0;const a=[];for(const f of i)t.some(m=>m.atom===f.atom&&m.type===f.type&&m.expression===f.expression)?c++:a.push(f);const l=Math.round(c/i.length*100);let d;l===100&&t.length===i.length?d="complete":l>0?d="partial":d="possible",(l>0||t.length===0)&&r.push({organism:o,completeness:l,missingMolecules:a,status:d})}return r.sort((o,i)=>i.completeness-o.completeness)}static generateSmartSuggestions(t,r){const o=[],i=this.getOrganismMatches(t);if(t.length===0)return o.push({type:"completion",message:'🎯 Start with "Market Structure" state molecule for trend context',action:{atom:"Market Structure",type:"state",expression:"Bullish"}}),o.push({type:"completion",message:'💡 Or start with "FVG" behavior molecule for gap-based setups',action:{atom:"FVG",type:"behavior",expression:"Bullish FVG"}}),o;const c=i.find(a=>a.status==="partial"&&a.completeness>=50);if(c&&c.missingMolecules.length>0){const a=c.missingMolecules[0];o.push({type:"completion",message:`🎯 ${c.completeness}% complete: Add ${a.atom} ${a.type} to complete "${c.organism.name}"`,action:{atom:a.atom,type:a.type,expression:a.expression}})}if(t.length===1){const a=t[0];a.type==="state"&&o.push({type:"completion",message:"🏎️ Add a behavior molecule to define your entry pattern",action:{atom:"Order Block",type:"behavior",expression:"Bullish OB"}}),a.type==="behavior"&&o.push({type:"completion",message:"🎯 Add a target molecule to define your profit objective",action:{atom:"Liquidity",type:"target",expression:"BSL"}})}if(!r&&t.length>=2){const a=this.suggestModelTemplate(t);a&&o.push({type:"model-change",message:`🏎️ Based on your molecules, ${a} model is recommended`,action:{modelTemplate:a}})}return o}static suggestModelTemplate(t){return t.some(i=>{const c=ue.find(a=>a.atom===i.atom&&a.type===i.type&&a.expression===i.expression);return c&&!c.modelEligibility["FVG-RD"]&&c.modelEligibility["RD-Cont"]})?"RD-Cont":t.some(i=>i.atom.includes("FVG"))?"FVG-RD":null}}const Jo=Oe.validateMoleculeForModel,Zo=Oe.validateMoleculeSelection,en=Oe.getOrganismMatches,tn=Oe.generateSmartSuggestions,we={constant:{parentArrays:["NWOG","Old-NWOG","NDOG","Old-NDOG","Monthly-FVG","Weekly-FVG","Daily-FVG","15min-Top/Bottom-FVG","1h-Top/Bottom-FVG"],fvgTypes:["Strong-FVG","AM-FPFVG","PM-FPFVG","Asia-FPFVG","Premarket-FPFVG","MNOR-FVG","Macro-FVG","News-FVG","Top/Bottom-FVG"]},action:{liquidityEvents:["None","London-H/L","Premarket-H/L","09:30-Opening-Range-H/L","Lunch-H/L","Prev-Day-H/L","Prev-Week-H/L","Monthly-H/L","Macro-H/L"]},variable:{rdTypes:["None","True-RD","IMM-RD","Dispersed-RD","Wide-Gap-RD"]},entry:{methods:["Simple-Entry","Complex-Entry","Complex-Entry/Mini"]}},rn=["RD-Cont","FVG-RD","Combined"];var n={},on={get exports(){return n},set exports(e){n=e}},ke={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qt;function nn(){if(Qt)return ke;Qt=1;var e=x,t=Symbol.for("react.element"),r=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,i=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function a(l,d,f){var u,m={},y=null,h=null;f!==void 0&&(y=""+f),d.key!==void 0&&(y=""+d.key),d.ref!==void 0&&(h=d.ref);for(u in d)o.call(d,u)&&!c.hasOwnProperty(u)&&(m[u]=d[u]);if(l&&l.defaultProps)for(u in d=l.defaultProps,d)m[u]===void 0&&(m[u]=d[u]);return{$$typeof:t,type:l,key:y,ref:h,props:m,_owner:i.current}}return ke.Fragment=r,ke.jsx=a,ke.jsxs=a,ke}var Ne={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xt;function sn(){return Xt||(Xt=1,process.env.NODE_ENV!=="production"&&function(){var e=x,t=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),l=Symbol.for("react.context"),d=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),u=Symbol.for("react.suspense_list"),m=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),h=Symbol.for("react.offscreen"),g=Symbol.iterator,b="@@iterator";function T(p){if(p===null||typeof p!="object")return null;var S=g&&p[g]||p[b];return typeof S=="function"?S:null}var w=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function C(p){{for(var S=arguments.length,E=new Array(S>1?S-1:0),P=1;P<S;P++)E[P-1]=arguments[P];L("error",p,E)}}function L(p,S,E){{var P=w.ReactDebugCurrentFrame,z=P.getStackAddendum();z!==""&&(S+="%s",E=E.concat([z]));var B=E.map(function(A){return String(A)});B.unshift("Warning: "+S),Function.prototype.apply.call(console[p],console,B)}}var $=!1,j=!1,k=!1,M=!1,R=!1,N;N=Symbol.for("react.module.reference");function W(p){return!!(typeof p=="string"||typeof p=="function"||p===o||p===c||R||p===i||p===f||p===u||M||p===h||$||j||k||typeof p=="object"&&p!==null&&(p.$$typeof===y||p.$$typeof===m||p.$$typeof===a||p.$$typeof===l||p.$$typeof===d||p.$$typeof===N||p.getModuleId!==void 0))}function q(p,S,E){var P=p.displayName;if(P)return P;var z=S.displayName||S.name||"";return z!==""?E+"("+z+")":E}function D(p){return p.displayName||"Context"}function G(p){if(p==null)return null;if(typeof p.tag=="number"&&C("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof p=="function")return p.displayName||p.name||null;if(typeof p=="string")return p;switch(p){case o:return"Fragment";case r:return"Portal";case c:return"Profiler";case i:return"StrictMode";case f:return"Suspense";case u:return"SuspenseList"}if(typeof p=="object")switch(p.$$typeof){case l:var S=p;return D(S)+".Consumer";case a:var E=p;return D(E._context)+".Provider";case d:return q(p,p.render,"ForwardRef");case m:var P=p.displayName||null;return P!==null?P:G(p.type)||"Memo";case y:{var z=p,B=z._payload,A=z._init;try{return G(A(B))}catch{return null}}}return null}var J=Object.assign,Z=0,le,ee,F,K,ye,V,pe;function Pt(){}Pt.__reactDisabledLog=!0;function to(){{if(Z===0){le=console.log,ee=console.info,F=console.warn,K=console.error,ye=console.group,V=console.groupCollapsed,pe=console.groupEnd;var p={configurable:!0,enumerable:!0,value:Pt,writable:!0};Object.defineProperties(console,{info:p,log:p,warn:p,error:p,group:p,groupCollapsed:p,groupEnd:p})}Z++}}function ro(){{if(Z--,Z===0){var p={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:J({},p,{value:le}),info:J({},p,{value:ee}),warn:J({},p,{value:F}),error:J({},p,{value:K}),group:J({},p,{value:ye}),groupCollapsed:J({},p,{value:V}),groupEnd:J({},p,{value:pe})})}Z<0&&C("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var ct=w.ReactCurrentDispatcher,lt;function We(p,S,E){{if(lt===void 0)try{throw Error()}catch(z){var P=z.stack.trim().match(/\n( *(at )?)/);lt=P&&P[1]||""}return`
`+lt+p}}var dt=!1,Ke;{var oo=typeof WeakMap=="function"?WeakMap:Map;Ke=new oo}function Ft(p,S){if(!p||dt)return"";{var E=Ke.get(p);if(E!==void 0)return E}var P;dt=!0;var z=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var B;B=ct.current,ct.current=null,to();try{if(S){var A=function(){throw Error()};if(Object.defineProperty(A.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(A,[])}catch(fe){P=fe}Reflect.construct(p,[],A)}else{try{A.call()}catch(fe){P=fe}p.call(A.prototype)}}else{try{throw Error()}catch(fe){P=fe}p()}}catch(fe){if(fe&&P&&typeof fe.stack=="string"){for(var O=fe.stack.split(`
`),oe=P.stack.split(`
`),Q=O.length-1,X=oe.length-1;Q>=1&&X>=0&&O[Q]!==oe[X];)X--;for(;Q>=1&&X>=0;Q--,X--)if(O[Q]!==oe[X]){if(Q!==1||X!==1)do if(Q--,X--,X<0||O[Q]!==oe[X]){var ae=`
`+O[Q].replace(" at new "," at ");return p.displayName&&ae.includes("<anonymous>")&&(ae=ae.replace("<anonymous>",p.displayName)),typeof p=="function"&&Ke.set(p,ae),ae}while(Q>=1&&X>=0);break}}}finally{dt=!1,ct.current=B,ro(),Error.prepareStackTrace=z}var Se=p?p.displayName||p.name:"",Kt=Se?We(Se):"";return typeof p=="function"&&Ke.set(p,Kt),Kt}function no(p,S,E){return Ft(p,!1)}function so(p){var S=p.prototype;return!!(S&&S.isReactComponent)}function Qe(p,S,E){if(p==null)return"";if(typeof p=="function")return Ft(p,so(p));if(typeof p=="string")return We(p);switch(p){case f:return We("Suspense");case u:return We("SuspenseList")}if(typeof p=="object")switch(p.$$typeof){case d:return no(p.render);case m:return Qe(p.type,S,E);case y:{var P=p,z=P._payload,B=P._init;try{return Qe(B(z),S,E)}catch{}}}return""}var Xe=Object.prototype.hasOwnProperty,Ot={},$t=w.ReactDebugCurrentFrame;function Je(p){if(p){var S=p._owner,E=Qe(p.type,p._source,S?S.type:null);$t.setExtraStackFrame(E)}else $t.setExtraStackFrame(null)}function io(p,S,E,P,z){{var B=Function.call.bind(Xe);for(var A in p)if(B(p,A)){var O=void 0;try{if(typeof p[A]!="function"){var oe=Error((P||"React class")+": "+E+" type `"+A+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof p[A]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw oe.name="Invariant Violation",oe}O=p[A](S,A,P,E,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(Q){O=Q}O&&!(O instanceof Error)&&(Je(z),C("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",P||"React class",E,A,typeof O),Je(null)),O instanceof Error&&!(O.message in Ot)&&(Ot[O.message]=!0,Je(z),C("Failed %s type: %s",E,O.message),Je(null))}}}var ao=Array.isArray;function ut(p){return ao(p)}function co(p){{var S=typeof Symbol=="function"&&Symbol.toStringTag,E=S&&p[Symbol.toStringTag]||p.constructor.name||"Object";return E}}function lo(p){try{return At(p),!1}catch{return!0}}function At(p){return""+p}function zt(p){if(lo(p))return C("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",co(p)),At(p)}var je=w.ReactCurrentOwner,uo={key:!0,ref:!0,__self:!0,__source:!0},Vt,Gt,pt;pt={};function po(p){if(Xe.call(p,"ref")){var S=Object.getOwnPropertyDescriptor(p,"ref").get;if(S&&S.isReactWarning)return!1}return p.ref!==void 0}function fo(p){if(Xe.call(p,"key")){var S=Object.getOwnPropertyDescriptor(p,"key").get;if(S&&S.isReactWarning)return!1}return p.key!==void 0}function mo(p,S){if(typeof p.ref=="string"&&je.current&&S&&je.current.stateNode!==S){var E=G(je.current.type);pt[E]||(C('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',G(je.current.type),p.ref),pt[E]=!0)}}function go(p,S){{var E=function(){Vt||(Vt=!0,C("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",S))};E.isReactWarning=!0,Object.defineProperty(p,"key",{get:E,configurable:!0})}}function ho(p,S){{var E=function(){Gt||(Gt=!0,C("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",S))};E.isReactWarning=!0,Object.defineProperty(p,"ref",{get:E,configurable:!0})}}var yo=function(p,S,E,P,z,B,A){var O={$$typeof:t,type:p,key:S,ref:E,props:A,_owner:B};return O._store={},Object.defineProperty(O._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(O,"_self",{configurable:!1,enumerable:!1,writable:!1,value:P}),Object.defineProperty(O,"_source",{configurable:!1,enumerable:!1,writable:!1,value:z}),Object.freeze&&(Object.freeze(O.props),Object.freeze(O)),O};function bo(p,S,E,P,z){{var B,A={},O=null,oe=null;E!==void 0&&(zt(E),O=""+E),fo(S)&&(zt(S.key),O=""+S.key),po(S)&&(oe=S.ref,mo(S,z));for(B in S)Xe.call(S,B)&&!uo.hasOwnProperty(B)&&(A[B]=S[B]);if(p&&p.defaultProps){var Q=p.defaultProps;for(B in Q)A[B]===void 0&&(A[B]=Q[B])}if(O||oe){var X=typeof p=="function"?p.displayName||p.name||"Unknown":p;O&&go(A,X),oe&&ho(A,X)}return yo(p,O,oe,z,P,je.current,A)}}var ft=w.ReactCurrentOwner,Bt=w.ReactDebugCurrentFrame;function ve(p){if(p){var S=p._owner,E=Qe(p.type,p._source,S?S.type:null);Bt.setExtraStackFrame(E)}else Bt.setExtraStackFrame(null)}var mt;mt=!1;function gt(p){return typeof p=="object"&&p!==null&&p.$$typeof===t}function Ht(){{if(ft.current){var p=G(ft.current.type);if(p)return`

Check the render method of \``+p+"`."}return""}}function xo(p){{if(p!==void 0){var S=p.fileName.replace(/^.*[\\\/]/,""),E=p.lineNumber;return`

Check your code at `+S+":"+E+"."}return""}}var qt={};function vo(p){{var S=Ht();if(!S){var E=typeof p=="string"?p:p.displayName||p.name;E&&(S=`

Check the top-level render call using <`+E+">.")}return S}}function Ut(p,S){{if(!p._store||p._store.validated||p.key!=null)return;p._store.validated=!0;var E=vo(S);if(qt[E])return;qt[E]=!0;var P="";p&&p._owner&&p._owner!==ft.current&&(P=" It was passed a child from "+G(p._owner.type)+"."),ve(p),C('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',E,P),ve(null)}}function Yt(p,S){{if(typeof p!="object")return;if(ut(p))for(var E=0;E<p.length;E++){var P=p[E];gt(P)&&Ut(P,S)}else if(gt(p))p._store&&(p._store.validated=!0);else if(p){var z=T(p);if(typeof z=="function"&&z!==p.entries)for(var B=z.call(p),A;!(A=B.next()).done;)gt(A.value)&&Ut(A.value,S)}}}function So(p){{var S=p.type;if(S==null||typeof S=="string")return;var E;if(typeof S=="function")E=S.propTypes;else if(typeof S=="object"&&(S.$$typeof===d||S.$$typeof===m))E=S.propTypes;else return;if(E){var P=G(S);io(E,p.props,"prop",P,p)}else if(S.PropTypes!==void 0&&!mt){mt=!0;var z=G(S);C("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",z||"Unknown")}typeof S.getDefaultProps=="function"&&!S.getDefaultProps.isReactClassApproved&&C("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function wo(p){{for(var S=Object.keys(p.props),E=0;E<S.length;E++){var P=S[E];if(P!=="children"&&P!=="key"){ve(p),C("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",P),ve(null);break}}p.ref!==null&&(ve(p),C("Invalid attribute `ref` supplied to `React.Fragment`."),ve(null))}}function Wt(p,S,E,P,z,B){{var A=W(p);if(!A){var O="";(p===void 0||typeof p=="object"&&p!==null&&Object.keys(p).length===0)&&(O+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var oe=xo(z);oe?O+=oe:O+=Ht();var Q;p===null?Q="null":ut(p)?Q="array":p!==void 0&&p.$$typeof===t?(Q="<"+(G(p.type)||"Unknown")+" />",O=" Did you accidentally export a JSX literal instead of a component?"):Q=typeof p,C("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",Q,O)}var X=bo(p,S,E,z,B);if(X==null)return X;if(A){var ae=S.children;if(ae!==void 0)if(P)if(ut(ae)){for(var Se=0;Se<ae.length;Se++)Yt(ae[Se],p);Object.freeze&&Object.freeze(ae)}else C("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else Yt(ae,p)}return p===o?wo(X):So(X),X}}function Co(p,S,E){return Wt(p,S,E,!0)}function To(p,S,E){return Wt(p,S,E,!1)}var Eo=To,Io=Co;Ne.Fragment=o,Ne.jsx=Eo,Ne.jsxs=Io}()),Ne}(function(e){process.env.NODE_ENV==="production"?e.exports=nn():e.exports=sn()})(on);const an={small:s.css(["padding:",";font-size:",";min-height:20px;min-width:",";"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.xs}`,({theme:e})=>e.fontSizes.xs,({dot:e})=>e?"8px":"20px"),medium:s.css(["padding:",";font-size:",";min-height:24px;min-width:",";"],({theme:e})=>`${e.spacing.xs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.sm,({dot:e})=>e?"10px":"24px"),large:s.css(["padding:",";font-size:",";min-height:32px;min-width:",";"],({theme:e})=>`${e.spacing.sm} ${e.spacing.md}`,({theme:e})=>e.fontSizes.md,({dot:e})=>e?"12px":"32px")},cn=(e,t,r=!1)=>s.css(["",""],({theme:o})=>{let i,c,a;switch(e){case"primary":i=t?o.colors.primary:`${o.colors.primary}20`,c=t?o.colors.textInverse:o.colors.primary,a=o.colors.primary;break;case"secondary":i=t?o.colors.secondary:`${o.colors.secondary}20`,c=t?o.colors.textInverse:o.colors.secondary,a=o.colors.secondary;break;case"success":i=t?o.colors.success:`${o.colors.success}20`,c=t?o.colors.textInverse:o.colors.success,a=o.colors.success;break;case"warning":i=t?o.colors.warning:`${o.colors.warning}20`,c=t?o.colors.textInverse:o.colors.warning,a=o.colors.warning;break;case"error":i=t?o.colors.error:`${o.colors.error}20`,c=t?o.colors.textInverse:o.colors.error,a=o.colors.error;break;case"info":i=t?o.colors.info:`${o.colors.info}20`,c=t?o.colors.textInverse:o.colors.info,a=o.colors.info;break;case"neutral":i=t?o.colors.textSecondary:`${o.colors.textSecondary}10`,c=t?o.colors.textInverse:o.colors.textSecondary,a=o.colors.textSecondary;break;default:i=t?o.colors.textSecondary:`${o.colors.textSecondary}20`,c=t?o.colors.textInverse:o.colors.textSecondary,a=o.colors.textSecondary}return r?`
          background-color: transparent;
          color: ${a};
          border: 1px solid ${a};
        `:`
        background-color: ${i};
        color: ${c};
        border: 1px solid transparent;
      `}),Nr=s.span.withConfig({displayName:"IconContainer",componentId:"sc-10uskub-0"})(["display:flex;align-items:center;justify-content:center;"]),ln=s(Nr).withConfig({displayName:"StartIcon",componentId:"sc-10uskub-1"})(["margin-right:",";"],({theme:e})=>e.spacing.xxs),dn=s(Nr).withConfig({displayName:"EndIcon",componentId:"sc-10uskub-2"})(["margin-left:",";"],({theme:e})=>e.spacing.xxs),un=s.span.withConfig({displayName:"StyledBadge",componentId:"sc-10uskub-3"})(["display:",";align-items:center;justify-content:center;border-radius:",";font-weight:",";white-space:nowrap;"," "," "," "," ",""],({inline:e})=>e?"inline-flex":"flex",({theme:e,rounded:t,dot:r})=>r?"50%":t?"9999px":e.borderRadius.sm,({theme:e})=>e.fontWeights.medium,({size:e})=>an[e],({variant:e,solid:t,outlined:r})=>cn(e,t,r||!1),({dot:e})=>e&&s.css(["padding:0;height:8px;width:8px;"]),({counter:e})=>e&&s.css(["min-width:1.5em;height:1.5em;padding:0 0.5em;border-radius:1em;"]),({clickable:e})=>e&&s.css(["cursor:pointer;transition:opacity ",";&:hover{opacity:0.8;}&:active{opacity:0.6;}"],({theme:t})=>t.transitions.fast)),$e=({children:e,variant:t="default",size:r="medium",solid:o=!1,className:i="",style:c,onClick:a,rounded:l=!1,dot:d=!1,counter:f=!1,outlined:u=!1,startIcon:m,endIcon:y,max:h,inline:g=!0})=>{let b=e;return f&&typeof e=="number"&&h!==void 0&&e>h&&(b=`${h}+`),n.jsx(un,{variant:t,size:r,solid:o,clickable:!!a,className:i,style:c,onClick:a,rounded:l,dot:d,counter:f,outlined:u,inline:g,children:!d&&n.jsxs(n.Fragment,{children:[m&&n.jsx(ln,{children:m}),b,y&&n.jsx(dn,{children:y})]})})},pn=s.keyframes(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),fn=s.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-1rze74q-0"})(["width:16px;height:16px;border:2px solid rgba(255,255,255,0.3);border-radius:50%;border-top-color:#fff;animation:"," 0.8s linear infinite;margin-right:",";"],pn,({theme:e})=>e.spacing.xs),mn={small:s.css(["padding:",";font-size:",";min-height:32px;"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.xs),medium:s.css(["padding:",";font-size:",";min-height:40px;"],({theme:e})=>`${e.spacing.xs} ${e.spacing.md}`,({theme:e})=>e.fontSizes.sm),large:s.css(["padding:",";font-size:",";min-height:48px;"],({theme:e})=>`${e.spacing.sm} ${e.spacing.lg}`,({theme:e})=>e.fontSizes.md)},gn={primary:s.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:",";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:",";transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.textPrimary||e.colors.textInverse||"#fff",({theme:e})=>e.colors.primaryDark,({theme:e})=>e.colors.primaryDark),secondary:s.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:",";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:",";transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.secondary,({theme:e})=>e.colors.textPrimary||e.colors.textInverse||"#fff",({theme:e})=>e.colors.secondaryDark,({theme:e})=>e.colors.secondaryDark),outline:s.css(["background-color:transparent;color:",";border:1px solid ",";&:hover:not(:disabled){background-color:","0d;transform:translateY(-1px);}&:active:not(:disabled){background-color:","1a;transform:translateY(0);}"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),text:s.css(["background-color:transparent;color:",";border:none;padding-left:",";padding-right:",";&:hover:not(:disabled){background-color:","0d;}&:active:not(:disabled){background-color:","1a;}"],({theme:e})=>e.colors.primary,({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),success:s.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:","dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.success,({theme:e})=>e.colors.textInverse||"#fff",({theme:e})=>e.colors.success),danger:s.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:","dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.error,({theme:e})=>e.colors.textInverse||"#fff",({theme:e})=>e.colors.error)},hn=s.button.withConfig({displayName:"StyledButton",componentId:"sc-1rze74q-1"})(["display:inline-flex;align-items:center;justify-content:center;border-radius:",";font-weight:",";cursor:pointer;transition:all ",";position:relative;overflow:hidden;"," "," "," &:disabled{opacity:0.6;cursor:not-allowed;box-shadow:none;transform:translateY(0);}"," ",""],({theme:e})=>e.borderRadius.sm,({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.medium)||500},({theme:e})=>{var t;return((t=e.transitions)==null?void 0:t.fast)||"0.2s ease"},({size:e="medium"})=>mn[e],({variant:e="primary"})=>gn[e],({fullWidth:e})=>e&&s.css(["width:100%;"]),({$hasStartIcon:e})=>e&&s.css(["& > *:first-child{margin-right:",";}"],({theme:t})=>t.spacing.xs),({$hasEndIcon:e})=>e&&s.css(["& > *:last-child{margin-left:",";}"],({theme:t})=>t.spacing.xs)),yn=s.div.withConfig({displayName:"ButtonContent",componentId:"sc-1rze74q-2"})(["display:flex;align-items:center;justify-content:center;"]),se=({children:e,variant:t="primary",disabled:r=!1,loading:o=!1,size:i="medium",fullWidth:c=!1,startIcon:a,endIcon:l,onClick:d,className:f="",type:u="button",...m})=>n.jsx(hn,{variant:t,disabled:r||o,size:i,fullWidth:c,onClick:d,className:f,type:u,$hasStartIcon:!!a&&!o,$hasEndIcon:!!l&&!o,...m,children:n.jsxs(yn,{children:[o&&n.jsx(fn,{}),!o&&a,e,!o&&l]})}),bn=s.div.withConfig({displayName:"InputWrapper",componentId:"sc-uv3rzi-0"})(["display:flex;flex-direction:column;width:",";position:relative;"],({fullWidth:e})=>e?"100%":"auto"),xn=s.label.withConfig({displayName:"Label",componentId:"sc-uv3rzi-1"})(["font-size:",";color:",";margin-bottom:",";font-weight:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xxs,({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.medium)||500}),vn=s.div.withConfig({displayName:"InputContainer",componentId:"sc-uv3rzi-2"})(["display:flex;align-items:center;position:relative;width:100%;border-radius:",";border:1px solid ",";background-color:",";transition:all ",";"," "," ",""],({theme:e})=>e.borderRadius.sm,({theme:e,hasError:t,hasSuccess:r,isFocused:o})=>t?e.colors.error:r?e.colors.success:o?e.colors.primary:e.colors.border,({theme:e})=>e.colors.surface,({theme:e})=>{var t;return((t=e.transitions)==null?void 0:t.fast)||"0.2s ease"},({disabled:e,theme:t})=>e&&s.css(["opacity:0.6;background-color:",";cursor:not-allowed;"],t.colors.background),({isFocused:e,theme:t,hasError:r,hasSuccess:o})=>e&&s.css(["box-shadow:0 0 0 2px ",";"],r?`${t.colors.error}33`:o?`${t.colors.success}33`:`${t.colors.primary}33`),({$size:e})=>{switch(e){case"small":return s.css(["height:32px;"]);case"large":return s.css(["height:48px;"]);default:return s.css(["height:40px;"])}}),Jt=s.div.withConfig({displayName:"IconContainer",componentId:"sc-uv3rzi-3"})(["display:flex;align-items:center;justify-content:center;padding:0 ",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.textSecondary),Sn=s.input.withConfig({displayName:"StyledInput",componentId:"sc-uv3rzi-4"})(["flex:1;border:none;background:transparent;color:",";width:100%;outline:none;&:disabled{cursor:not-allowed;}&::placeholder{color:",";}"," "," ",""],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.textDisabled,({hasStartIcon:e})=>e&&s.css(["padding-left:0;"]),({hasEndIcon:e})=>e&&s.css(["padding-right:0;"]),({$size:e,theme:t})=>e==="small"?s.css(["font-size:",";padding:"," ",";"],t.fontSizes.xs,t.spacing.xxs,t.spacing.xs):e==="large"?s.css(["font-size:",";padding:"," ",";"],t.fontSizes.md,t.spacing.sm,t.spacing.md):s.css(["font-size:",";padding:"," ",";"],t.fontSizes.sm,t.spacing.xs,t.spacing.sm)),wn=s.button.withConfig({displayName:"ClearButton",componentId:"sc-uv3rzi-5"})(["background:none;border:none;cursor:pointer;color:",";padding:0 ",";display:flex;align-items:center;justify-content:center;&:hover{color:",";}&:focus{outline:none;}"],({theme:e})=>e.colors.textDisabled,({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.textSecondary),Cn=s.div.withConfig({displayName:"HelperTextContainer",componentId:"sc-uv3rzi-6"})(["display:flex;justify-content:space-between;margin-top:",";font-size:",";color:",";"],({theme:e})=>e.spacing.xxs,({theme:e})=>e.fontSizes.xs,({theme:e,hasError:t,hasSuccess:r})=>t?e.colors.error:r?e.colors.success:e.colors.textSecondary),xe=({value:e,onChange:t,placeholder:r="",disabled:o=!1,error:i="",type:c="text",name:a="",id:l="",className:d="",required:f=!1,autoComplete:u="",label:m="",helperText:y="",startIcon:h,endIcon:g,loading:b=!1,success:T=!1,clearable:w=!1,onClear:C,maxLength:L,showCharCount:$=!1,size:j="medium",fullWidth:k=!1,...M})=>{const[R,N]=x.useState(!1),W=x.useRef(null),q=()=>{C?C():t(""),W.current&&W.current.focus()},D=ee=>{N(!0),M.onFocus&&M.onFocus(ee)},G=ee=>{N(!1),M.onBlur&&M.onBlur(ee)},J=w&&e&&!o,Z=(e==null?void 0:e.length)||0,le=$||L!==void 0&&L>0;return n.jsxs(bn,{className:d,fullWidth:k,children:[m&&n.jsxs(xn,{htmlFor:l,children:[m,f&&" *"]}),n.jsxs(vn,{hasError:!!i,hasSuccess:!!T,disabled:!!o,$size:j,hasStartIcon:!!h,hasEndIcon:!!(g||J),isFocused:!!R,children:[h&&n.jsx(Jt,{children:h}),n.jsx(Sn,{ref:W,type:c,value:e,onChange:ee=>t(ee.target.value),placeholder:r,disabled:!!(o||b),name:a,id:l,required:!!f,autoComplete:u,hasStartIcon:!!h,hasEndIcon:!!(g||J),$size:j,maxLength:L,onFocus:D,onBlur:G,...M}),J&&n.jsx(wn,{type:"button",onClick:q,tabIndex:-1,children:"✕"}),g&&n.jsx(Jt,{children:g})]}),(i||y||le)&&n.jsxs(Cn,{hasError:!!i,hasSuccess:!!T,children:[n.jsx("div",{children:i||y}),le&&n.jsxs("div",{children:[Z,L!==void 0&&`/${L}`]})]})]})},Zt={small:s.css(["height:100px;"]),medium:s.css(["height:200px;"]),large:s.css(["height:300px;"]),custom:e=>s.css(["height:",";width:",";"],e.customHeight,e.customWidth||"100%")},Tn={default:s.css(["background-color:",";border-radius:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.md),card:s.css(["background-color:",";border-radius:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.sm),text:s.css(["background-color:transparent;height:auto !important;min-height:1.5em;"]),list:s.css(["background-color:",";border-radius:",";margin-bottom:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.spacing.sm)},En=s.keyframes(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),In=s.div.withConfig({displayName:"Container",componentId:"sc-12vczt5-0"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;"," ",""],({size:e,customHeight:t,customWidth:r})=>e==="custom"?Zt.custom({customHeight:t,customWidth:r}):Zt[e],({variant:e})=>Tn[e]),Rn=s.div.withConfig({displayName:"Spinner",componentId:"sc-12vczt5-1"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:"," 1s linear infinite;margin-bottom:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary,En,({theme:e})=>e.spacing.sm),jn=s.div.withConfig({displayName:"Text",componentId:"sc-12vczt5-2"})(["color:",";font-size:",";"],({theme:e})=>e.colors.textSecondary,({theme:e})=>e.fontSizes.sm),Dr=({variant:e="default",size:t="medium",height:r="200px",width:o="",text:i="Loading...",showSpinner:c=!0,className:a=""})=>n.jsxs(In,{variant:e,size:t,customHeight:r,customWidth:o,className:a,children:[c&&n.jsx(Rn,{}),i&&n.jsx(jn,{children:i})]}),kn=s.div.withConfig({displayName:"SelectWrapper",componentId:"sc-wvk2um-0"})(["display:flex;flex-direction:column;width:",";position:relative;"],({fullWidth:e})=>e?"100%":"auto"),Nn=s.label.withConfig({displayName:"Label",componentId:"sc-wvk2um-1"})(["font-size:",";color:",";margin-bottom:",";font-weight:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xxs,({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.medium)||500}),Dn=s.div.withConfig({displayName:"SelectContainer",componentId:"sc-wvk2um-2"})(["display:flex;align-items:center;position:relative;width:100%;border-radius:",";border:1px solid ",";background-color:",";transition:all ",";"," "," ",""],({theme:e})=>e.borderRadius.sm,({theme:e,hasError:t,hasSuccess:r,isFocused:o})=>t?e.colors.error:r?e.colors.success:o?e.colors.primary:e.colors.border,({theme:e})=>e.colors.surface,({theme:e})=>{var t;return((t=e.transitions)==null?void 0:t.fast)||"0.2s ease"},({disabled:e,theme:t})=>e&&s.css(["opacity:0.6;background-color:",";cursor:not-allowed;"],t.colors.background),({isFocused:e,theme:t,hasError:r,hasSuccess:o})=>e&&s.css(["box-shadow:0 0 0 2px ",";"],r?`${t.colors.error}33`:o?`${t.colors.success}33`:`${t.colors.primary}33`),({$size:e})=>{switch(e){case"small":return s.css(["height:32px;"]);case"large":return s.css(["height:48px;"]);default:return s.css(["height:40px;"])}}),Ln=s.div.withConfig({displayName:"IconContainer",componentId:"sc-wvk2um-3"})(["display:flex;align-items:center;justify-content:center;padding:0 ",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.textSecondary),Mn=s.select.withConfig({displayName:"StyledSelect",componentId:"sc-wvk2um-4"})(["flex:1;border:none;background:transparent;color:",`;width:100%;outline:none;appearance:none;background-image:url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right `," center;background-size:16px;padding-right:",";&:disabled{cursor:not-allowed;}"," ",""],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.xl,({hasStartIcon:e})=>e&&s.css(["padding-left:0;"]),({$size:e,theme:t})=>e==="small"?s.css(["font-size:",";padding:"," ",";"],t.fontSizes.xs,t.spacing.xxs,t.spacing.xs):e==="large"?s.css(["font-size:",";padding:"," ",";"],t.fontSizes.md,t.spacing.sm,t.spacing.md):s.css(["font-size:",";padding:"," ",";"],t.fontSizes.sm,t.spacing.xs,t.spacing.sm)),_n=s.div.withConfig({displayName:"HelperTextContainer",componentId:"sc-wvk2um-5"})(["display:flex;justify-content:space-between;margin-top:",";font-size:",";color:",";"],({theme:e})=>e.spacing.xxs,({theme:e})=>e.fontSizes.xs,({theme:e,hasError:t,hasSuccess:r})=>t?e.colors.error:r?e.colors.success:e.colors.textSecondary),Pn=s.optgroup.withConfig({displayName:"OptionGroup",componentId:"sc-wvk2um-6"})(["font-weight:",";color:",";"],({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.medium)||500},({theme:e})=>e.colors.textPrimary),Ce=({options:e,value:t,onChange:r,disabled:o=!1,error:i="",name:c="",id:a="",className:l="",required:d=!1,placeholder:f="",label:u="",helperText:m="",size:y="medium",fullWidth:h=!0,loading:g=!1,success:b=!1,startIcon:T,...w})=>{const[C,L]=x.useState(!1),$=N=>{L(!0),w.onFocus&&w.onFocus(N)},j=N=>{L(!1),w.onBlur&&w.onBlur(N)},k={},M=[];e.forEach(N=>{N.group?(k[N.group]||(k[N.group]=[]),k[N.group].push(N)):M.push(N)});const R=Object.keys(k).length>0;return n.jsxs(kn,{className:l,fullWidth:h,children:[u&&n.jsxs(Nn,{htmlFor:a,children:[u,d&&" *"]}),n.jsxs(Dn,{hasError:!!i,hasSuccess:!!b,disabled:!!(o||g),$size:y,hasStartIcon:!!T,isFocused:!!C,children:[T&&n.jsx(Ln,{children:T}),n.jsxs(Mn,{value:t,onChange:N=>r(N.target.value),disabled:!!(o||g),name:c,id:a,required:!!d,hasStartIcon:!!T,$size:y,onFocus:$,onBlur:j,...w,children:[f&&n.jsx("option",{value:"",disabled:!0,children:f}),R?n.jsxs(n.Fragment,{children:[M.map(N=>n.jsx("option",{value:N.value,disabled:N.disabled,children:N.label},N.value)),Object.entries(k).map(([N,W])=>n.jsx(Pn,{label:N,children:W.map(q=>n.jsx("option",{value:q.value,disabled:q.disabled,children:q.label},q.value))},N))]}):e.map(N=>n.jsx("option",{value:N.value,disabled:N.disabled,children:N.label},N.value))]})]}),(i||m)&&n.jsx(_n,{hasError:!!i,hasSuccess:!!b,children:n.jsx("div",{children:i||m})})]})},er={small:"8px",medium:"12px",large:"16px"},Fn={small:s.css(["font-size:",";margin-left:",";"],({theme:e})=>e.fontSizes.xs,({theme:e})=>e.spacing.xs),medium:s.css(["font-size:",";margin-left:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.sm),large:s.css(["font-size:",";margin-left:",";"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.spacing.md)},On=s.css(["@keyframes pulse{0%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0.7);}70%{transform:scale(1);box-shadow:0 0 0 6px rgba(var(--pulse-color),0);}100%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0);}}animation:pulse 2s infinite;"]),$n=s.div.withConfig({displayName:"Container",componentId:"sc-gwj3m-0"})(["display:inline-flex;align-items:center;"]),An=s.div.withConfig({displayName:"Indicator",componentId:"sc-gwj3m-1"})(["border-radius:50%;width:",";height:",";",""],({size:e})=>er[e],({size:e})=>er[e],({status:e,theme:t,pulse:r})=>{let o,i;switch(e){case"success":o=t.colors.success,i="76, 175, 80";break;case"error":o=t.colors.error,i="244, 67, 54";break;case"warning":o=t.colors.warning,i="255, 152, 0";break;case"info":o=t.colors.info,i="33, 150, 243";break;default:o=t.colors.textSecondary,i="158, 158, 158"}return s.css(["background-color:",";",""],o,r&&s.css(["--pulse-color:",";",""],i,On))}),zn=s.span.withConfig({displayName:"Label",componentId:"sc-gwj3m-2"})([""," ",""],({size:e})=>Fn[e],({status:e,theme:t})=>{let r;switch(e){case"success":r=t.colors.success;break;case"error":r=t.colors.error;break;case"warning":r=t.colors.warning;break;case"info":r=t.colors.info;break;default:r=t.colors.textSecondary}return s.css(["color:",";font-weight:",";"],r,t.fontWeights.medium)}),Vn=({status:e,size:t="medium",pulse:r=!1,showLabel:o=!1,label:i="",className:c=""})=>{const a=i||e.charAt(0).toUpperCase()+e.slice(1);return n.jsxs($n,{className:c,children:[n.jsx(An,{status:e,size:t,pulse:r}),o&&n.jsx(zn,{status:e,size:t,children:a})]})},Gn={small:s.css(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.xs}`,({theme:e})=>e.fontSizes.xs),medium:s.css(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.xs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.sm),large:s.css(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.sm} ${e.spacing.md}`,({theme:e})=>e.fontSizes.md)},Bn=e=>s.css(["",""],({theme:t})=>{let r,o,i;switch(e){case"primary":r=`${t.colors.primary}10`,o=t.colors.primary,i=`${t.colors.primary}30`;break;case"secondary":r=`${t.colors.secondary}10`,o=t.colors.secondary,i=`${t.colors.secondary}30`;break;case"success":r=`${t.colors.success}10`,o=t.colors.success,i=`${t.colors.success}30`;break;case"warning":r=`${t.colors.warning}10`,o=t.colors.warning,i=`${t.colors.warning}30`;break;case"error":r=`${t.colors.error}10`,o=t.colors.error,i=`${t.colors.error}30`;break;case"info":r=`${t.colors.info}10`,o=t.colors.info,i=`${t.colors.info}30`;break;default:r=`${t.colors.textSecondary}10`,o=t.colors.textSecondary,i=`${t.colors.textSecondary}30`}return`
        background-color: ${r};
        color: ${o};
        border: 1px solid ${i};
      `}),Hn=s.span.withConfig({displayName:"StyledTag",componentId:"sc-11nmnw9-0"})(["display:inline-flex;align-items:center;border-radius:",";font-weight:",";"," "," ",""],({theme:e})=>e.borderRadius.pill,({theme:e})=>e.fontWeights.medium,({size:e})=>Gn[e],({variant:e})=>Bn(e),({clickable:e})=>e&&s.css(["cursor:pointer;transition:opacity ",";&:hover{opacity:0.8;}&:active{opacity:0.6;}"],({theme:t})=>t.transitions.fast)),qn=s.button.withConfig({displayName:"RemoveButton",componentId:"sc-11nmnw9-1"})(["display:inline-flex;align-items:center;justify-content:center;background:none;border:none;cursor:pointer;color:inherit;opacity:0.7;margin-left:",";padding:0;"," &:hover{opacity:1;}"],({theme:e})=>e.spacing.xs,({size:e,theme:t})=>{const r={small:"12px",medium:"14px",large:"16px"};return`
      width: ${r[e]};
      height: ${r[e]};
      font-size: ${t.fontSizes.xs};
    `}),Un=({children:e,variant:t="default",size:r="medium",removable:o=!1,onRemove:i,className:c="",onClick:a})=>{const l=d=>{d.stopPropagation(),i==null||i()};return n.jsxs(Hn,{variant:t,size:r,clickable:!!a,className:c,onClick:a,children:[e,o&&n.jsx(qn,{size:r,onClick:l,children:"×"})]})},Yn=s.div.withConfig({displayName:"TimePickerContainer",componentId:"sc-v5w9zw-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),Wn=s.label.withConfig({displayName:"Label",componentId:"sc-v5w9zw-1"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.fontWeights.medium,({theme:e})=>e.colors.textPrimary),Kn=s.input.withConfig({displayName:"TimeInput",componentId:"sc-v5w9zw-2"})(["padding:",";border:1px solid ",";border-radius:",";font-size:",";color:",";background-color:",";transition:border-color ",";&:focus{outline:none;border-color:",";}&:disabled{background-color:",";cursor:not-allowed;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.surface,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.chartGrid),Qn=({id:e,name:t,value:r,onChange:o,label:i,required:c=!1,disabled:a=!1,className:l,placeholder:d="HH:MM",min:f,max:u})=>n.jsxs(Yn,{className:l,children:[i&&n.jsxs(Wn,{htmlFor:e,children:[i,c&&n.jsx("span",{style:{color:"red"},children:" *"})]}),n.jsx(Kn,{id:e,name:t,type:"time",value:r,onChange:o,required:c,disabled:a,placeholder:d,min:f,max:u})]}),Xn=Qn,it=()=>Intl.DateTimeFormat().resolvedOptions().timeZone,St=(e,t=new Date)=>{const i=new Intl.DateTimeFormat("en",{timeZone:e,timeZoneName:"short"}).formatToParts(t).find(c=>c.type==="timeZoneName");return(i==null?void 0:i.value)||e},wt=(e,t)=>{const r=t||it(),[o,i]=e.split(":").map(Number),c=new Date,a=c.getFullYear(),l=String(c.getMonth()+1).padStart(2,"0"),d=String(c.getDate()).padStart(2,"0"),f=`${String(o).padStart(2,"0")}:${String(i).padStart(2,"0")}:00`,u=`${a}-${l}-${d}T${f}`,m=new Date(u),y=new Date,h=new Date(y.toLocaleString("en-US",{timeZone:"America/New_York"})),b=new Date(y.toLocaleString("en-US",{timeZone:r})).getTime()-h.getTime();return new Date(m.getTime()+b).toLocaleTimeString("en-GB",{timeZone:r,hour:"2-digit",minute:"2-digit",hour12:!1})},Jn=(e,t)=>{const r=new Date;return new Date(`${r.toDateString()} ${e}:00`).toLocaleTimeString("en-US",{timeZone:"America/New_York",hour:"2-digit",minute:"2-digit",hour12:!1})},rt=e=>{const t=e||it(),r=new Date,o=r.toLocaleTimeString("en-US",{timeZone:"America/New_York",hour:"2-digit",minute:"2-digit",hour12:!1}),i=r.toLocaleTimeString("en-GB",{timeZone:t,hour:"2-digit",minute:"2-digit",hour12:!1}),c=St("America/New_York",r),a=St(t,r);return{nyTime:o,localTime:i,nyTimezone:c,localTimezone:a,formatted:`${o} ${c} | ${i} ${a}`}},Zn=()=>{const t=new Date().toLocaleTimeString("en-US",{timeZone:"America/New_York",hour:"2-digit",minute:"2-digit",hour12:!1});return _e(t)},es=()=>{const e=new Date;return new Date(e.toLocaleString("en-US",{timeZone:"America/New_York"}))},Lr=e=>{const t=Math.floor(e/60),r=e%60;let o="";return t>0?o=r>0?`${t}h ${r}m`:`${t}h`:o=`${r}m`,{totalMinutes:e,hours:t,minutes:r,formatted:o}},Fe=e=>{const r=new Date().toLocaleString("en-US",{timeZone:"America/New_York"}),o=new Date(r),[i,c]=e.split(":").map(Number),a=new Date(o);a.setHours(i,c,0,0),a<=o&&a.setDate(a.getDate()+1);const l=a.getTime()-o.getTime(),d=Math.floor(l/(1e3*60));return Lr(d)},ts=e=>Fe(e),at=(e,t,r)=>{const o=r||it(),i=wt(e,o),c=wt(t,o),a=St(o);return{nyStart:e,nyEnd:t,localStart:i,localEnd:c,formatted:`${e}-${t} NY | ${i}-${c} ${a}`}},Mr=(e,t)=>{const o=new Date().toLocaleTimeString("en-US",{timeZone:"America/New_York",hour:"2-digit",minute:"2-digit",hour12:!1}),i=_e(o),c=_e(e),a=_e(t);return i>=c&&i<=a},_e=e=>{const[t,r]=e.split(":").map(Number);return t*60+r},rs=e=>{const t=Math.floor(e/60),r=e%60;return`${t.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`},_r=e=>{const t=e.localTimezone.includes("GMT")?"🇮🇪":"🌍";return`${e.localTime} ${t} | ${e.nyTime} 🇺🇸`},os=e=>`${e.localTime} Local (${e.localTimezone}) | ${e.nyTime} NY (${e.nyTimezone})`,ns=(e,t,r,o)=>{const i=Mr(t,r),c=at(t,r,o);if(i)return{isActive:!0,timeRemaining:ts(r),sessionTime:c,status:"active"};const a=Fe(t);return{isActive:!1,timeUntilStart:a,sessionTime:c,status:a.totalMinutes<24*60?"upcoming":"ended"}},he=s.div.withConfig({displayName:"TimeContainer",componentId:"sc-10dqpqu-0"})(["display:flex;align-items:center;gap:",";font-family:'SF Mono','Monaco','Inconsolata','Roboto Mono',monospace;font-weight:600;"],({format:e})=>e==="mobile"?"4px":"8px"),Te=s.span.withConfig({displayName:"NYTime",componentId:"sc-10dqpqu-1"})(["color:#3b82f6;font-size:inherit;"]),Ee=s.span.withConfig({displayName:"LocalTime",componentId:"sc-10dqpqu-2"})(["color:#10b981;font-size:inherit;"]),Ie=s.span.withConfig({displayName:"Separator",componentId:"sc-10dqpqu-3"})(["color:#6b7280;font-size:inherit;"]),ot=s.span.withConfig({displayName:"Timezone",componentId:"sc-10dqpqu-4"})(["color:#9ca3af;font-size:0.85em;font-weight:500;"]),ht=s.span.withConfig({displayName:"LiveIndicator",componentId:"sc-10dqpqu-5"})(["color:#ef4444;font-size:0.75em;font-weight:bold;animation:pulse 2s infinite;@keyframes pulse{0%,100%{opacity:1;}50%{opacity:0.5;}}"]),tr=s.div.withConfig({displayName:"CountdownContainer",componentId:"sc-10dqpqu-6"})(["display:flex;align-items:center;gap:8px;"]),rr=s.span.withConfig({displayName:"CountdownValue",componentId:"sc-10dqpqu-7"})(["color:#f59e0b;font-weight:bold;"]),yt=s.span.withConfig({displayName:"CountdownLabel",componentId:"sc-10dqpqu-8"})(["color:#9ca3af;font-size:0.9em;"]),ss=({format:e,showLive:t,updateInterval:r})=>{const[o,i]=x.useState(rt());return x.useEffect(()=>{const c=setInterval(()=>{i(rt())},r*1e3);return()=>clearInterval(c)},[r]),e==="mobile"?n.jsxs(he,{format:e,children:[n.jsx("span",{children:_r(o)}),t&&n.jsx(ht,{children:"LIVE"})]}):e==="compact"?n.jsxs(he,{format:e,children:[n.jsx(Te,{children:o.nyTime}),n.jsx(Ie,{children:"|"}),n.jsx(Ee,{children:o.localTime}),t&&n.jsx(ht,{children:"LIVE"})]}):n.jsxs(he,{format:e,children:[n.jsx(Te,{children:o.nyTime}),n.jsx(ot,{children:o.nyTimezone}),n.jsx(Ie,{children:"|"}),n.jsx(Ee,{children:o.localTime}),n.jsx(ot,{children:o.localTimezone}),t&&n.jsx(ht,{children:"LIVE"})]})},is=({nyTime:e,format:t})=>{const r=rt(),o=at(e,e);return t==="mobile"?n.jsx(he,{format:t,children:n.jsxs("span",{children:[o.localStart," 🇮🇪 | ",e," 🇺🇸"]})}):t==="compact"?n.jsxs(he,{format:t,children:[n.jsx(Te,{children:e}),n.jsx(Ie,{children:"|"}),n.jsx(Ee,{children:o.localStart})]}):n.jsxs(he,{format:t,children:[n.jsx(Te,{children:e}),n.jsx(ot,{children:r.nyTimezone}),n.jsx(Ie,{children:"|"}),n.jsx(Ee,{children:o.localStart}),n.jsx(ot,{children:r.localTimezone})]})},as=({targetNYTime:e,format:t,updateInterval:r})=>{const[o,i]=x.useState(Fe(e));return x.useEffect(()=>{const c=setInterval(()=>{i(Fe(e))},r*1e3);return()=>clearInterval(c)},[e,r]),t==="mobile"?n.jsxs(tr,{children:[n.jsx(rr,{children:o.formatted}),n.jsxs(yt,{children:["until ",e]})]}):n.jsxs(tr,{children:[n.jsx(yt,{children:"Next in:"}),n.jsx(rr,{children:o.formatted}),n.jsxs(yt,{children:["(",e," NY)"]})]})},cs=({sessionStart:e,sessionEnd:t,format:r})=>{const o=at(e,t);return r==="mobile"?n.jsx(he,{format:r,children:n.jsx("span",{children:o.formatted})}):r==="compact"?n.jsxs(he,{format:r,children:[n.jsxs(Te,{children:[e,"-",t]}),n.jsx(Ie,{children:"|"}),n.jsxs(Ee,{children:[o.localStart,"-",o.localEnd]})]}):n.jsxs(he,{format:r,children:[n.jsx("div",{children:n.jsxs(Te,{children:[e,"-",t," NY"]})}),n.jsx(Ie,{children:"|"}),n.jsx("div",{children:n.jsxs(Ee,{children:[o.localStart,"-",o.localEnd," Local"]})})]})},ls=e=>{const{mode:t="current",nyTime:r,targetNYTime:o,sessionStart:i,sessionEnd:c,format:a="desktop",showLive:l=!1,className:d,updateInterval:f=1}=e,u={className:d,style:{fontSize:a==="mobile"?"14px":a==="compact"?"13px":"14px"}};switch(t){case"static":return r?n.jsx("div",{...u,children:n.jsx(is,{nyTime:r,format:a})}):(console.warn("DualTimeDisplay: nyTime is required for static mode"),null);case"countdown":return o?n.jsx("div",{...u,children:n.jsx(as,{targetNYTime:o,format:a,updateInterval:f})}):(console.warn("DualTimeDisplay: targetNYTime is required for countdown mode"),null);case"session":return!i||!c?(console.warn("DualTimeDisplay: sessionStart and sessionEnd are required for session mode"),null):n.jsx("div",{...u,children:n.jsx(cs,{sessionStart:i,sessionEnd:c,format:a})});case"current":default:return n.jsx("div",{...u,children:n.jsx(ss,{format:a,showLive:l,updateInterval:f})})}},ds=s.div.withConfig({displayName:"SelectContainer",componentId:"sc-w0dp8e-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),us=s.label.withConfig({displayName:"Label",componentId:"sc-w0dp8e-1"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.fontWeights.medium,({theme:e})=>e.colors.textPrimary),ps=s.select.withConfig({displayName:"Select",componentId:"sc-w0dp8e-2"})(["padding:",";border:1px solid ",";border-radius:",";font-size:",";color:",";background-color:",";transition:border-color ",";&:focus{outline:none;border-color:",";}&:disabled{background-color:",";cursor:not-allowed;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.surface,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.chartGrid),fs=({id:e,name:t,value:r,onChange:o,options:i,label:c,required:a=!1,disabled:l=!1,className:d,placeholder:f})=>n.jsxs(ds,{className:d,children:[c&&n.jsxs(us,{htmlFor:e,children:[c,a&&n.jsx("span",{style:{color:"red"},children:" *"})]}),n.jsxs(ps,{id:e,name:t,value:r,onChange:o,required:a,disabled:l,children:[f&&n.jsx("option",{value:"",disabled:!0,children:f}),i.map(u=>n.jsx("option",{value:u.value,children:u.label},u.value))]})]}),ms=fs,gs=s.span.withConfig({displayName:"StyledLoadingCell",componentId:"sc-1i0qdjp-0"})(["display:inline-flex;align-items:center;justify-content:flex-end;opacity:0.6;position:relative;"," border-radius:",";&::after{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);animation:shimmer 1.5s infinite;}@keyframes shimmer{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}"],({$size:e,theme:t})=>{var r,o,i,c,a,l,d,f,u;switch(e){case"small":return s.css(["font-size:",";padding:"," ",";"],((r=t.fontSizes)==null?void 0:r.xs)||"12px",((o=t.spacing)==null?void 0:o.xxs)||"2px",((i=t.spacing)==null?void 0:i.xs)||"4px");case"large":return s.css(["font-size:",";padding:"," ",";"],((c=t.fontSizes)==null?void 0:c.lg)||"18px",((a=t.spacing)==null?void 0:a.sm)||"8px",((l=t.spacing)==null?void 0:l.md)||"12px");default:return s.css(["font-size:",";padding:"," ",";"],((d=t.fontSizes)==null?void 0:d.sm)||"14px",((f=t.spacing)==null?void 0:f.xs)||"4px",((u=t.spacing)==null?void 0:u.sm)||"8px")}},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.sm)||"4px"}),hs=s.span.withConfig({displayName:"LoadingPlaceholder",componentId:"sc-1i0qdjp-1"})(["display:inline-block;width:",";height:1em;background-color:currentColor;opacity:0.3;border-radius:2px;"],({$width:e})=>e||"60px"),ys=e=>{const{size:t="medium",width:r,className:o,"aria-label":i}=e;return n.jsx(gs,{className:o,$size:t,$width:r,"aria-label":i||"Loading data",role:"cell","aria-busy":"true",children:n.jsx(hs,{$width:r})})},bs=ys,xs=s.keyframes(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),vs=s.keyframes(["0%{background-position:0% 0%;}100%{background-position:100% 100%;}"]),Ss=s.div.withConfig({displayName:"StyledSpinner",componentId:"sc-1hoaoss-0"})(["display:inline-block;position:relative;"," &::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;border-radius:50%;border:2px solid transparent;"," animation:"," ","s linear infinite;}",""],({$size:e})=>{switch(e){case"xs":return s.css(["width:16px;height:16px;"]);case"sm":return s.css(["width:20px;height:20px;"]);case"md":return s.css(["width:32px;height:32px;"]);case"lg":return s.css(["width:48px;height:48px;"]);case"xl":return s.css(["width:64px;height:64px;"]);default:return s.css(["width:32px;height:32px;"])}},({$variant:e,theme:t})=>{var r,o,i,c,a,l;switch(e){case"primary":return s.css(["border-top-color:",";border-right-color:",";"],((r=t.colors)==null?void 0:r.primary)||"#dc2626",((o=t.colors)==null?void 0:o.primary)||"#dc2626");case"secondary":return s.css(["border-top-color:",";border-right-color:",";"],((i=t.colors)==null?void 0:i.textSecondary)||"#9ca3af",((c=t.colors)==null?void 0:c.textSecondary)||"#9ca3af");case"white":return s.css(["border-top-color:#ffffff;border-right-color:#ffffff;"]);case"red":return s.css(["border-top-color:#dc2626;border-right-color:#dc2626;"]);default:return s.css(["border-top-color:",";border-right-color:",";"],((a=t.colors)==null?void 0:a.primary)||"#dc2626",((l=t.colors)==null?void 0:l.primary)||"#dc2626")}},xs,({$speed:e})=>1/e,({$showStripes:e,$variant:t})=>e&&s.css(["&::after{content:'';position:absolute;top:2px;left:2px;right:2px;bottom:2px;border-radius:50%;background:",";background-size:8px 8px;animation:"," ","s linear infinite;}"],t==="red"||t==="primary"?"linear-gradient(45deg, transparent 25%, rgba(220, 38, 38, 0.3) 25%, rgba(220, 38, 38, 0.3) 50%, transparent 50%, transparent 75%, rgba(220, 38, 38, 0.3) 75%)":"linear-gradient(45deg, transparent 25%, rgba(156, 163, 175, 0.3) 25%, rgba(156, 163, 175, 0.3) 50%, transparent 50%, transparent 75%, rgba(156, 163, 175, 0.3) 75%)",vs,r=>2/r.$speed)),ws=s.div.withConfig({displayName:"SpinnerContainer",componentId:"sc-1hoaoss-1"})(["display:inline-flex;align-items:center;justify-content:center;"]),Cs=e=>{const{size:t="md",variant:r="primary",className:o,"aria-label":i,speed:c=1,showStripes:a=!1}=e;return n.jsx(ws,{className:o,children:n.jsx(Ss,{$size:t,$variant:r,$speed:c,$showStripes:a,role:"status","aria-label":i||"Loading","aria-live":"polite"})})},Ts=Cs,Es={none:s.css(["padding:0;"]),small:s.css(["padding:",";"],({theme:e})=>e.spacing.sm),medium:s.css(["padding:",";"],({theme:e})=>e.spacing.md),large:s.css(["padding:",";"],({theme:e})=>e.spacing.lg)},Is={default:s.css(["background-color:",";"],({theme:e})=>e.colors.surface),primary:s.css(["background-color:","10;border-color:","30;"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),secondary:s.css(["background-color:","10;border-color:","30;"],({theme:e})=>e.colors.secondary,({theme:e})=>e.colors.secondary),outlined:s.css(["background-color:transparent;border:1px solid ",";"],({theme:e})=>e.colors.border),elevated:s.css(["background-color:",";box-shadow:",";border:none;"],({theme:e})=>e.colors.surface,({theme:e})=>e.shadows.md)},Rs=s.div.withConfig({displayName:"CardContainer",componentId:"sc-mv9m67-0"})(["border-radius:",";overflow:hidden;transition:all ",";position:relative;"," "," "," ",""],({theme:e})=>e.borderRadius.md,({theme:e})=>e.transitions.fast,({bordered:e,theme:t})=>e&&s.css(["border:1px solid ",";"],t.colors.border),({padding:e})=>Es[e],({variant:e})=>Is[e],({clickable:e})=>e&&s.css(["cursor:pointer;&:hover{transform:translateY(-2px);box-shadow:",";}&:active{transform:translateY(0);}"],({theme:t})=>t.shadows.md)),js=s.div.withConfig({displayName:"CardHeader",componentId:"sc-mv9m67-1"})(["display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:",";"],({theme:e})=>e.spacing.md),ks=s.div.withConfig({displayName:"HeaderContent",componentId:"sc-mv9m67-2"})(["flex:1;"]),Ns=s.h3.withConfig({displayName:"CardTitle",componentId:"sc-mv9m67-3"})(["margin:0;font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.fontWeights.semibold,({theme:e})=>e.colors.textPrimary),Ds=s.div.withConfig({displayName:"CardSubtitle",componentId:"sc-mv9m67-4"})(["margin-top:",";font-size:",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),Ls=s.div.withConfig({displayName:"ActionsContainer",componentId:"sc-mv9m67-5"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),Ms=s.div.withConfig({displayName:"CardContent",componentId:"sc-mv9m67-6"})([""]),_s=s.div.withConfig({displayName:"CardFooter",componentId:"sc-mv9m67-7"})(["margin-top:",";padding-top:",";border-top:1px solid ",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.md,({theme:e})=>e.colors.border),Ps=s.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-mv9m67-8"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:e})=>`${e.colors.background}80`),Fs=s.div.withConfig({displayName:"ErrorContainer",componentId:"sc-mv9m67-9"})(["padding:",";background-color:","10;border-radius:",";color:",";margin-bottom:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.error,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.error,({theme:e})=>e.spacing.md),Os=s.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-mv9m67-10"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary),Pr=({children:e,title:t="",subtitle:r="",bordered:o=!0,variant:i="default",padding:c="medium",className:a="",footer:l,actions:d,isLoading:f=!1,hasError:u=!1,errorMessage:m="An error occurred",clickable:y=!1,onClick:h,...g})=>{const b=t||r||d;return n.jsxs(Rs,{bordered:o,variant:i,padding:c,clickable:y,className:a,onClick:y?h:void 0,...g,children:[f&&n.jsx(Ps,{children:n.jsx(Os,{})}),b&&n.jsxs(js,{children:[n.jsxs(ks,{children:[t&&n.jsx(Ns,{children:t}),r&&n.jsx(Ds,{children:r})]}),d&&n.jsx(Ls,{children:d})]}),u&&n.jsx(Fs,{children:n.jsx("p",{children:m})}),n.jsx(Ms,{children:e}),l&&n.jsx(_s,{children:l})]})},$s=s.h3.withConfig({displayName:"Title",componentId:"sc-1jsjvya-0"})(["margin:0 0 "," 0;color:",";font-weight:",";",""],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontWeights.semibold,({size:e,theme:t})=>{const r={small:t.fontSizes.md,medium:t.fontSizes.lg,large:t.fontSizes.xl};return s.css(["font-size:",";"],r[e])}),As=s.p.withConfig({displayName:"Description",componentId:"sc-1jsjvya-1"})(["margin:0 0 "," 0;color:",";",""],({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.textSecondary,({size:e,theme:t})=>{const r={small:t.fontSizes.sm,medium:t.fontSizes.md,large:t.fontSizes.lg};return s.css(["font-size:",";"],r[e])}),zs={default:s.css(["background-color:transparent;"]),compact:s.css(["background-color:transparent;text-align:left;align-items:flex-start;"]),card:s.css(["background-color:",";border-radius:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.sm)},Vs=s.div.withConfig({displayName:"Container",componentId:"sc-1jsjvya-2"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;width:100%;"," ",""],({variant:e})=>zs[e],({size:e,theme:t})=>{switch(e){case"small":return s.css(["padding:",";min-height:120px;"],t.spacing.md);case"large":return s.css(["padding:",";min-height:300px;"],t.spacing.xl);default:return s.css(["padding:",";min-height:200px;"],t.spacing.lg)}}),Gs=s.div.withConfig({displayName:"IconContainer",componentId:"sc-1jsjvya-3"})(["margin-bottom:",";",""],({theme:e})=>e.spacing.md,({size:e,theme:t})=>{const r={small:"32px",medium:"48px",large:"64px"};return s.css(["font-size:",";svg{width:",";height:",";color:",";}"],r[e],r[e],r[e],t.colors.textSecondary)}),Bs=s.div.withConfig({displayName:"ActionContainer",componentId:"sc-1jsjvya-4"})(["margin-top:",";"],({theme:e})=>e.spacing.md),Hs=s.div.withConfig({displayName:"ChildrenContainer",componentId:"sc-1jsjvya-5"})(["margin-top:",";width:100%;"],({theme:e})=>e.spacing.lg),Ct=({title:e="",description:t="",icon:r,actionText:o="",onAction:i,variant:c="default",size:a="medium",className:l="",children:d})=>n.jsxs(Vs,{variant:c,size:a,className:l,children:[r&&n.jsx(Gs,{size:a,children:r}),e&&n.jsx($s,{size:a,children:e}),t&&n.jsx(As,{size:a,children:t}),o&&i&&n.jsx(Bs,{children:n.jsx(se,{variant:"primary",size:a==="small"?"small":"medium",onClick:i,children:o})}),d&&n.jsx(Hs,{children:d})]}),or=s.div.withConfig({displayName:"ErrorContainer",componentId:"sc-jxqb9h-0"})(["padding:1.5rem;margin:",";border-radius:0.5rem;background-color:",";color:#ffffff;",""],e=>e.isAppLevel?"0":"1rem 0",e=>e.isAppLevel?"#1a1f2c":"#f44336",e=>e.isAppLevel&&`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  `),qs=s.div.withConfig({displayName:"ErrorCard",componentId:"sc-jxqb9h-1"})(["background-color:#252a37;border-radius:0.5rem;padding:2rem;width:100%;box-shadow:0 4px 6px rgba(0,0,0,0.1);"]),nr=s.h3.withConfig({displayName:"ErrorTitle",componentId:"sc-jxqb9h-2"})(["margin-top:0;font-size:",";font-weight:700;text-align:",";"],e=>e.isAppLevel?"1.5rem":"1.25rem",e=>e.isAppLevel?"center":"left"),Ze=s.p.withConfig({displayName:"ErrorMessage",componentId:"sc-jxqb9h-3"})(["margin-bottom:1rem;text-align:",";"],e=>e.isAppLevel?"center":"left"),sr=s.details.withConfig({displayName:"ErrorDetails",componentId:"sc-jxqb9h-4"})(["margin-bottom:1rem;summary{cursor:pointer;color:#2196f3;font-weight:500;margin-bottom:0.5rem;}"]),ir=s.pre.withConfig({displayName:"ErrorStack",componentId:"sc-jxqb9h-5"})(["font-size:0.875rem;background-color:rgba(0,0,0,0.1);padding:0.5rem;border-radius:0.25rem;overflow:auto;max-height:200px;"]),Us=s.div.withConfig({displayName:"ButtonContainer",componentId:"sc-jxqb9h-6"})(["display:flex;gap:0.5rem;justify-content:flex-start;"]),Fr=s.button.withConfig({displayName:"RetryButton",componentId:"sc-jxqb9h-7"})(["background-color:#ffffff;color:#f44336;border:none;border-radius:0.25rem;padding:0.5rem 1rem;font-weight:700;cursor:pointer;transition:background-color 0.2s;&:hover{background-color:#f5f5f5;}"]),Ys=s.button.withConfig({displayName:"SkipButton",componentId:"sc-jxqb9h-8"})(["padding:0.5rem 1rem;background-color:transparent;color:#ffffff;border:1px solid #ffffff;border-radius:0.25rem;font-size:0.875rem;font-weight:500;cursor:pointer;transition:all 0.2s;&:hover{background-color:rgba(255,255,255,0.1);}"]),Ws=s(Fr).withConfig({displayName:"ReloadButton",componentId:"sc-jxqb9h-9"})(["margin-top:1rem;width:100%;"]),Ks=({error:e,resetError:t,isAppLevel:r,name:o,onSkip:i})=>{const c=()=>{window.location.reload()};return r?n.jsx(or,{isAppLevel:!0,children:n.jsxs(qs,{children:[n.jsx(nr,{isAppLevel:!0,children:"Something went wrong"}),n.jsx(Ze,{isAppLevel:!0,children:"We're sorry, but an unexpected error has occurred. Please try reloading the application."}),n.jsxs(sr,{children:[n.jsx("summary",{children:"Technical Details"}),n.jsx(Ze,{children:e.message}),e.stack&&n.jsx(ir,{children:e.stack})]}),n.jsx(Ws,{onClick:c,children:"Reload Application"})]})}):n.jsxs(or,{children:[n.jsx(nr,{children:o?`Error in ${o}`:"Something went wrong"}),n.jsx(Ze,{children:o?`We encountered a problem while loading ${o}. You can try again${i?" or skip this feature":""}.`:"An unexpected error occurred. Please try again."}),n.jsxs(sr,{children:[n.jsx("summary",{children:"Technical Details"}),n.jsx(Ze,{children:e.message}),e.stack&&n.jsx(ir,{children:e.stack})]}),n.jsxs(Us,{children:[n.jsx(Fr,{onClick:t,children:"Try Again"}),i&&n.jsx(Ys,{onClick:i,children:"Skip This Feature"})]})]})};class Or extends x.Component{constructor(r){super(r);be(this,"resetError",()=>{this.setState({hasError:!1,error:null})});this.state={hasError:!1,error:null}}static getDerivedStateFromError(r){return{hasError:!0,error:r}}componentDidCatch(r,o){const{name:i}=this.props,c=i?`ErrorBoundary(${i})`:"ErrorBoundary";console.error(`Error caught by ${c}:`,r,o),this.props.onError&&this.props.onError(r,o)}componentDidUpdate(r){this.state.hasError&&this.props.resetOnPropsChange&&r.children!==this.props.children&&this.resetError()}componentWillUnmount(){this.state.hasError&&this.props.resetOnUnmount&&this.resetError()}render(){const{hasError:r,error:o}=this.state,{children:i,fallback:c,name:a,isFeatureBoundary:l,onSkip:d}=this.props;return r&&o?typeof c=="function"?c({error:o,resetError:this.resetError}):c||n.jsx(Ks,{error:o,resetError:this.resetError,isAppLevel:!l,name:a,onSkip:d}):i}}const It=({isAppLevel:e=!1,isFeatureBoundary:t=!1,children:r,...o})=>{const i=e?"app":t?"feature":"component",c={resetOnPropsChange:i!=="app",resetOnUnmount:i!=="app",isFeatureBoundary:i==="feature"};return n.jsx(Or,{...c,...o,children:r})},Qs=e=>n.jsx(It,{isAppLevel:!0,...e}),Xs=({featureName:e,children:t,...r})=>n.jsx(It,{isFeatureBoundary:!0,name:e,children:t,...r}),Js=s.div.withConfig({displayName:"TabContainer",componentId:"sc-lgz9vh-0"})(["display:flex;flex-direction:column;width:100%;"]),Zs=s.div.withConfig({displayName:"TabList",componentId:"sc-lgz9vh-1"})(["display:flex;border-bottom:1px solid ",";margin-bottom:",";"],({theme:e})=>e.colors.border,({theme:e})=>e.spacing.md),ei=s.button.withConfig({displayName:"TabButton",componentId:"sc-lgz9vh-2"})(["padding:"," ",";background:none;border:none;border-bottom:2px solid ",";color:",";font-weight:",";cursor:pointer;transition:all ",";&:hover{color:",";}&:focus{outline:none;color:",";}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md,({active:e,theme:t})=>e?t.colors.primary:"transparent",({active:e,theme:t})=>e?t.colors.primary:t.colors.textSecondary,({active:e,theme:t})=>e?t.fontWeights.semibold:t.fontWeights.regular,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),ti=s.div.withConfig({displayName:"TabContent",componentId:"sc-lgz9vh-3"})(["padding:"," 0;"],({theme:e})=>e.spacing.sm),ri=({tabs:e,defaultTab:t,className:r,activeTab:o,onTabClick:i})=>{var f;const[c,a]=x.useState(t||e[0].id),l=o!==void 0?o:c,d=(u,m)=>{u.preventDefault(),u.stopPropagation(),i?i(m):a(m)};return n.jsxs(Js,{className:r,children:[n.jsx(Zs,{children:e.map(u=>n.jsx(ei,{active:l===u.id,onClick:m=>d(m,u.id),type:"button",form:"",tabIndex:0,"data-tab-id":u.id,children:u.label},u.id))}),n.jsx(ti,{children:(f=e.find(u=>u.id===l))==null?void 0:f.content})]})},oi=ri,$r={required:(e="This field is required")=>({validate:t=>typeof t=="string"?t.trim().length>0:typeof t=="number"?!isNaN(t):Array.isArray(t)?t.length>0:t!=null&&t!==void 0,message:e}),email:(e="Please enter a valid email address")=>({validate:t=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t),message:e}),minLength:(e,t)=>({validate:r=>r.length>=e,message:t||`Must be at least ${e} characters`}),maxLength:(e,t)=>({validate:r=>r.length<=e,message:t||`Must be no more than ${e} characters`}),min:(e,t)=>({validate:r=>r>=e,message:t||`Must be at least ${e}`}),max:(e,t)=>({validate:r=>r<=e,message:t||`Must be no more than ${e}`}),pattern:(e,t)=>({validate:r=>e.test(r),message:t})},Ar=(e={})=>{const{initialValue:t="",required:r=!1,type:o="text",validationRules:i=[],validateOnChange:c=!1,validateOnBlur:a=!0,transform:l}=e,d=x.useMemo(()=>{const R=[...i];return r&&!i.some(N=>N.message.toLowerCase().includes("required"))&&R.unshift($r.required()),R},[r,i]),[f,u]=x.useState(t),[m,y]=x.useState(null),[h,g]=x.useState(!1),[b,T]=x.useState(!1),w=x.useMemo(()=>f!==t,[f,t]),C=x.useMemo(()=>m===null&&!b,[m,b]),L=x.useMemo(()=>m===null&&!b,[m,b]),$=x.useCallback(async()=>{T(!0);try{for(const R of d)if(!R.validate(f))return y(R.message),T(!1),!1;return y(null),T(!1),!0}catch{return y("Validation error occurred"),T(!1),!1}},[f,d]),j=x.useCallback(()=>{u(t),y(null),g(!1),T(!1)},[t]),k=x.useCallback(R=>{let N;o==="number"?N=parseFloat(R.target.value)||0:N=R.target.value,l&&(N=l(N)),u(N),c&&h&&setTimeout(()=>$(),0)},[o,l,c,h,$]),M=x.useCallback(R=>{g(!0),a&&$()},[a,$]);return{value:f,error:m,touched:h,dirty:w,valid:C,isValid:L,validating:b,setValue:u,setError:y,setTouched:g,validate:$,reset:j,handleChange:k,handleBlur:M}},ni=s.div.withConfig({displayName:"FieldContainer",componentId:"sc-oh07s1-0"})(["display:flex;flex-direction:column;gap:",";width:100%;margin-bottom:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"}),si=s.label.withConfig({displayName:"Label",componentId:"sc-oh07s1-1"})(["font-size:",";font-weight:",";color:",";",""],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"},({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.medium)||"500"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({$required:e})=>e&&s.css(["&::after{content:' *';color:",";}"],({theme:t})=>{var r;return((r=t.colors)==null?void 0:r.error)||"#dc2626"})),Rt=s.css(["width:100%;border:1px solid ",";border-radius:",";background-color:",";color:",";font-size:",";padding:",";transition:",";&:focus{outline:none;border-color:",";box-shadow:0 0 0 2px ",";}&:disabled{background-color:",";color:",";cursor:not-allowed;}&::placeholder{color:",";}"],({theme:e,$hasError:t})=>{var r,o;return t?((r=e.colors)==null?void 0:r.error)||"#dc2626":((o=e.colors)==null?void 0:o.border)||"#4b5563"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.sm)||"4px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"#1f2937"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e,$size:t})=>{var r,o,i;switch(t){case"sm":return((r=e.fontSizes)==null?void 0:r.sm)||"14px";case"lg":return((o=e.fontSizes)==null?void 0:o.lg)||"18px";default:return((i=e.fontSizes)==null?void 0:i.md)||"16px"}},({theme:e,$size:t})=>{var r,o,i,c,a,l;switch(t){case"sm":return`${((r=e.spacing)==null?void 0:r.xs)||"4px"} ${((o=e.spacing)==null?void 0:o.sm)||"8px"}`;case"lg":return`${((i=e.spacing)==null?void 0:i.md)||"12px"} ${((c=e.spacing)==null?void 0:c.lg)||"16px"}`;default:return`${((a=e.spacing)==null?void 0:a.sm)||"8px"} ${((l=e.spacing)==null?void 0:l.md)||"12px"}`}},({theme:e})=>{var t;return((t=e.transitions)==null?void 0:t.fast)||"all 0.2s ease"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#dc2626"},({theme:e})=>{var t;return(t=e.colors)!=null&&t.primary?`${e.colors.primary}20`:"rgba(220, 38, 38, 0.2)"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.chartGrid)||"#374151"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"}),ii=s.input.withConfig({displayName:"StyledInput",componentId:"sc-oh07s1-2"})(["",""],Rt),ai=s.textarea.withConfig({displayName:"StyledTextarea",componentId:"sc-oh07s1-3"})([""," resize:vertical;min-height:80px;"],Rt),ci=s.select.withConfig({displayName:"StyledSelect",componentId:"sc-oh07s1-4"})([""," cursor:pointer;"],Rt),li=s.div.withConfig({displayName:"ErrorMessage",componentId:"sc-oh07s1-5"})(["font-size:",";color:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"12px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.error)||"#dc2626"}),di=s.div.withConfig({displayName:"HelpText",componentId:"sc-oh07s1-6"})(["font-size:",";color:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"12px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"}),ui=e=>{const{name:t,label:r,placeholder:o,disabled:i=!1,className:c,size:a="md",helpText:l,inputType:d="input",options:f=[],rows:u=4,onChange:m,onBlur:y,...h}=e,g=Ar({...h,validateOnBlur:!0});x.useEffect(()=>{m&&m(g.value)},[g.value,m]);const b=C=>{g.handleBlur(C),y&&y()},T={id:t,name:t,value:g.value,onChange:g.handleChange,onBlur:b,disabled:i,placeholder:o,$hasError:!!g.error,$disabled:i,$size:a},w=()=>{switch(d){case"textarea":return n.jsx(ai,{...T,rows:u});case"select":return n.jsxs(ci,{...T,children:[o&&n.jsx("option",{value:"",disabled:!0,children:o}),f.map(C=>n.jsx("option",{value:C.value,children:C.label},C.value))]});default:return n.jsx(ii,{...T,type:h.type||"text"})}};return n.jsxs(ni,{className:c,children:[r&&n.jsx(si,{htmlFor:t,$required:!!h.required,children:r}),w(),g.error&&g.touched&&n.jsx(li,{role:"alert",children:g.error}),l&&!g.error&&n.jsx(di,{children:l})]})},pi=ui,fi={string:e=>(t,r)=>{const o=String(t[e]||""),i=String(r[e]||"");return o.localeCompare(i)},number:e=>(t,r)=>{const o=Number(t[e])||0,i=Number(r[e])||0;return o-i},date:e=>(t,r)=>{const o=new Date(t[e]).getTime(),i=new Date(r[e]).getTime();return o-i},boolean:e=>(t,r)=>{const o=!!t[e],i=!!r[e];return Number(o)-Number(i)}},zr=({data:e,columns:t,defaultSort:r})=>{const[o,i]=x.useState(r?{field:r.field,direction:r.direction}:null),c=x.useCallback(u=>{const m=t.find(y=>y.field===u);m!=null&&m.sortable&&i(y=>{var h;if((y==null?void 0:y.field)===u)return{field:u,direction:y.direction==="asc"?"desc":"asc"};{const g=typeof((h=e[0])==null?void 0:h[u])=="number"?"desc":"asc";return{field:u,direction:g}}})},[t,e]),a=x.useMemo(()=>{if(!o)return e;const u=t.find(y=>y.field===o.field);return u?[...e].sort((y,h)=>{let g=0;if(u.sortFn)g=u.sortFn(y,h);else{const b=y[o.field],T=h[o.field];typeof b=="string"&&typeof T=="string"?g=b.localeCompare(T):typeof b=="number"&&typeof T=="number"?g=b-T:g=String(b).localeCompare(String(T))}return o.direction==="asc"?g:-g}):e},[e,o,t]),l=x.useCallback(u=>!o||o.field!==u?null:o.direction==="asc"?"↑":"↓",[o]),d=x.useCallback(u=>(o==null?void 0:o.field)===u,[o]),f=x.useCallback(u=>(o==null?void 0:o.field)===u?o.direction:null,[o]);return{sortedData:a,sortConfig:o,handleSort:c,getSortIcon:l,isSorted:d,getSortDirection:f}},ar=s.div.withConfig({displayName:"Container",componentId:"sc-13j9udn-0"})(["overflow-x:auto;border-radius:",";border:1px solid ",";"],({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.md)||"8px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#4b5563"}),mi=s.table.withConfig({displayName:"Table",componentId:"sc-13j9udn-1"})(["width:100%;border-collapse:collapse;font-size:",";"],({theme:e,$size:t})=>{var r,o,i;switch(t){case"sm":return((r=e.fontSizes)==null?void 0:r.xs)||"12px";case"lg":return((o=e.fontSizes)==null?void 0:o.md)||"16px";default:return((i=e.fontSizes)==null?void 0:i.sm)||"14px"}}),gi=s.thead.withConfig({displayName:"TableHead",componentId:"sc-13j9udn-2"})(["background-color:",";border-bottom:2px solid ",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"#1f2937"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#4b5563"}),hi=s.tbody.withConfig({displayName:"TableBody",componentId:"sc-13j9udn-3"})([""]),cr=s.tr.withConfig({displayName:"TableRow",componentId:"sc-13j9udn-4"})([""," "," "," border-bottom:1px solid ",";"],({$striped:e,theme:t})=>{var r;return e&&s.css(["&:nth-child(even){background-color:",";}"],((r=t.colors)==null?void 0:r.background)||"#0f0f0f")},({$hoverable:e,theme:t})=>{var r;return e&&s.css(["&:hover{background-color:",";}"],((r=t.colors)==null?void 0:r.surface)||"#1f2937")},({$clickable:e})=>e&&s.css(["cursor:pointer;"]),({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#4b5563"}),yi=s.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-13j9udn-5"})(["text-align:left;font-weight:",";color:",";cursor:",";user-select:none;transition:",";padding:",";&:hover{","}&:focus{outline:2px solid ",";outline-offset:-2px;}"],({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.semibold)||"600"},({theme:e,$active:t})=>{var r,o;return t?((r=e.colors)==null?void 0:r.primary)||"#dc2626":((o=e.colors)==null?void 0:o.textPrimary)||"#ffffff"},({$sortable:e})=>e?"pointer":"default",({theme:e})=>{var t;return((t=e.transitions)==null?void 0:t.fast)||"all 0.2s ease"},({theme:e,$size:t})=>{var r,o,i,c,a,l;switch(t){case"sm":return`${((r=e.spacing)==null?void 0:r.xs)||"4px"} ${((o=e.spacing)==null?void 0:o.sm)||"8px"}`;case"lg":return`${((i=e.spacing)==null?void 0:i.md)||"12px"} ${((c=e.spacing)==null?void 0:c.lg)||"16px"}`;default:return`${((a=e.spacing)==null?void 0:a.sm)||"8px"} ${((l=e.spacing)==null?void 0:l.md)||"12px"}`}},({$sortable:e,theme:t})=>{var r;return e&&s.css(["color:",";"],((r=t.colors)==null?void 0:r.primary)||"#dc2626")},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#dc2626"}),bi=s.td.withConfig({displayName:"TableCell",componentId:"sc-13j9udn-6"})(["padding:",";color:",";"],({theme:e,$size:t})=>{var r,o,i,c,a,l;switch(t){case"sm":return`${((r=e.spacing)==null?void 0:r.xs)||"4px"} ${((o=e.spacing)==null?void 0:o.sm)||"8px"}`;case"lg":return`${((i=e.spacing)==null?void 0:i.md)||"12px"} ${((c=e.spacing)==null?void 0:c.lg)||"16px"}`;default:return`${((a=e.spacing)==null?void 0:a.sm)||"8px"} ${((l=e.spacing)==null?void 0:l.md)||"12px"}`}},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"}),xi=s.span.withConfig({displayName:"SortIcon",componentId:"sc-13j9udn-7"})(["display:inline-block;margin-left:",";font-size:",";&::after{content:'","';}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"},({$direction:e})=>e==="asc"?"↑":"↓"),vi=s.div.withConfig({displayName:"EmptyState",componentId:"sc-13j9udn-8"})(["padding:",";text-align:center;color:",";font-style:italic;"],({theme:e,$size:t})=>{var r,o,i;switch(t){case"sm":return((r=e.spacing)==null?void 0:r.md)||"12px";case"lg":return((o=e.spacing)==null?void 0:o.xl)||"24px";default:return((i=e.spacing)==null?void 0:i.lg)||"16px"}},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"}),Si=({data:e,columns:t,className:r,emptyMessage:o="No data available",defaultSort:i,renderCell:c,onRowClick:a,size:l="md",striped:d=!0,hoverable:f=!0})=>{const{sortedData:u,handleSort:m,getSortIcon:y,isSorted:h}=zr({data:e,columns:t,defaultSort:i});return e.length===0?n.jsx(ar,{className:r,children:n.jsx(vi,{$size:l,children:o})}):n.jsx(ar,{className:r,children:n.jsxs(mi,{$size:l,$striped:d,$hoverable:f,children:[n.jsx(gi,{children:n.jsx(cr,{$striped:!1,$hoverable:!1,$clickable:!1,children:t.map(g=>n.jsxs(yi,{$sortable:g.sortable||!1,$active:h(g.field),$size:l,onClick:()=>g.sortable&&m(g.field),tabIndex:g.sortable?0:-1,onKeyDown:b=>{g.sortable&&(b.key==="Enter"||b.key===" ")&&(b.preventDefault(),m(g.field))},role:g.sortable?"button":void 0,"aria-sort":h(g.field)?y(g.field)==="↑"?"ascending":"descending":void 0,children:[g.label,h(g.field)&&n.jsx(xi,{$direction:y(g.field)==="↑"?"asc":"desc"})]},String(g.field)))})}),n.jsx(hi,{children:u.map((g,b)=>n.jsx(cr,{$striped:d,$hoverable:f,$clickable:!!a,onClick:()=>a==null?void 0:a(g,b),tabIndex:a?0:-1,onKeyDown:T=>{a&&(T.key==="Enter"||T.key===" ")&&(T.preventDefault(),a(g,b))},role:a?"button":void 0,children:t.map(T=>{const w=g[T.field];return n.jsx(bi,{$size:l,children:c?c(w,g,T):String(w)},String(T.field))})},b))})]})})},wi=Si,Ci=s.div.withConfig({displayName:"FieldContainer",componentId:"sc-i922jg-0"})(["display:flex;flex-direction:column;margin-bottom:",";"],({theme:e})=>e.spacing.md),Ti=s.label.withConfig({displayName:"Label",componentId:"sc-i922jg-1"})(["font-size:",";font-weight:500;margin-bottom:",";color:",";.required-indicator{color:",";margin-left:",";}"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.xxs,({theme:e,hasError:t})=>t?e.colors.error:e.colors.textPrimary,({theme:e})=>e.colors.error,({theme:e})=>e.spacing.xxs),Ei=s.div.withConfig({displayName:"HelperText",componentId:"sc-i922jg-2"})(["font-size:",";color:",";margin-top:",";"],({theme:e})=>e.fontSizes.xs,({theme:e,hasError:t})=>t?e.colors.error:e.colors.textSecondary,({theme:e})=>e.spacing.xxs),Ii=({children:e,label:t,helperText:r,required:o=!1,error:i,className:c,id:a,...l})=>{const d=a||`field-${Math.random().toString(36).substr(2,9)}`,f=x.Children.map(e,u=>x.isValidElement(u)?x.cloneElement(u,{id:d,required:o,error:i}):u);return n.jsxs(Ci,{className:c,...l,children:[n.jsxs(Ti,{htmlFor:d,hasError:!!i,children:[t,o&&n.jsx("span",{className:"required-indicator",children:"*"})]}),f,(r||i)&&n.jsx(Ei,{hasError:!!i,children:i||r})]})},Ri=s.keyframes(["from{opacity:0;}to{opacity:1;}"]),ji=s.keyframes(["from{transform:translateY(-20px);opacity:0;}to{transform:translateY(0);opacity:1;}"]),ki=s.div.withConfig({displayName:"Backdrop",componentId:"sc-1cuqxtr-0"})(["position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:",";animation:"," 0.2s ease-out;"],({zIndex:e})=>e||1e3,Ri),Ni=s.div.withConfig({displayName:"ModalContainer",componentId:"sc-1cuqxtr-1"})(["background-color:",";border-radius:",";box-shadow:",";display:flex;flex-direction:column;max-height:",";width:",";max-width:95vw;animation:"," 0.2s ease-out;position:relative;"," ",""],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.lg,({size:e})=>e==="fullscreen"?"100vh":"90vh",({size:e})=>{switch(e){case"small":return"400px";case"medium":return"600px";case"large":return"800px";case"fullscreen":return"100vw";default:return"600px"}},ji,({size:e})=>e==="fullscreen"&&s.css(["height:100vh;border-radius:0;"]),({centered:e})=>e&&s.css(["margin:auto;"])),Di=s.div.withConfig({displayName:"ModalHeader",componentId:"sc-1cuqxtr-2"})(["display:flex;justify-content:space-between;align-items:center;padding:",";border-bottom:1px solid ",";"],({theme:e})=>`${e.spacing.md} ${e.spacing.lg}`,({theme:e})=>e.colors.border),Li=s.h3.withConfig({displayName:"ModalTitle",componentId:"sc-1cuqxtr-3"})(["margin:0;font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.fontWeights.semibold,({theme:e})=>e.colors.textPrimary),Mi=s.button.withConfig({displayName:"CloseButton",componentId:"sc-1cuqxtr-4"})(["background:none;border:none;cursor:pointer;font-size:",";color:",";padding:0;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:",";&:hover{background-color:",";}&:focus{outline:none;box-shadow:0 0 0 2px ","33;}"],({theme:e})=>e.fontSizes.xl,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary),_i=s.div.withConfig({displayName:"ModalContent",componentId:"sc-1cuqxtr-5"})(["padding:",";",""],({theme:e})=>e.spacing.lg,({scrollable:e})=>e&&s.css(["overflow-y:auto;flex:1;"])),Pi=s.div.withConfig({displayName:"ModalFooter",componentId:"sc-1cuqxtr-6"})(["display:flex;justify-content:flex-end;gap:",";padding:",";border-top:1px solid ",";"],({theme:e})=>e.spacing.md,({theme:e})=>`${e.spacing.md} ${e.spacing.lg}`,({theme:e})=>e.colors.border),Fi=({isOpen:e,title:t="",children:r,onClose:o,size:i="medium",closeOnOutsideClick:c=!0,showCloseButton:a=!0,footer:l,hasFooter:d=!0,primaryActionText:f="",onPrimaryAction:u,primaryActionDisabled:m=!1,primaryActionLoading:y=!1,secondaryActionText:h="",onSecondaryAction:g,secondaryActionDisabled:b=!1,className:T="",zIndex:w=1e3,centered:C=!0,scrollable:L=!0})=>{const $=x.useRef(null);x.useEffect(()=>{const R=N=>{N.key==="Escape"&&e&&c&&o()};return document.addEventListener("keydown",R),()=>{document.removeEventListener("keydown",R)}},[e,o,c]);const j=R=>{$.current&&!$.current.contains(R.target)&&c&&o()};x.useEffect(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="",()=>{document.body.style.overflow=""}),[e]);const k=n.jsxs(n.Fragment,{children:[h&&n.jsx(se,{variant:"outline",onClick:g,disabled:b,children:h}),f&&n.jsx(se,{onClick:u,disabled:m,loading:y,children:f})]});if(!e)return null;const M=n.jsx(ki,{onClick:j,zIndex:w,children:n.jsxs(Ni,{ref:$,size:i,className:T,centered:C,scrollable:L,onClick:R=>R.stopPropagation(),children:[(t||a)&&n.jsxs(Di,{children:[t&&n.jsx(Li,{children:t}),a&&n.jsx(Mi,{onClick:o,"aria-label":"Close",children:"×"})]}),n.jsx(_i,{scrollable:L,children:r}),d&&(l||f||h)&&n.jsx(Pi,{children:l||k})]})});return ko.createPortal(M,document.body)},Oi=s.div.withConfig({displayName:"TableContainer",componentId:"sc-4as3uq-0"})(["width:100%;overflow:auto;"," ",""],({height:e})=>e&&`height: ${e};`,({scrollable:e})=>e&&"overflow-x: auto;"),$i=s.table.withConfig({displayName:"StyledTable",componentId:"sc-4as3uq-1"})(["width:100%;border-collapse:separate;border-spacing:0;font-size:",";"," ",""],({theme:e})=>e.fontSizes.sm,({bordered:e,theme:t})=>e&&s.css(["border:1px solid ",";border-radius:",";"],t.colors.border,t.borderRadius.sm),({compact:e,theme:t})=>e?s.css(["th,td{padding:"," ",";}"],t.spacing.xs,t.spacing.sm):s.css(["th,td{padding:"," ",";}"],t.spacing.sm,t.spacing.md)),Ai=s.thead.withConfig({displayName:"TableHeader",componentId:"sc-4as3uq-2"})(["",""],({stickyHeader:e})=>e&&s.css(["position:sticky;top:0;z-index:1;"])),zi=s.tr.withConfig({displayName:"TableHeaderRow",componentId:"sc-4as3uq-3"})(["background-color:",";"],({theme:e})=>e.colors.background),Vi=s.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-4as3uq-4"})(["text-align:",";font-weight:",";color:",";border-bottom:1px solid ",";white-space:nowrap;"," "," ",""],({align:e})=>e||"left",({theme:e})=>e.fontWeights.semibold,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.colors.border,({width:e})=>e&&`width: ${e};`,({sortable:e})=>e&&s.css(["cursor:pointer;user-select:none;&:hover{background-color:","aa;}"],({theme:t})=>t.colors.background),({isSorted:e,theme:t})=>e&&s.css(["color:",";"],t.colors.primary)),Gi=s.span.withConfig({displayName:"SortIcon",componentId:"sc-4as3uq-5"})(["display:inline-block;margin-left:",";&::after{content:'","';}"],({theme:e})=>e.spacing.xs,({direction:e})=>e==="asc"?"↑":e==="desc"?"↓":"↕"),Bi=s.tbody.withConfig({displayName:"TableBody",componentId:"sc-4as3uq-6"})([""]),Hi=s.tr.withConfig({displayName:"TableRow",componentId:"sc-4as3uq-7"})([""," "," "," ",""],({striped:e,theme:t,isSelected:r})=>e&&!r&&s.css(["&:nth-child(even){background-color:","50;}"],t.colors.background),({hoverable:e,theme:t,isSelected:r})=>e&&!r&&s.css(["&:hover{background-color:","aa;}"],t.colors.background),({isSelected:e,theme:t})=>e&&s.css(["background-color:","15;"],t.colors.primary),({isClickable:e})=>e&&s.css(["cursor:pointer;"])),qi=s.td.withConfig({displayName:"TableCell",componentId:"sc-4as3uq-8"})(["text-align:",";border-bottom:1px solid ",";color:",";"],({align:e})=>e||"left",({theme:e})=>e.colors.border,({theme:e})=>e.colors.textPrimary),Ui=s.div.withConfig({displayName:"EmptyState",componentId:"sc-4as3uq-9"})(["padding:",";text-align:center;color:",";"],({theme:e})=>e.spacing.xl,({theme:e})=>e.colors.textSecondary),Yi=s.div.withConfig({displayName:"PaginationContainer",componentId:"sc-4as3uq-10"})(["display:flex;justify-content:space-between;align-items:center;padding:"," 0;font-size:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.fontSizes.sm),Wi=s.div.withConfig({displayName:"PageInfo",componentId:"sc-4as3uq-11"})(["color:",";"],({theme:e})=>e.colors.textSecondary),Ki=s.div.withConfig({displayName:"PaginationControls",componentId:"sc-4as3uq-12"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.xs),Qi=s.div.withConfig({displayName:"PageSizeSelector",componentId:"sc-4as3uq-13"})(["display:flex;align-items:center;gap:",";margin-right:",";"],({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md),Xi=s.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-4as3uq-14"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:e})=>`${e.colors.background}80`),Ji=s.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-4as3uq-15"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary);function Zi({columns:e,data:t,isLoading:r=!1,bordered:o=!0,striped:i=!0,hoverable:c=!0,compact:a=!1,stickyHeader:l=!1,height:d,onRowClick:f,isRowSelected:u,onSort:m,sortColumn:y,sortDirection:h,pagination:g=!1,currentPage:b=1,pageSize:T=10,totalRows:w=0,onPageChange:C,onPageSizeChange:L,className:$,emptyMessage:j="No data available",scrollable:k=!0}){const M=x.useMemo(()=>e.filter(D=>!D.hidden),[e]),R=x.useMemo(()=>Math.ceil(w/T),[w,T]),N=x.useMemo(()=>{if(!g)return t;const D=(b-1)*T,G=D+T;return w>0&&t.length<=T?t:t.slice(D,G)},[t,g,b,T,w]),W=D=>{if(!m)return;m(D,y===D&&h==="asc"?"desc":"asc")},q=D=>{D<1||D>R||!C||C(D)};return n.jsxs("div",{style:{position:"relative"},children:[r&&n.jsx(Xi,{children:n.jsx(Ji,{})}),n.jsx(Oi,{height:d,scrollable:k,children:n.jsxs($i,{bordered:o,striped:i,compact:a,className:$,children:[n.jsx(Ai,{stickyHeader:l,children:n.jsx(zi,{children:M.map(D=>n.jsxs(Vi,{sortable:D.sortable,isSorted:y===D.id,align:D.align,width:D.width,onClick:()=>D.sortable&&W(D.id),children:[D.header,D.sortable&&n.jsx(Gi,{direction:y===D.id?h:void 0})]},D.id))})}),n.jsx(Bi,{children:N.length>0?N.map((D,G)=>n.jsx(Hi,{hoverable:c,striped:i,isSelected:u?u(D,G):!1,isClickable:!!f,onClick:()=>f&&f(D,G),children:M.map(J=>n.jsx(qi,{align:J.align,children:J.cell(D,G)},J.id))},G)):n.jsx("tr",{children:n.jsx("td",{colSpan:M.length,children:n.jsx(Ui,{children:j})})})})]})}),g&&R>0&&n.jsxs(Yi,{children:[n.jsxs(Wi,{children:["Showing ",Math.min((b-1)*T+1,w)," to"," ",Math.min(b*T,w)," of ",w," entries"]}),n.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[L&&n.jsxs(Qi,{children:[n.jsx("span",{children:"Show"}),n.jsx("select",{value:T,onChange:D=>L(Number(D.target.value)),style:{padding:"4px 8px",borderRadius:"4px",border:"1px solid #ccc"},children:[10,25,50,100].map(D=>n.jsx("option",{value:D,children:D},D))}),n.jsx("span",{children:"entries"})]}),n.jsxs(Ki,{children:[n.jsx(se,{size:"small",variant:"outline",onClick:()=>q(1),disabled:b===1,children:"First"}),n.jsx(se,{size:"small",variant:"outline",onClick:()=>q(b-1),disabled:b===1,children:"Prev"}),n.jsx(se,{size:"small",variant:"outline",onClick:()=>q(b+1),disabled:b===R,children:"Next"}),n.jsx(se,{size:"small",variant:"outline",onClick:()=>q(R),disabled:b===R,children:"Last"})]})]})]})]})}const ce={[_.MORNING_BREAKOUT]:{type:_.MORNING_BREAKOUT,name:"9:50-10:10 Macro",timeRange:{start:"09:50:00",end:"10:10:00"},description:"Morning breakout period - high volatility after market open",characteristics:["High Volume","Breakout Setups","Gap Fills","Opening Range"],volatilityLevel:5,volumeLevel:5,isHighProbability:!0},[_.MID_MORNING_REVERSION]:{type:_.MID_MORNING_REVERSION,name:"10:50-11:10 Macro",timeRange:{start:"10:50:00",end:"11:10:00"},description:"Mid-morning reversion period - mean reversion opportunities",characteristics:["Mean Reversion","Pullback Setups","Support/Resistance Tests"],volatilityLevel:3,volumeLevel:3,isHighProbability:!0},[_.PRE_LUNCH]:{type:_.PRE_LUNCH,name:"11:50-12:10 Macro",timeRange:{start:"11:50:00",end:"12:10:00"},description:"Pre-lunch macro window - specific high-activity period within lunch session",characteristics:["Consolidation","Range Trading","Pre-Lunch Activity"],volatilityLevel:2,volumeLevel:2,isHighProbability:!1,parentMacro:_.LUNCH_MACRO_EXTENDED},[_.LUNCH_MACRO_EXTENDED]:{type:_.LUNCH_MACRO_EXTENDED,name:"Lunch Macro (11:30-13:30)",timeRange:{start:"11:30:00",end:"13:30:00"},description:"Extended lunch period spanning late morning through early afternoon",characteristics:["Multi-Session","Lunch Trading","Lower Volume","Transition Period"],volatilityLevel:2,volumeLevel:2,isHighProbability:!1,isMultiSession:!0,spansSessions:[U.NEW_YORK_AM,U.NEW_YORK_PM],subPeriods:[]},[_.LUNCH_MACRO]:{type:_.LUNCH_MACRO,name:"Lunch Macro (12:00-13:30)",timeRange:{start:"12:00:00",end:"13:30:00"},description:"Traditional lunch time trading - typically lower volume",characteristics:["Low Volume","Range Bound","Choppy Price Action"],volatilityLevel:2,volumeLevel:1,isHighProbability:!1},[_.POST_LUNCH]:{type:_.POST_LUNCH,name:"13:50-14:10 Macro",timeRange:{start:"13:50:00",end:"14:10:00"},description:"Post-lunch macro window",characteristics:["Volume Pickup","Trend Resumption"],volatilityLevel:3,volumeLevel:3,isHighProbability:!1},[_.PRE_CLOSE]:{type:_.PRE_CLOSE,name:"14:50-15:10 Macro",timeRange:{start:"14:50:00",end:"15:10:00"},description:"Pre-close macro window",characteristics:["Institutional Activity","Position Adjustments"],volatilityLevel:3,volumeLevel:4,isHighProbability:!1},[_.POWER_HOUR]:{type:_.POWER_HOUR,name:"15:15-15:45 Macro (Power Hour)",timeRange:{start:"15:15:00",end:"15:45:00"},description:"Last hour macro - high activity before close",characteristics:["High Volume","Institutional Flows","EOD Positioning"],volatilityLevel:4,volumeLevel:5,isHighProbability:!0},[_.MOC]:{type:_.MOC,name:"MOC (Market on Close)",timeRange:{start:"15:45:00",end:"16:00:00"},description:"Market on close period",characteristics:["MOC Orders","Final Positioning","High Volume"],volatilityLevel:4,volumeLevel:5,isHighProbability:!1},[_.LONDON_OPEN]:{type:_.LONDON_OPEN,name:"London Open",timeRange:{start:"08:00:00",end:"09:00:00"},description:"London market opening hour",characteristics:["European Activity","Currency Moves","News Reactions"],volatilityLevel:4,volumeLevel:4,isHighProbability:!0},[_.LONDON_NY_OVERLAP]:{type:_.LONDON_NY_OVERLAP,name:"London/NY Overlap",timeRange:{start:"14:00:00",end:"16:00:00"},description:"London and New York session overlap",characteristics:["Highest Volume","Major Moves","Cross-Market Activity"],volatilityLevel:5,volumeLevel:5,isHighProbability:!0},[_.CUSTOM]:{type:_.CUSTOM,name:"Custom Period",timeRange:{start:"00:00:00",end:"23:59:59"},description:"User-defined custom time period",characteristics:["Custom"],volatilityLevel:3,volumeLevel:3,isHighProbability:!1}},ea=()=>{const e=Object.values(ta).map(i=>({id:i.type,...i})),r=[{...ce[_.LUNCH_MACRO_EXTENDED],id:"lunch-macro-extended",subPeriods:[{...ce[_.PRE_LUNCH],id:"pre-lunch-sub"}]}],o={};return e.forEach(i=>{i.macroPeriods.forEach(c=>{o[c.type]={...c,parentSession:i.type}})}),r.forEach(i=>{o[i.type]={...i,spansSessions:i.spansSessions}}),{sessions:e,sessionsByType:e.reduce((i,c)=>(i[c.type]=c,i),{}),macrosByType:o,multiSessionMacros:r}},ta={[U.NEW_YORK_AM]:{type:U.NEW_YORK_AM,name:"New York AM Session",timeRange:{start:"09:30:00",end:"12:00:00"},description:"New York morning session - high activity and volatility",timezone:"America/New_York",characteristics:["High Volume","Trend Development","Breakout Opportunities"],color:"#dc2626",macroPeriods:[{...ce[_.MORNING_BREAKOUT],id:"morning-breakout"},{...ce[_.MID_MORNING_REVERSION],id:"mid-morning-reversion"},{...ce[_.PRE_LUNCH],id:"pre-lunch"}]},[U.NEW_YORK_PM]:{type:U.NEW_YORK_PM,name:"New York PM Session",timeRange:{start:"12:00:00",end:"16:00:00"},description:"New York afternoon session - institutional activity increases toward close",timezone:"America/New_York",characteristics:["Institutional Flows","EOD Positioning","Power Hour Activity"],color:"#dc2626",macroPeriods:[{...ce[_.LUNCH_MACRO],id:"lunch-macro"},{...ce[_.POST_LUNCH],id:"post-lunch"},{...ce[_.PRE_CLOSE],id:"pre-close"},{...ce[_.POWER_HOUR],id:"power-hour"},{...ce[_.MOC],id:"moc"}]},[U.LONDON]:{type:U.LONDON,name:"London Session",timeRange:{start:"08:00:00",end:"16:00:00"},description:"London trading session - European market activity",timezone:"Europe/London",characteristics:["European Activity","Currency Focus","News-Driven"],color:"#1f2937",macroPeriods:[{...ce[_.LONDON_OPEN],id:"london-open"},{...ce[_.LONDON_NY_OVERLAP],id:"london-ny-overlap"}]},[U.ASIA]:{type:U.ASIA,name:"Asia Session",timeRange:{start:"18:00:00",end:"03:00:00"},description:"Asian trading session - typically lower volatility",timezone:"Asia/Tokyo",characteristics:["Lower Volume","Range Trading","News Reactions"],color:"#4b5563",macroPeriods:[]},[U.PRE_MARKET]:{type:U.PRE_MARKET,name:"Pre-Market",timeRange:{start:"04:00:00",end:"09:30:00"},description:"Pre-market trading hours",timezone:"America/New_York",characteristics:["Low Volume","News Reactions","Gap Setups"],color:"#6b7280",macroPeriods:[]},[U.AFTER_HOURS]:{type:U.AFTER_HOURS,name:"After Hours",timeRange:{start:"16:00:00",end:"20:00:00"},description:"After-hours trading",timezone:"America/New_York",characteristics:["Low Volume","Earnings Reactions","News-Driven"],color:"#6b7280",macroPeriods:[]},[U.OVERNIGHT]:{type:U.OVERNIGHT,name:"Overnight",timeRange:{start:"20:00:00",end:"04:00:00"},description:"Overnight session",timezone:"America/New_York",characteristics:["Very Low Volume","Futures Activity"],color:"#374151",macroPeriods:[]}};class re{static getSessionHierarchy(){return this.hierarchy||(this.hierarchy=this.buildHierarchy()),this.hierarchy}static buildHierarchy(){return ea()}static timeToMinutes(t){const[r,o,i=0]=t.split(":").map(Number);return r*60+o+i/60}static minutesToTime(t){const r=Math.floor(t/60),o=Math.floor(t%60),i=Math.floor(t%1*60);return`${r.toString().padStart(2,"0")}:${o.toString().padStart(2,"0")}:${i.toString().padStart(2,"0")}`}static isTimeInRange(t,r){const o=this.timeToMinutes(t),i=this.timeToMinutes(r.start),c=this.timeToMinutes(r.end);return c<i?o>=i||o<=c:o>=i&&o<=c}static validateTime(t){var c;if(!/^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/.test(t))return{isValid:!1,error:"Invalid time format. Use HH:MM or HH:MM:SS format."};const o=this.getSessionHierarchy(),i=[];for(const[a,l]of Object.entries(o.macrosByType))this.isTimeInRange(t,l.timeRange)&&i.push({type:a,macro:l,isSubPeriod:!!l.parentMacro});if(i.length>0){const l=i.sort((f,u)=>{if(f.isSubPeriod&&!u.isSubPeriod)return-1;if(!f.isSubPeriod&&u.isSubPeriod)return 1;const m=this.timeToMinutes(f.macro.timeRange.end)-this.timeToMinutes(f.macro.timeRange.start),y=this.timeToMinutes(u.macro.timeRange.end)-this.timeToMinutes(u.macro.timeRange.start);return m-y})[0],d=i.length>1;return{isValid:!0,suggestedMacro:l.type,suggestedSession:l.macro.parentSession||((c=l.macro.spansSessions)==null?void 0:c[0]),warning:d?`Time falls within ${i.length} overlapping macro periods. Suggesting most specific: ${l.macro.name}`:void 0}}for(const a of o.sessions)if(this.isTimeInRange(t,a.timeRange))return{isValid:!0,suggestedSession:a.type,warning:"Time falls within session but not in a specific macro period."};return{isValid:!0,warning:"Time does not fall within any defined session or macro period."}}static getSession(t){return this.getSessionHierarchy().sessionsByType[t]||null}static getMacroPeriod(t){return this.getSessionHierarchy().macrosByType[t]||null}static getMacroPeriodsForSession(t){const r=this.getSession(t);return(r==null?void 0:r.macroPeriods)||[]}static createSessionSelection(t,r,o){if(r){const i=this.getMacroPeriod(r);return{session:i==null?void 0:i.parentSession,macroPeriod:r,displayLabel:(i==null?void 0:i.name)||"Unknown Macro",selectionType:"macro"}}if(t){const i=this.getSession(t);return{session:t,displayLabel:(i==null?void 0:i.name)||"Unknown Session",selectionType:"session"}}return o?{customTimeRange:o,displayLabel:`${o.start} - ${o.end}`,selectionType:"custom"}:{displayLabel:"No Selection",selectionType:"custom"}}static filterSessions(t={}){var c,a;const r=this.getSessionHierarchy();let o=[...r.sessions],i=Object.values(r.macrosByType);return t.activeOnly&&(o=o.filter(l=>l.isActive)),(c=t.sessionTypes)!=null&&c.length&&(o=o.filter(l=>t.sessionTypes.includes(l.type))),(a=t.macroTypes)!=null&&a.length&&(i=i.filter(l=>t.macroTypes.includes(l.type))),t.highProbabilityOnly&&(i=i.filter(l=>l.isHighProbability)),t.minVolatility!==void 0&&(i=i.filter(l=>l.volatilityLevel>=t.minVolatility)),t.maxVolatility!==void 0&&(i=i.filter(l=>l.volatilityLevel<=t.maxVolatility)),{sessions:o,macros:i}}static getCurrentSession(){const t=new Date,r=`${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}:00`,o=this.validateTime(r);return o.suggestedMacro?this.createSessionSelection(o.suggestedSession,o.suggestedMacro):o.suggestedSession?this.createSessionSelection(o.suggestedSession):null}static timeRangesOverlap(t,r){const o=this.timeToMinutes(t.start),i=this.timeToMinutes(t.end),c=this.timeToMinutes(r.start),a=this.timeToMinutes(r.end);return Math.max(o,c)<Math.min(i,a)}static getDisplayOptions(){const t=this.getSessionHierarchy(),r=t.sessions.map(i=>({value:i.type,label:i.name,group:"Sessions"})),o=Object.values(t.macrosByType).filter(i=>i.parentSession).map(i=>{var c;return{value:i.type,label:i.name,group:((c=t.sessionsByType[i.parentSession])==null?void 0:c.name)||"Other",parentSession:i.parentSession}});return{sessionOptions:r,macroOptions:o}}static getOverlappingMacros(t){const r=this.getSessionHierarchy(),o=[];for(const[i,c]of Object.entries(r.macrosByType))this.isTimeInRange(t,c.timeRange)&&o.push({type:i,macro:c,isSubPeriod:!!c.parentMacro,isMultiSession:!!c.spansSessions});return o.sort((i,c)=>{if(i.isSubPeriod&&!c.isSubPeriod)return-1;if(!i.isSubPeriod&&c.isSubPeriod)return 1;const a=this.timeToMinutes(i.macro.timeRange.end)-this.timeToMinutes(i.macro.timeRange.start),l=this.timeToMinutes(c.macro.timeRange.end)-this.timeToMinutes(c.macro.timeRange.start);return a-l})}static getMultiSessionMacros(){return this.getSessionHierarchy().multiSessionMacros||[]}static hasSubPeriods(t){const r=this.getMacroPeriod(t);return!!(r!=null&&r.subPeriods&&r.subPeriods.length>0)}static getSubPeriods(t){const r=this.getMacroPeriod(t);return(r==null?void 0:r.subPeriods)||[]}static convertLegacySession(t){const o={"NY Open":{session:U.NEW_YORK_AM},"London Open":{session:U.LONDON},"Lunch Macro":{macro:_.LUNCH_MACRO_EXTENDED},"Lunch Macro (11:30-13:30)":{macro:_.LUNCH_MACRO_EXTENDED},"Lunch Macro (12:00-13:30)":{macro:_.LUNCH_MACRO},MOC:{macro:_.MOC},Overnight:{session:U.OVERNIGHT},"Pre-Market":{session:U.PRE_MARKET},"After Hours":{session:U.AFTER_HOURS},"Power Hour":{macro:_.POWER_HOUR},"10:50-11:10":{macro:_.MID_MORNING_REVERSION},"11:50-12:10":{macro:_.PRE_LUNCH},"15:15-15:45":{macro:_.POWER_HOUR}}[t];return o?this.createSessionSelection(o.session,o.macro):null}}be(re,"hierarchy",null);const Vr=(e={})=>{const{initialSelection:t,autoDetectCurrent:r=!1,filterOptions:o={},onSelectionChange:i,validateTimes:c=!0}=e,[a,l]=x.useState(t||{displayLabel:"No Selection",selectionType:"custom"}),d=x.useMemo(()=>re.getCurrentSession(),[]),f=x.useMemo(()=>d!==null,[d]),{availableSessions:u,availableMacros:m}=x.useMemo(()=>{const{sessions:k,macros:M}=re.filterSessions(o),{sessionOptions:R,macroOptions:N}=re.getDisplayOptions(),W=R.filter(D=>k.some(G=>G.type===D.value)),q=N.filter(D=>M.some(G=>G.type===D.value));return{availableSessions:W,availableMacros:q}},[o]),y=x.useMemo(()=>u.map(k=>{const M=m.filter(R=>R.parentSession===k.value).map(R=>({value:R.value,label:R.label}));return{session:k.value,sessionLabel:k.label,macros:M}}),[u,m]);x.useEffect(()=>{r&&d&&!t&&l(d)},[r,d,t]),x.useEffect(()=>{i==null||i(a)},[a,i]);const h=x.useCallback(k=>{const M=re.createSessionSelection(k);l(M)},[]),g=x.useCallback(k=>{const M=re.createSessionSelection(void 0,k);l(M)},[]),b=x.useCallback(k=>{const M=re.createSessionSelection(void 0,void 0,k);l(M)},[]),T=x.useCallback(()=>{l({displayLabel:"No Selection",selectionType:"custom"})},[]),w=x.useCallback(k=>c?re.validateTime(k):{isValid:!0},[c]),C=x.useMemo(()=>{if(a.selectionType==="session"&&a.session)return re.getSession(a.session)!==null;if(a.selectionType==="macro"&&a.macroPeriod)return re.getMacroPeriod(a.macroPeriod)!==null;if(a.selectionType==="custom"&&a.customTimeRange){const k=w(a.customTimeRange.start),M=w(a.customTimeRange.end);return k.isValid&&M.isValid}return a.selectionType==="custom"&&!a.customTimeRange},[a,w]),L=x.useCallback(k=>re.getSession(k),[]),$=x.useCallback(k=>re.getMacroPeriod(k),[]),j=x.useCallback(k=>re.convertLegacySession(k),[]);return{selection:a,selectSession:h,selectMacro:g,selectCustomRange:b,clearSelection:T,validateTime:w,isValidSelection:C,availableSessions:u,availableMacros:m,hierarchicalOptions:y,currentSession:d,isCurrentSessionActive:f,getSessionDetails:L,getMacroDetails:$,convertLegacySession:j}},ra=s.div.withConfig({displayName:"Container",componentId:"sc-1reqqnl-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"}),oa=s.div.withConfig({displayName:"SelectorContainer",componentId:"sc-1reqqnl-1"})(["position:relative;border:1px solid ",";border-radius:",";background:",";transition:all 0.2s ease;opacity:",";pointer-events:",";&:hover{border-color:","40;}&:focus-within{border-color:",";box-shadow:0 0 0 3px ","20;}"],({theme:e,hasError:t})=>{var r,o;return t?((r=e.colors)==null?void 0:r.error)||"#ef4444":((o=e.colors)==null?void 0:o.border)||"#4b5563"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.md)||"6px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"#1f2937"},({disabled:e})=>e?.6:1,({disabled:e})=>e?"none":"auto",({theme:e,hasError:t})=>{var r,o;return t?((r=e.colors)==null?void 0:r.error)||"#ef4444":((o=e.colors)==null?void 0:o.primary)||"#dc2626"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#dc2626"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#dc2626"}),na=s.div.withConfig({displayName:"SelectedValue",componentId:"sc-1reqqnl-2"})(["padding:",";color:",";font-size:",";cursor:pointer;display:flex;align-items:center;justify-content:space-between;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.md)||"1rem"}),sa=s.div.withConfig({displayName:"DropdownIcon",componentId:"sc-1reqqnl-3"})(["transition:transform 0.2s ease;transform:",";color:",";"],({isOpen:e})=>e?"rotate(180deg)":"rotate(0deg)",({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"}),ia=s.div.withConfig({displayName:"DropdownMenu",componentId:"sc-1reqqnl-4"})(["position:absolute;top:100%;left:0;right:0;z-index:1000;background:",";border:1px solid ",";border-radius:",";box-shadow:0 10px 25px -5px rgba(0,0,0,0.3);max-height:400px;overflow-y:auto;display:",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"#1f2937"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#4b5563"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.md)||"6px"},({isOpen:e})=>e?"block":"none"),aa=s.div.withConfig({displayName:"MultiSessionGroup",componentId:"sc-1reqqnl-5"})(["border-bottom:1px solid ",";background:",";&:last-child{border-bottom:none;}"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#4b5563"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"#111827"}),ca=s.div.withConfig({displayName:"MultiSessionHeader",componentId:"sc-1reqqnl-6"})(["padding:",";background:",";color:",";font-weight:600;cursor:pointer;display:flex;align-items:center;justify-content:space-between;transition:background-color 0.2s ease;border-left:3px solid ",";&:hover{background:","40;}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e,isSelected:t})=>{var r,o;return t?((r=e.colors)==null?void 0:r.primary)||"#dc2626":((o=e.colors)==null?void 0:o.surface)||"#1f2937"},({theme:e,isSelected:t})=>{var r;return t?"#ffffff":((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.warning)||"#f59e0b"},({theme:e,isSelected:t})=>{var r,o;return t?((r=e.colors)==null?void 0:r.primary)||"#dc2626":((o=e.colors)==null?void 0:o.border)||"#4b5563"}),la=s.div.withConfig({displayName:"MultiSessionIndicator",componentId:"sc-1reqqnl-7"})(["display:inline-flex;align-items:center;gap:",";font-size:",";color:",";font-weight:500;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.warning)||"#f59e0b"}),da=s.div.withConfig({displayName:"SessionGroup",componentId:"sc-1reqqnl-8"})(["border-bottom:1px solid ",";&:last-child{border-bottom:none;}"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#4b5563"}),ua=s.div.withConfig({displayName:"SessionHeader",componentId:"sc-1reqqnl-9"})(["padding:",";background:",";color:",";font-weight:600;cursor:pointer;display:flex;align-items:center;justify-content:space-between;transition:background-color 0.2s ease;&:hover{background:","40;}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e,isSelected:t})=>{var r;return t?((r=e.colors)==null?void 0:r.primary)||"#dc2626":"transparent"},({theme:e,isSelected:t})=>{var r;return t?"#ffffff":((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({theme:e,isSelected:t})=>{var r,o;return t?((r=e.colors)==null?void 0:r.primary)||"#dc2626":((o=e.colors)==null?void 0:o.border)||"#4b5563"}),pa=s.div.withConfig({displayName:"MacroList",componentId:"sc-1reqqnl-10"})(["background:",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"#111827"}),fa=s.div.withConfig({displayName:"MacroItem",componentId:"sc-1reqqnl-11"})(["padding:"," ",";color:",";cursor:pointer;font-size:",";transition:all 0.2s ease;border-left:3px solid ",";&:hover{background:","20;color:",";}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"},({theme:e,isSelected:t})=>{var r,o;return t?((r=e.colors)==null?void 0:r.primary)||"#dc2626":((o=e.colors)==null?void 0:o.textSecondary)||"#9ca3af"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e,isSelected:t})=>{var r;return t?((r=e.colors)==null?void 0:r.primary)||"#dc2626":"transparent"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#4b5563"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"}),ma=s.div.withConfig({displayName:"CurrentSessionIndicator",componentId:"sc-1reqqnl-12"})(["display:inline-flex;align-items:center;gap:",";font-size:",";color:",";font-weight:500;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.success)||"#10b981"}),ga=s.div.withConfig({displayName:"ErrorMessage",componentId:"sc-1reqqnl-13"})(["color:",";font-size:",";margin-top:",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.error)||"#ef4444"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"}),ha=({value:e,onChange:t,showMacroPeriods:r=!0,showCurrentSession:o=!0,placeholder:i="Select session or macro period",disabled:c=!1,error:a,className:l})=>{const[d,f]=x.useState(!1),{hierarchicalOptions:u,currentSession:m,selectSession:y,selectMacro:h}=Vr({onSelectionChange:t}),g=x.useMemo(()=>re.getMultiSessionMacros(),[]),b=x.useMemo(()=>e!=null&&e.displayLabel?e.displayLabel:i,[e,i]),T=j=>{y(j),f(!1)},w=j=>{h(j),f(!1)},C=j=>(e==null?void 0:e.session)===j&&(e==null?void 0:e.selectionType)==="session",L=j=>(e==null?void 0:e.macroPeriod)===j&&(e==null?void 0:e.selectionType)==="macro",$=j=>(m==null?void 0:m.session)===j;return n.jsxs(ra,{className:l,hasError:!!a,children:[n.jsxs(oa,{hasError:!!a,disabled:c,onClick:()=>!c&&f(!d),children:[n.jsxs(na,{children:[n.jsx("span",{children:b}),n.jsx(sa,{isOpen:d,children:"▼"})]}),n.jsxs(ia,{isOpen:d,children:[r&&g.length>0&&n.jsx(aa,{children:g.map(j=>n.jsxs(ca,{isSelected:L(j.type),onClick:k=>{k.stopPropagation(),w(j.type)},children:[n.jsx("span",{children:j.name}),n.jsx(la,{children:"🌐 MULTI-SESSION"})]},j.type))}),u.map(({session:j,sessionLabel:k,macros:M})=>n.jsxs(da,{children:[n.jsxs(ua,{isSelected:C(j),onClick:R=>{R.stopPropagation(),T(j)},children:[n.jsx("span",{children:k}),o&&$(j)&&n.jsx(ma,{children:"🔴 LIVE"})]}),r&&M.length>0&&n.jsx(pa,{children:M.map(({value:R,label:N})=>n.jsxs(fa,{isSelected:L(R),onClick:W=>{W.stopPropagation(),w(R)},children:[N,re.hasSubPeriods(R)&&n.jsx("span",{style:{marginLeft:"8px",fontSize:"0.75rem",opacity:.7},children:"📋 Has sub-periods"})]},R))})]},j))]})]}),a&&n.jsx(ga,{children:a})]})},I={DATE:"date",SYMBOL:"symbol",DIRECTION:"direction",MODEL_TYPE:"model_type",SESSION:"session",ENTRY_PRICE:"entry_price",EXIT_PRICE:"exit_price",R_MULTIPLE:"r_multiple",ACHIEVED_PL:"achieved_pl",WIN_LOSS:"win_loss",PATTERN_QUALITY:"pattern_quality_rating",ENTRY_TIME:"entry_time",EXIT_TIME:"exit_time"},jt=s.span.withConfig({displayName:"ProfitLossCell",componentId:"sc-14bks31-0"})(["color:",";font-weight:",";"],({isProfit:e,theme:t})=>e?t.colors.success||"#10b981":t.colors.error||"#ef4444",({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.semibold)||600}),Gr=s($e).withConfig({displayName:"DirectionBadge",componentId:"sc-14bks31-1"})(["background-color:",";color:white;"],({direction:e,theme:t})=>e==="Long"?t.colors.success||"#10b981":t.colors.error||"#ef4444"),Br=s.span.withConfig({displayName:"QualityRating",componentId:"sc-14bks31-2"})(["color:",";font-weight:",";"],({rating:e,theme:t})=>e>=4?t.colors.success||"#10b981":e>=3?t.colors.warning||"#f59e0b":t.colors.error||"#ef4444",({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.semibold)||600}),kt=s.span.withConfig({displayName:"RMultipleCell",componentId:"sc-14bks31-3"})(["color:",";font-weight:",";"],({rMultiple:e,theme:t})=>e>0?t.colors.success||"#10b981":t.colors.error||"#ef4444",({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.semibold)||600}),Pe=e=>e==null?"-":new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2}).format(e),Nt=e=>{try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch{return e}},Tt=e=>e||"-",Hr=()=>[{id:I.DATE,header:"Date",sortable:!0,width:"100px",cell:e=>Nt(e.trade[I.DATE])},{id:I.SYMBOL,header:"Symbol",sortable:!0,width:"80px",cell:e=>e.trade.market||"MNQ"},{id:I.DIRECTION,header:"Direction",sortable:!0,width:"80px",align:"center",cell:e=>n.jsx(Gr,{direction:e.trade[I.DIRECTION],size:"small",children:e.trade[I.DIRECTION]})},{id:I.MODEL_TYPE,header:"Model",sortable:!0,width:"120px",cell:e=>e.trade[I.MODEL_TYPE]||"-"},{id:I.SESSION,header:"Session",sortable:!0,width:"120px",cell:e=>e.trade[I.SESSION]||"-"},{id:I.ENTRY_PRICE,header:"Entry",sortable:!0,width:"100px",align:"right",cell:e=>Pe(e.trade[I.ENTRY_PRICE])},{id:I.EXIT_PRICE,header:"Exit",sortable:!0,width:"100px",align:"right",cell:e=>Pe(e.trade[I.EXIT_PRICE])},{id:I.R_MULTIPLE,header:"R Multiple",sortable:!0,width:"100px",align:"right",cell:e=>{var t;return n.jsx(kt,{rMultiple:e.trade[I.R_MULTIPLE]||0,children:e.trade[I.R_MULTIPLE]?`${(t=e.trade[I.R_MULTIPLE])==null?void 0:t.toFixed(2)}R`:"-"})}},{id:I.ACHIEVED_PL,header:"P&L",sortable:!0,width:"100px",align:"right",cell:e=>n.jsx(jt,{isProfit:(e.trade[I.ACHIEVED_PL]||0)>0,children:Pe(e.trade[I.ACHIEVED_PL])})},{id:I.WIN_LOSS,header:"Result",sortable:!0,width:"80px",align:"center",cell:e=>n.jsx($e,{variant:e.trade[I.WIN_LOSS]==="Win"?"success":"error",size:"small",children:e.trade[I.WIN_LOSS]||"-"})},{id:I.PATTERN_QUALITY,header:"Quality",sortable:!0,width:"80px",align:"center",cell:e=>n.jsx(Br,{rating:e.trade[I.PATTERN_QUALITY]||0,children:e.trade[I.PATTERN_QUALITY]?`${e.trade[I.PATTERN_QUALITY]}/5`:"-"})},{id:I.ENTRY_TIME,header:"Entry Time",sortable:!0,width:"100px",align:"center",cell:e=>Tt(e.trade[I.ENTRY_TIME])},{id:I.EXIT_TIME,header:"Exit Time",sortable:!0,width:"100px",align:"center",cell:e=>Tt(e.trade[I.EXIT_TIME])}],qr=()=>[{id:I.DATE,header:"Date",sortable:!0,width:"90px",cell:e=>Nt(e.trade[I.DATE])},{id:I.SYMBOL,header:"Symbol",sortable:!0,width:"60px",cell:e=>e.trade.market||"MNQ"},{id:I.DIRECTION,header:"Dir",sortable:!0,width:"50px",align:"center",cell:e=>n.jsx(Gr,{direction:e.trade[I.DIRECTION],size:"small",children:e.trade[I.DIRECTION].charAt(0)})},{id:I.R_MULTIPLE,header:"R",sortable:!0,width:"60px",align:"right",cell:e=>{var t;return n.jsx(kt,{rMultiple:e.trade[I.R_MULTIPLE]||0,children:e.trade[I.R_MULTIPLE]?`${(t=e.trade[I.R_MULTIPLE])==null?void 0:t.toFixed(1)}R`:"-"})}},{id:I.ACHIEVED_PL,header:"P&L",sortable:!0,width:"80px",align:"right",cell:e=>n.jsx(jt,{isProfit:(e.trade[I.ACHIEVED_PL]||0)>0,children:Pe(e.trade[I.ACHIEVED_PL])})},{id:I.WIN_LOSS,header:"Result",sortable:!0,width:"60px",align:"center",cell:e=>n.jsx($e,{variant:e.trade[I.WIN_LOSS]==="Win"?"success":"error",size:"small",children:e.trade[I.WIN_LOSS]==="Win"?"W":e.trade[I.WIN_LOSS]==="Loss"?"L":"-"})}],Ur=()=>[{id:I.DATE,header:"Date",sortable:!0,width:"100px",cell:e=>Nt(e.trade[I.DATE])},{id:I.MODEL_TYPE,header:"Model",sortable:!0,width:"120px",cell:e=>e.trade[I.MODEL_TYPE]||"-"},{id:I.SESSION,header:"Session",sortable:!0,width:"120px",cell:e=>e.trade[I.SESSION]||"-"},{id:I.R_MULTIPLE,header:"R Multiple",sortable:!0,width:"100px",align:"right",cell:e=>{var t;return n.jsx(kt,{rMultiple:e.trade[I.R_MULTIPLE]||0,children:e.trade[I.R_MULTIPLE]?`${(t=e.trade[I.R_MULTIPLE])==null?void 0:t.toFixed(2)}R`:"-"})}},{id:I.ACHIEVED_PL,header:"P&L",sortable:!0,width:"100px",align:"right",cell:e=>n.jsx(jt,{isProfit:(e.trade[I.ACHIEVED_PL]||0)>0,children:Pe(e.trade[I.ACHIEVED_PL])})},{id:I.PATTERN_QUALITY,header:"Quality",sortable:!0,width:"80px",align:"center",cell:e=>n.jsx(Br,{rating:e.trade[I.PATTERN_QUALITY]||0,children:e.trade[I.PATTERN_QUALITY]?`${e.trade[I.PATTERN_QUALITY]}/5`:"-"})},{id:I.WIN_LOSS,header:"Result",sortable:!0,width:"80px",align:"center",cell:e=>n.jsx($e,{variant:e.trade[I.WIN_LOSS]==="Win"?"success":"error",size:"small",children:e.trade[I.WIN_LOSS]||"-"})}],ya=s.tr.withConfig({displayName:"TableRow",componentId:"sc-uyrnn-0"})([""," "," "," "," ",""],({striped:e,theme:t,isSelected:r})=>{var o;return e&&!r&&s.css(["&:nth-child(even){background-color:","50;}"],((o=t.colors)==null?void 0:o.background)||"#f8f9fa")},({hoverable:e,theme:t,isSelected:r})=>{var o;return e&&!r&&s.css(["&:hover{background-color:","aa;}"],((o=t.colors)==null?void 0:o.background)||"#f8f9fa")},({isSelected:e,theme:t})=>{var r;return e&&s.css(["background-color:","15;"],((r=t.colors)==null?void 0:r.primary)||"#3b82f6")},({isClickable:e})=>e&&s.css(["cursor:pointer;"]),({isExpanded:e,theme:t})=>{var r;return e&&s.css(["border-bottom:2px solid ",";"],((r=t.colors)==null?void 0:r.primary)||"#3b82f6")}),lr=s.td.withConfig({displayName:"TableCell",componentId:"sc-uyrnn-1"})(["text-align:",";border-bottom:1px solid ",";color:",";padding:"," ",";vertical-align:middle;"],({align:e})=>e||"left",({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#e5e7eb"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#111827"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"12px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"16px"}),ba=s.tr.withConfig({displayName:"ExpandedRow",componentId:"sc-uyrnn-2"})(["display:",";"],({isVisible:e})=>e?"table-row":"none"),xa=s.td.withConfig({displayName:"ExpandedCell",componentId:"sc-uyrnn-3"})(["padding:0;border-bottom:1px solid ",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#e5e7eb"}),va=s.div.withConfig({displayName:"ExpandedContent",componentId:"sc-uyrnn-4"})(["padding:",";background-color:","30;border-left:3px solid ",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"16px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"#f8f9fa"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#3b82f6"}),Sa=s.button.withConfig({displayName:"ExpandButton",componentId:"sc-uyrnn-5"})(["background:none;border:none;cursor:pointer;padding:",";color:",";font-size:",";display:flex;align-items:center;justify-content:center;border-radius:",";transition:all 0.2s ease;&:hover{background-color:",";color:",";}&:focus{outline:2px solid ",";outline-offset:2px;}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"8px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#6b7280"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.sm)||"4px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"#f8f9fa"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#3b82f6"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#3b82f6"}),wa=s.span.withConfig({displayName:"ExpandIcon",componentId:"sc-uyrnn-6"})(["display:inline-block;transition:transform 0.2s ease;transform:",";&::after{content:'▶';}"],({isExpanded:e})=>e?"rotate(90deg)":"rotate(0deg)"),Ca=s.div.withConfig({displayName:"TradeDetails",componentId:"sc-uyrnn-7"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"16px"}),De=s.div.withConfig({displayName:"DetailGroup",componentId:"sc-uyrnn-8"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"8px"}),Le=s.span.withConfig({displayName:"DetailLabel",componentId:"sc-uyrnn-9"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"},({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.medium)||500},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#6b7280"}),ne=s.span.withConfig({displayName:"DetailValue",componentId:"sc-uyrnn-10"})(["font-size:",";color:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#111827"}),Ta=({trade:e})=>n.jsxs(Ca,{children:[e.fvg_details&&n.jsxs(De,{children:[n.jsx(Le,{children:"FVG Details"}),n.jsxs(ne,{children:["Type: ",e.fvg_details.rd_type||"-"]}),n.jsxs(ne,{children:["Entry Version: ",e.fvg_details.entry_version||"-"]}),n.jsxs(ne,{children:["Draw on Liquidity: ",e.fvg_details.draw_on_liquidity||"-"]})]}),e.setup&&n.jsxs(De,{children:[n.jsx(Le,{children:"Setup Classification"}),n.jsxs(ne,{children:["Primary: ",e.setup.primary_setup||"-"]}),n.jsxs(ne,{children:["Secondary: ",e.setup.secondary_setup||"-"]}),n.jsxs(ne,{children:["Liquidity: ",e.setup.liquidity_taken||"-"]})]}),e.analysis&&n.jsxs(De,{children:[n.jsx(Le,{children:"Analysis"}),n.jsxs(ne,{children:["DOL Target: ",e.analysis.dol_target_type||"-"]}),n.jsxs(ne,{children:["Path Quality: ",e.analysis.path_quality||"-"]}),n.jsxs(ne,{children:["Clustering: ",e.analysis.clustering||"-"]})]}),n.jsxs(De,{children:[n.jsx(Le,{children:"Timing"}),n.jsxs(ne,{children:["Entry: ",e.trade.entry_time||"-"]}),n.jsxs(ne,{children:["Exit: ",e.trade.exit_time||"-"]}),n.jsxs(ne,{children:["FVG: ",e.trade.fvg_time||"-"]}),n.jsxs(ne,{children:["RD: ",e.trade.rd_time||"-"]})]}),e.trade.notes&&n.jsxs(De,{style:{gridColumn:"1 / -1"},children:[n.jsx(Le,{children:"Notes"}),n.jsx(ne,{children:e.trade.notes})]})]}),Yr=({trade:e,index:t,columns:r,isSelected:o=!1,hoverable:i=!0,striped:c=!0,expandable:a=!1,isExpanded:l=!1,onRowClick:d,onToggleExpand:f,expandedContent:u})=>{const[m,y]=x.useState(!1),h=l!==void 0?l:m,g=w=>{w.target.closest("button")||d==null||d(e,t)},b=w=>{w.stopPropagation(),f?f(e,t):y(!m)},T=r.filter(w=>!w.hidden);return n.jsxs(n.Fragment,{children:[n.jsxs(ya,{hoverable:i,striped:c,isSelected:o,isClickable:!!d,isExpanded:h,onClick:g,children:[a&&n.jsx(lr,{align:"center",style:{width:"40px",padding:"8px"},children:n.jsx(Sa,{onClick:b,children:n.jsx(wa,{isExpanded:h})})}),T.map(w=>n.jsx(lr,{align:w.align,children:w.cell(e,t)},w.id))]}),a&&n.jsx(ba,{isVisible:h,children:n.jsx(xa,{colSpan:T.length+1,children:n.jsx(va,{children:u||n.jsx(Ta,{trade:e})})})})]})},de={MODEL_TYPE:"model_type",WIN_LOSS:"win_loss",DATE_FROM:"dateFrom",DATE_TO:"dateTo",SESSION:"session",DIRECTION:"direction",MARKET:"market",MIN_R_MULTIPLE:"min_r_multiple",MAX_R_MULTIPLE:"max_r_multiple",MIN_PATTERN_QUALITY:"min_pattern_quality",MAX_PATTERN_QUALITY:"max_pattern_quality"},Ea=s.div.withConfig({displayName:"FiltersContainer",componentId:"sc-32k3gq-0"})(["display:flex;flex-direction:column;gap:",";padding:",";background-color:",";border-radius:",";border:1px solid ",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"16px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"16px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"#f8f9fa"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.md)||"8px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#e5e7eb"}),dr=s.div.withConfig({displayName:"FilterRow",componentId:"sc-32k3gq-1"})(["display:flex;gap:",";align-items:end;flex-wrap:wrap;@media (max-width:768px){flex-direction:column;align-items:stretch;}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"12px"}),me=s.div.withConfig({displayName:"FilterGroup",componentId:"sc-32k3gq-2"})(["display:flex;flex-direction:column;gap:",";min-width:120px;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"8px"}),ge=s.label.withConfig({displayName:"FilterLabel",componentId:"sc-32k3gq-3"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"},({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.medium)||500},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#6b7280"}),Ia=s.div.withConfig({displayName:"FilterActions",componentId:"sc-32k3gq-4"})(["display:flex;gap:",";align-items:center;margin-left:auto;@media (max-width:768px){margin-left:0;justify-content:flex-end;}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"12px"}),Ra=s.div.withConfig({displayName:"AdvancedFilters",componentId:"sc-32k3gq-5"})(["display:",";flex-direction:column;gap:",";padding-top:",";border-top:1px solid ",";"],({isVisible:e})=>e?"flex":"none",({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"16px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"16px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#e5e7eb"}),ur=s.div.withConfig({displayName:"RangeInputGroup",componentId:"sc-32k3gq-6"})(["display:flex;gap:",";align-items:center;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"8px"}),pr=s.span.withConfig({displayName:"RangeLabel",componentId:"sc-32k3gq-7"})(["font-size:",";color:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#6b7280"}),Wr=({filters:e,onFiltersChange:t,onReset:r,isLoading:o=!1,showAdvanced:i=!1,onToggleAdvanced:c})=>{var f,u,m,y;const a=(h,g)=>{t({...e,[h]:g})},l=()=>{t({}),r==null||r()},d=Object.values(e).some(h=>h!==void 0&&h!==""&&h!==null);return n.jsxs(Ea,{children:[n.jsxs(dr,{children:[n.jsxs(me,{children:[n.jsx(ge,{children:"Date From"}),n.jsx(xe,{type:"date",value:e.dateFrom||"",onChange:h=>a(de.DATE_FROM,h),disabled:o})]}),n.jsxs(me,{children:[n.jsx(ge,{children:"Date To"}),n.jsx(xe,{type:"date",value:e.dateTo||"",onChange:h=>a(de.DATE_TO,h),disabled:o})]}),n.jsxs(me,{children:[n.jsx(ge,{children:"Model Type"}),n.jsx(Ce,{options:[{value:"",label:"All Models"},{value:"RD-Cont",label:"RD-Cont"},{value:"FVG-RD",label:"FVG-RD"},{value:"True-RD",label:"True-RD"},{value:"IMM-RD",label:"IMM-RD"},{value:"Dispersed-RD",label:"Dispersed-RD"},{value:"Wide-Gap-RD",label:"Wide-Gap-RD"}],value:e.model_type||"",onChange:h=>a(de.MODEL_TYPE,h),disabled:o})]}),n.jsxs(me,{children:[n.jsx(ge,{children:"Session"}),n.jsx(Ce,{options:[{value:"",label:"All Sessions"},{value:"Pre-Market",label:"Pre-Market"},{value:"NY Open",label:"NY Open"},{value:"10:50-11:10",label:"10:50-11:10"},{value:"11:50-12:10",label:"11:50-12:10"},{value:"Lunch Macro",label:"Lunch Macro"},{value:"13:50-14:10",label:"13:50-14:10"},{value:"14:50-15:10",label:"14:50-15:10"},{value:"15:15-15:45",label:"15:15-15:45"},{value:"MOC",label:"MOC"},{value:"Post MOC",label:"Post MOC"}],value:e.session||"",onChange:h=>a(de.SESSION,h),disabled:o})]}),n.jsxs(me,{children:[n.jsx(ge,{children:"Direction"}),n.jsx(Ce,{options:[{value:"",label:"All Directions"},{value:"Long",label:"Long"},{value:"Short",label:"Short"}],value:e.direction||"",onChange:h=>a(de.DIRECTION,h),disabled:o})]}),n.jsxs(me,{children:[n.jsx(ge,{children:"Result"}),n.jsx(Ce,{options:[{value:"",label:"All Results"},{value:"Win",label:"Win"},{value:"Loss",label:"Loss"}],value:e.win_loss||"",onChange:h=>a(de.WIN_LOSS,h),disabled:o})]}),n.jsxs(Ia,{children:[c&&n.jsxs(se,{variant:"outline",size:"small",onClick:c,disabled:o,children:[i?"Hide":"Show"," Advanced"]}),n.jsx(se,{variant:"outline",size:"small",onClick:l,disabled:o||!d,children:"Reset"})]})]}),n.jsx(Ra,{isVisible:i,children:n.jsxs(dr,{children:[n.jsxs(me,{children:[n.jsx(ge,{children:"Market"}),n.jsx(Ce,{options:[{value:"",label:"All Markets"},{value:"MNQ",label:"MNQ"},{value:"NQ",label:"NQ"},{value:"ES",label:"ES"},{value:"MES",label:"MES"},{value:"YM",label:"YM"},{value:"MYM",label:"MYM"}],value:e.market||"",onChange:h=>a(de.MARKET,h),disabled:o})]}),n.jsxs(me,{children:[n.jsx(ge,{children:"R Multiple Range"}),n.jsxs(ur,{children:[n.jsx(xe,{type:"number",placeholder:"Min",step:"0.1",value:((f=e.min_r_multiple)==null?void 0:f.toString())||"",onChange:h=>a(de.MIN_R_MULTIPLE,h?Number(h):void 0),disabled:o,style:{width:"80px"}}),n.jsx(pr,{children:"to"}),n.jsx(xe,{type:"number",placeholder:"Max",step:"0.1",value:((u=e.max_r_multiple)==null?void 0:u.toString())||"",onChange:h=>a(de.MAX_R_MULTIPLE,h?Number(h):void 0),disabled:o,style:{width:"80px"}})]})]}),n.jsxs(me,{children:[n.jsx(ge,{children:"Pattern Quality Range"}),n.jsxs(ur,{children:[n.jsx(xe,{type:"number",placeholder:"Min",min:"1",max:"5",step:"0.1",value:((m=e.min_pattern_quality)==null?void 0:m.toString())||"",onChange:h=>a(de.MIN_PATTERN_QUALITY,h?Number(h):void 0),disabled:o,style:{width:"80px"}}),n.jsx(pr,{children:"to"}),n.jsx(xe,{type:"number",placeholder:"Max",min:"1",max:"5",step:"0.1",value:((y=e.max_pattern_quality)==null?void 0:y.toString())||"",onChange:h=>a(de.MAX_PATTERN_QUALITY,h?Number(h):void 0),disabled:o,style:{width:"80px"}})]})]})]})})]})},ja=s.div.withConfig({displayName:"TableContainer",componentId:"sc-13oxwmo-0"})(["width:100%;overflow:auto;"," ",""],({height:e})=>e&&`height: ${e};`,({scrollable:e})=>e&&"overflow-x: auto;"),ka=s.table.withConfig({displayName:"StyledTable",componentId:"sc-13oxwmo-1"})(["width:100%;border-collapse:separate;border-spacing:0;font-size:",";"," ",""],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"},({bordered:e,theme:t})=>{var r,o;return e&&s.css(["border:1px solid ",";border-radius:",";"],((r=t.colors)==null?void 0:r.border)||"#e5e7eb",((o=t.borderRadius)==null?void 0:o.sm)||"4px")},({compact:e,theme:t})=>{var r,o,i,c;return e?s.css(["th,td{padding:"," ",";}"],((r=t.spacing)==null?void 0:r.xs)||"8px",((o=t.spacing)==null?void 0:o.sm)||"12px"):s.css(["th,td{padding:"," ",";}"],((i=t.spacing)==null?void 0:i.sm)||"12px",((c=t.spacing)==null?void 0:c.md)||"16px")}),Na=s.thead.withConfig({displayName:"TableHeader",componentId:"sc-13oxwmo-2"})(["",""],({stickyHeader:e})=>e&&s.css(["position:sticky;top:0;z-index:1;"])),Da=s.tr.withConfig({displayName:"TableHeaderRow",componentId:"sc-13oxwmo-3"})(["background-color:",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"#f8f9fa"}),fr=s.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-13oxwmo-4"})(["text-align:",";font-weight:",";color:",";border-bottom:1px solid ",";white-space:nowrap;"," "," ",""],({align:e})=>e||"left",({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.semibold)||600},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#6b7280"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#e5e7eb"},({width:e})=>e&&`width: ${e};`,({sortable:e})=>e&&s.css(["cursor:pointer;user-select:none;&:hover{background-color:","aa;}"],({theme:t})=>{var r;return((r=t.colors)==null?void 0:r.background)||"#f8f9fa"}),({isSorted:e,theme:t})=>{var r;return e&&s.css(["color:",";"],((r=t.colors)==null?void 0:r.primary)||"#3b82f6")}),La=s.span.withConfig({displayName:"SortIcon",componentId:"sc-13oxwmo-5"})(["display:inline-block;margin-left:",";&::after{content:'","';}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"8px"},({direction:e})=>e==="asc"?"↑":e==="desc"?"↓":"↕"),Ma=s.tbody.withConfig({displayName:"TableBody",componentId:"sc-13oxwmo-6"})([""]),_a=s.div.withConfig({displayName:"EmptyState",componentId:"sc-13oxwmo-7"})(["padding:",";text-align:center;color:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xl)||"32px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#6b7280"}),Pa=s.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-13oxwmo-8"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:e})=>{var t;return`${((t=e.colors)==null?void 0:t.background)||"#ffffff"}80`}),Fa=s.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-13oxwmo-9"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"#f8f9fa"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#3b82f6"}),Oa=s.div.withConfig({displayName:"PaginationContainer",componentId:"sc-13oxwmo-10"})(["display:flex;justify-content:space-between;align-items:center;padding:"," 0;font-size:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"16px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"}),$a=s.div.withConfig({displayName:"PageInfo",componentId:"sc-13oxwmo-11"})(["color:",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#6b7280"}),Aa=s.div.withConfig({displayName:"PaginationControls",componentId:"sc-13oxwmo-12"})(["display:flex;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"8px"}),za=({data:e,isLoading:t=!1,bordered:r=!0,striped:o=!0,hoverable:i=!0,compact:c=!1,stickyHeader:a=!1,height:l="",onRowClick:d,isRowSelected:f,onSort:u,sortColumn:m="",sortDirection:y="asc",pagination:h=!1,currentPage:g=1,pageSize:b=10,totalRows:T=0,onPageChange:w,onPageSizeChange:C,className:L="",emptyMessage:$="No trades available",scrollable:j=!0,showFilters:k=!1,filters:M={},onFiltersChange:R,columnPreset:N="default",customColumns:W,expandableRows:q=!1,renderExpandedContent:D})=>{const[G,J]=x.useState(!1),Z=x.useMemo(()=>{if(W)return W;switch(N){case"compact":return qr();case"performance":return Ur();default:return Hr()}},[W,N]),le=x.useMemo(()=>Z.filter(V=>!V.hidden),[Z]),ee=x.useMemo(()=>Math.ceil(T/b),[T,b]),F=x.useMemo(()=>{if(!h)return e;const V=(g-1)*b,pe=V+b;return T>0&&e.length<=b?e:e.slice(V,pe)},[e,h,g,b,T]),K=V=>{if(!u)return;u(V,m===V&&y==="asc"?"desc":"asc")},ye=V=>{V<1||V>ee||!w||w(V)};return n.jsxs("div",{children:[k&&R&&n.jsx(Wr,{filters:M,onFiltersChange:R,isLoading:t,showAdvanced:G,onToggleAdvanced:()=>J(!G)}),n.jsxs("div",{style:{position:"relative"},children:[t&&n.jsx(Pa,{children:n.jsx(Fa,{})}),n.jsx(ja,{height:l,scrollable:j,children:n.jsxs(ka,{bordered:r,striped:o,compact:c,className:L,children:[n.jsx(Na,{stickyHeader:a,children:n.jsxs(Da,{children:[q&&n.jsx(fr,{width:"40px",align:"center"}),le.map(V=>n.jsxs(fr,{sortable:V.sortable,isSorted:m===V.id,align:V.align,width:V.width,onClick:()=>V.sortable&&K(V.id),children:[V.header,V.sortable&&n.jsx(La,{direction:m===V.id?y:void 0})]},V.id))]})}),n.jsx(Ma,{children:F.length>0?F.map((V,pe)=>n.jsx(Yr,{trade:V,index:pe,columns:le,isSelected:f?f(V,pe):!1,hoverable:i,striped:o,expandable:q,onRowClick:d,expandedContent:D==null?void 0:D(V)},V.trade.id||pe)):n.jsx("tr",{children:n.jsx("td",{colSpan:le.length+(q?1:0),children:n.jsx(_a,{children:$})})})})]})}),h&&ee>0&&n.jsxs(Oa,{children:[n.jsxs($a,{children:["Showing ",Math.min((g-1)*b+1,T)," to"," ",Math.min(g*b,T)," of ",T," entries"]}),n.jsxs(Aa,{children:[n.jsx(se,{size:"small",variant:"outline",onClick:()=>ye(1),disabled:g===1,children:"First"}),n.jsx(se,{size:"small",variant:"outline",onClick:()=>ye(g-1),disabled:g===1,children:"Prev"}),n.jsx(se,{size:"small",variant:"outline",onClick:()=>ye(g+1),disabled:g===ee,children:"Next"}),n.jsx(se,{size:"small",variant:"outline",onClick:()=>ye(ee),disabled:g===ee,children:"Last"})]})]})]})]})},Va=s.div.withConfig({displayName:"HeaderActions",componentId:"sc-1l7c7gv-0"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),Ga=({title:e,children:t,isLoading:r=!1,hasError:o=!1,errorMessage:i="An error occurred while loading data",showRetry:c=!0,onRetry:a,isEmpty:l=!1,emptyMessage:d="No data available",emptyActionText:f,onEmptyAction:u,actionButton:m,className:y,...h})=>{const g=n.jsx(Va,{children:m});let b;return r?b=n.jsx(Dr,{variant:"card",text:"Loading data..."}):o?b=n.jsx(Ct,{title:"Error",description:i,variant:"compact",actionText:c?"Retry":void 0,onAction:c?a:void 0}):l?b=n.jsx(Ct,{title:"No Data",description:d,variant:"compact",actionText:f,onAction:u}):b=t,n.jsx(Pr,{title:e,actions:g,className:y,...h,children:b})},Ba=s.div.withConfig({displayName:"SectionContainer",componentId:"sc-14y246p-0"})(["background:",";border:1px solid ",";border-radius:",";padding:",";margin-bottom:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.lg,({theme:e})=>e.spacing.lg),Ha=s.div.withConfig({displayName:"SectionHeader",componentId:"sc-14y246p-1"})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:",";padding-bottom:",";border-bottom:1px solid ",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border),qa=s.h2.withConfig({displayName:"SectionTitle",componentId:"sc-14y246p-2"})(["color:",";font-size:",";font-weight:600;margin:0;"],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontSizes.lg),Ua=s.div.withConfig({displayName:"SectionActions",componentId:"sc-14y246p-3"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),Ya=s.div.withConfig({displayName:"SectionContent",componentId:"sc-14y246p-4"})(["min-height:200px;"]),mr=s.div.withConfig({displayName:"LoadingState",componentId:"sc-14y246p-5"})(["display:flex;align-items:center;justify-content:center;min-height:200px;color:",";"],({theme:e})=>e.colors.textSecondary),Wa=s.div.withConfig({displayName:"ErrorState",componentId:"sc-14y246p-6"})(["display:flex;align-items:center;justify-content:center;min-height:200px;color:",";text-align:center;"],({theme:e})=>e.colors.danger),Ka=({name:e,title:t,children:r,actions:o,isLoading:i=!1,error:c=null,className:a,collapsible:l=!1,defaultCollapsed:d=!1})=>{const[f,u]=x.useState(d),m=()=>{l&&u(!f)},y=t||e.charAt(0).toUpperCase()+e.slice(1),h=()=>c?n.jsx(Wa,{children:n.jsxs("div",{children:[n.jsxs("div",{children:["Error loading ",e]}),n.jsx("div",{style:{fontSize:"0.9em",marginTop:"8px"},children:c})]})}):i?n.jsxs(mr,{children:["Loading ",e,"..."]}):r||n.jsxs(mr,{children:["No ",e," data available"]});return n.jsxs(Ba,{className:a,"data-section":e,children:[n.jsxs(Ha,{children:[n.jsxs(qa,{onClick:m,style:{cursor:l?"pointer":"default"},children:[y,l&&n.jsx("span",{style:{marginLeft:"8px",fontSize:"0.8em"},children:f?"▶":"▼"})]}),o&&n.jsx(Ua,{children:o})]}),!f&&n.jsx(Ya,{children:h()})]})},Qa=Ka,Xa=s.div.withConfig({displayName:"Container",componentId:"sc-djltr5-0"})(["display:grid;grid-template-areas:'header header' 'sidebar content';grid-template-columns:",";grid-template-rows:auto 1fr;height:100vh;width:100%;overflow:hidden;transition:grid-template-columns "," ease;"],({sidebarCollapsed:e})=>e?"auto 1fr":"240px 1fr",({theme:e})=>e.transitions.normal),Ja=s.header.withConfig({displayName:"HeaderContainer",componentId:"sc-djltr5-1"})(["grid-area:header;background-color:",";border-bottom:1px solid ",";padding:",";z-index:",";"],({theme:e})=>e.colors.headerBackground,({theme:e})=>e.colors.border,({theme:e})=>e.spacing.md,({theme:e})=>e.zIndex.fixed),Za=s.aside.withConfig({displayName:"SidebarContainer",componentId:"sc-djltr5-2"})(["grid-area:sidebar;background-color:",";border-right:1px solid ",";overflow-y:auto;transition:width "," ease;width:",";"],({theme:e})=>e.colors.sidebarBackground,({theme:e})=>e.colors.border,({theme:e})=>e.transitions.normal,({collapsed:e})=>e?"60px":"240px"),ec=s.main.withConfig({displayName:"ContentContainer",componentId:"sc-djltr5-3"})(["grid-area:content;overflow-y:auto;padding:",";background-color:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.background),tc=({header:e,sidebar:t,children:r,sidebarCollapsed:o=!1,className:i})=>n.jsxs(Xa,{sidebarCollapsed:o,className:i,children:[n.jsx(Ja,{children:e}),n.jsx(Za,{collapsed:o,children:t}),n.jsx(ec,{children:r})]}),rc=s.div.withConfig({displayName:"BuilderContainer",componentId:"sc-5duzr2-0"})(["background:#1a1a1a;border:1px solid #4b5563;border-radius:8px;padding:24px;margin-bottom:16px;"]),oc=s.h3.withConfig({displayName:"SectionTitle",componentId:"sc-5duzr2-1"})(["color:#ffffff;font-size:1.1rem;font-weight:600;margin-bottom:16px;border-bottom:2px solid #dc2626;padding-bottom:8px;"]),nc=s.div.withConfig({displayName:"MatrixGrid",componentId:"sc-5duzr2-2"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px;margin-bottom:20px;"]),et=s.div.withConfig({displayName:"ElementSection",componentId:"sc-5duzr2-3"})(["background:#262626;border:1px solid #4b5563;border-radius:6px;padding:16px;"]),Me=s.h4.withConfig({displayName:"ElementTitle",componentId:"sc-5duzr2-4"})(["color:#ffffff;font-size:0.9rem;font-weight:600;margin-bottom:12px;text-transform:uppercase;letter-spacing:0.5px;"]),tt=s.select.withConfig({displayName:"Select",componentId:"sc-5duzr2-5"})(["width:100%;padding:8px 12px;background:#0f0f0f;border:1px solid #4b5563;border-radius:4px;color:#ffffff;font-size:0.9rem;&:focus{outline:none;border-color:#dc2626;box-shadow:0 0 0 2px rgba(220,38,38,0.2);}option{background:#0f0f0f;color:#ffffff;}"]),sc=s.div.withConfig({displayName:"PreviewContainer",componentId:"sc-5duzr2-6"})(["background:#0f0f0f;border:1px solid #4b5563;border-radius:6px;padding:16px;margin-top:16px;"]),ic=s.div.withConfig({displayName:"PreviewText",componentId:"sc-5duzr2-7"})(["color:#ffffff;font-family:'Monaco','Menlo','Ubuntu Mono',monospace;font-size:0.9rem;line-height:1.4;min-height:20px;"]),gr=s.span.withConfig({displayName:"RequiredIndicator",componentId:"sc-5duzr2-8"})(["color:#dc2626;margin-left:4px;"]),hr=s.span.withConfig({displayName:"OptionalIndicator",componentId:"sc-5duzr2-9"})(["color:#9ca3af;font-size:0.8rem;margin-left:4px;"]),ac=({onSetupChange:e,initialComponents:t})=>{const[r,o]=x.useState({constant:(t==null?void 0:t.constant)||"",action:(t==null?void 0:t.action)||"None",variable:(t==null?void 0:t.variable)||"None",entry:(t==null?void 0:t.entry)||""});x.useEffect(()=>{r.constant&&r.entry&&e(r)},[r,e]);const i=(a,l)=>{o(d=>({...d,[a]:l}))},c=()=>{const{constant:a,action:l,variable:d,entry:f}=r;if(!a||!f)return"Select required elements to see setup preview...";let u=a;return l&&l!=="None"&&(u+=` → ${l}`),d&&d!=="None"&&(u+=` → ${d}`),u+=` [${f}]`,u};return n.jsxs(rc,{children:[n.jsx(oc,{children:"Setup Construction Matrix"}),n.jsxs(nc,{children:[n.jsxs(et,{children:[n.jsxs(Me,{children:["Constant Element",n.jsx(gr,{children:"*"})]}),n.jsxs(tt,{value:r.constant,onChange:a=>i("constant",a.target.value),children:[n.jsx("option",{value:"",children:"Select Constant"}),we.constant.parentArrays.map(a=>n.jsx("option",{value:a,children:a},a)),we.constant.fvgTypes.map(a=>n.jsx("option",{value:a,children:a},a))]})]}),n.jsxs(et,{children:[n.jsxs(Me,{children:["Action Element",n.jsx(hr,{children:"(optional)"})]}),n.jsxs(tt,{value:r.action,onChange:a=>i("action",a.target.value),children:[n.jsx("option",{value:"None",children:"None"}),we.action.liquidityEvents.map(a=>n.jsx("option",{value:a,children:a},a))]})]}),n.jsxs(et,{children:[n.jsxs(Me,{children:["Variable Element",n.jsx(hr,{children:"(optional)"})]}),n.jsxs(tt,{value:r.variable,onChange:a=>i("variable",a.target.value),children:[n.jsx("option",{value:"None",children:"None"}),we.variable.rdTypes.map(a=>n.jsx("option",{value:a,children:a},a))]})]}),n.jsxs(et,{children:[n.jsxs(Me,{children:["Entry Method",n.jsx(gr,{children:"*"})]}),n.jsxs(tt,{value:r.entry,onChange:a=>i("entry",a.target.value),children:[n.jsx("option",{value:"",children:"Select Entry Method"}),we.entry.methods.map(a=>n.jsx("option",{value:a,children:a},a))]})]})]}),n.jsxs(sc,{children:[n.jsx(Me,{children:"Setup Preview"}),n.jsx(ic,{children:c()})]})]})},cc=ac,yr=s.div.withConfig({displayName:"MetricsContainer",componentId:"sc-opkdti-0"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:",";"],({theme:e})=>e.spacing.md),br=s.div.withConfig({displayName:"MetricCard",componentId:"sc-opkdti-1"})(["background:",";border:1px solid ",";border-radius:",";padding:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.md),xr=s.div.withConfig({displayName:"MetricLabel",componentId:"sc-opkdti-2"})(["color:",";font-size:",";margin-bottom:",";"],({theme:e})=>e.colors.textSecondary,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.xs),vr=s.div.withConfig({displayName:"MetricValue",componentId:"sc-opkdti-3"})(["color:",";font-size:",";font-weight:600;"],({theme:e,positive:t,negative:r})=>t?e.colors.success:r?e.colors.danger:e.colors.textPrimary,({theme:e})=>e.fontSizes.lg),lc=({metrics:e,isLoading:t})=>t?n.jsx(yr,{children:Array.from({length:4}).map((r,o)=>n.jsxs(br,{children:[n.jsx(xr,{children:"Loading..."}),n.jsx(vr,{children:"--"})]},o))}):n.jsx(yr,{children:e.map((r,o)=>n.jsxs(br,{children:[n.jsx(xr,{children:r.label}),n.jsx(vr,{positive:r.positive,negative:r.negative,children:r.value})]},o))}),dc=lc,uc=s.div.withConfig({displayName:"AnalysisContainer",componentId:"sc-tp1ymt-0"})(["background:",";border:1px solid ",";border-radius:",";padding:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.lg),pc=s.h3.withConfig({displayName:"AnalysisTitle",componentId:"sc-tp1ymt-1"})(["color:",";font-size:",";font-weight:600;margin-bottom:",";"],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontSizes.lg,({theme:e})=>e.spacing.md),fc=s.div.withConfig({displayName:"AnalysisContent",componentId:"sc-tp1ymt-2"})(["color:",";line-height:1.6;"],({theme:e})=>e.colors.textSecondary),mc=({title:e="Trade Analysis",children:t,isLoading:r})=>n.jsxs(uc,{children:[n.jsx(pc,{children:e}),n.jsx(fc,{children:r?n.jsx("div",{children:"Loading analysis..."}):t||n.jsx("div",{children:"No analysis data available"})})]}),gc=mc,v={f1Red:"#e10600",f1RedDark:"#c10500",f1RedLight:"#ff3b36",f1Blue:"#0600EF",f1BlueDark:"#0500CC",f1BlueLight:"#4169E1",f1MercedesGreen:"#00D2BE",f1MercedesGreenDark:"#00A896",f1MercedesGreenLight:"#00FFE5",f1McLarenOrange:"#FF8700",f1McLarenOrangeDark:"#E67600",f1McLarenOrangeLight:"#FFA500",f1RacingYellow:"#FFD320",f1RacingYellowDark:"#E6BE1D",f1RacingYellowLight:"#FFDC4A",f1Carbon:"#1A1A1A",f1Silver:"#C0C0C0",white:"#ffffff",black:"#000000",gray50:"#f9fafb",gray100:"#f3f4f6",gray200:"#e5e7eb",gray300:"#d1d5db",gray400:"#9ca3af",gray500:"#6b7280",gray600:"#4b5563",gray700:"#374151",gray800:"#1f2937",gray900:"#111827",green:"#4caf50",greenDark:"#388e3c",greenLight:"#81c784",yellow:"#ffeb3b",yellowDark:"#fbc02d",yellowLight:"#fff59d",orange:"#ff9800",orangeDark:"#f57c00",orangeLight:"#ffb74d",red:"#f44336",redDark:"#d32f2f",redLight:"#e57373",blue:"#2196f3",blueDark:"#1976d2",blueLight:"#64b5f6",purple:"#9c27b0",purpleDark:"#7b1fa2",purpleLight:"#ba68c8",whiteTransparent10:"rgba(255, 255, 255, 0.1)",blackTransparent10:"rgba(0, 0, 0, 0.1)"},te={background:"#0f0f0f",surface:"#1a1a1a",cardBackground:"#1a1a1a",border:"#333333",divider:"rgba(255, 255, 255, 0.1)",textPrimary:"#ffffff",textSecondary:"#aaaaaa",textDisabled:"#666666",textInverse:"#1a1f2c",success:v.green,warning:v.yellow,error:v.red,info:v.blue,chartGrid:"rgba(255, 255, 255, 0.1)",chartLine:v.f1Red,profit:v.green,loss:v.red,neutral:v.gray400,tooltipBackground:"rgba(37, 42, 55, 0.9)",modalBackground:"rgba(26, 31, 44, 0.8)"},hc={background:"#f5f5f5",surface:"#ffffff",cardBackground:"#ffffff",border:"#e0e0e0",divider:"rgba(0, 0, 0, 0.1)",textPrimary:"#333333",textSecondary:"#666666",textDisabled:"#999999",textInverse:"#ffffff",success:v.green,warning:v.yellow,error:v.red,info:v.blue,chartGrid:"rgba(0, 0, 0, 0.1)",chartLine:v.f1Red,profit:v.green,loss:v.red,neutral:v.gray400,tooltipBackground:"rgba(255, 255, 255, 0.9)",modalBackground:"rgba(255, 255, 255, 0.8)"},Y={xxs:"4px",xs:"8px",sm:"12px",md:"16px",lg:"24px",xl:"32px",xxl:"48px"},ie={xs:"0.75rem",sm:"0.875rem",md:"1rem",lg:"1.125rem",xl:"1.25rem",xxl:"1.5rem",xxxl:"2.5rem",h1:"2.5rem",h2:"2rem",h3:"1.75rem",h4:"1.5rem",h5:"1.25rem",h6:"1rem"},Ae={light:300,regular:400,medium:500,semibold:600,bold:700},ze={tight:1.25,normal:1.5,relaxed:1.75},Ve={body:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",heading:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",mono:"SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace"},Ge={xs:"480px",sm:"640px",md:"768px",lg:"1024px",xl:"1280px"},Be={xs:"2px",sm:"4px",md:"6px",lg:"8px",xl:"12px",pill:"9999px",circle:"50%"},He={sm:"0 1px 3px rgba(0, 0, 0, 0.1)",md:"0 4px 6px rgba(0, 0, 0, 0.1)",lg:"0 10px 15px rgba(0, 0, 0, 0.1)"},qe={fast:"0.1s",normal:"0.3s",slow:"0.5s"},Ue={base:1,overlay:10,modal:20,popover:30,tooltip:40,fixed:100},yc=s.div.withConfig({displayName:"HeaderContainer",componentId:"sc-e71xhh-0"})(["display:flex;justify-content:space-between;align-items:center;padding:"," 0;border-bottom:2px solid #4b5563;margin-bottom:",";",""],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"16px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"16px"},({$variant:e})=>{switch(e){case"dashboard":return s.css(["padding:24px 0;margin-bottom:24px;"]);case"form":return s.css(["padding:16px 0;margin-bottom:16px;"]);default:return s.css(["padding:20px 0;margin-bottom:20px;"])}}),bc=s.div.withConfig({displayName:"TitleSection",componentId:"sc-e71xhh-1"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"}),xc=s.h1.withConfig({displayName:"MainTitle",componentId:"sc-e71xhh-2"})(["font-weight:700;color:",";margin:0;letter-spacing:-0.025em;text-transform:uppercase;font-family:'Inter',-apple-system,BlinkMacSystemFont,sans-serif;"," span{color:",";}"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({$variant:e})=>{switch(e){case"dashboard":return s.css(["font-size:",";"],ie.xxxl);case"analysis":return s.css(["font-size:",";"],ie.xxl);case"form":return s.css(["font-size:",";"],ie.xl);default:return s.css(["font-size:",";"],ie.xxl)}},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#dc2626"}),vc=s.div.withConfig({displayName:"Subtitle",componentId:"sc-e71xhh-3"})(["font-size:",";color:#9ca3af;font-weight:500;text-transform:uppercase;letter-spacing:0.05em;"],ie.sm),Sc=s.div.withConfig({displayName:"ActionsSection",componentId:"sc-e71xhh-4"})(["display:flex;align-items:center;gap:",";flex-wrap:wrap;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"}),wc=s.div.withConfig({displayName:"StatusIndicator",componentId:"sc-e71xhh-5"})(["display:flex;align-items:center;gap:",";padding:"," ",";border-radius:",";font-size:",";font-weight:600;text-transform:uppercase;letter-spacing:0.05em;",""],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.sm)||"4px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"},({$isLive:e,$variant:t,theme:r})=>{var o,i,c,a,l,d,f,u,m;return e?s.css(["background:","20;border:1px solid ",";color:",";"],((o=r.colors)==null?void 0:o.sessionActive)||"#00D2BE",((i=r.colors)==null?void 0:i.sessionActive)||"#00D2BE",((c=r.colors)==null?void 0:c.sessionActive)||"#00D2BE"):t==="active"?s.css(["background:","20;border:1px solid ",";color:",";"],((a=r.colors)==null?void 0:a.sessionOptimal)||"#00FFE5",((l=r.colors)==null?void 0:l.sessionOptimal)||"#00FFE5",((d=r.colors)==null?void 0:d.sessionOptimal)||"#00FFE5"):s.css(["background:","20;border:1px solid ",";color:",";"],((f=r.colors)==null?void 0:f.textSecondary)||"#9ca3af",((u=r.colors)==null?void 0:u.textSecondary)||"#9ca3af",((m=r.colors)==null?void 0:m.textSecondary)||"#9ca3af")}),Cc=s.div.withConfig({displayName:"StatusDot",componentId:"sc-e71xhh-6"})(["width:6px;height:6px;border-radius:50%;background:",";",""],({$isLive:e,theme:t})=>{var r,o;return e?((r=t.colors)==null?void 0:r.sessionActive)||"#00D2BE":((o=t.colors)==null?void 0:o.sessionOptimal)||"#00FFE5"},({$isLive:e})=>e&&s.css(["animation:mercedesPulse 2s infinite;@keyframes mercedesPulse{0%,100%{opacity:1;transform:scale(1);}50%{opacity:0.7;transform:scale(1.2);}}"])),Tc=s.button.withConfig({displayName:"RefreshButton",componentId:"sc-e71xhh-7"})(["padding:"," ",";background:transparent;color:",";border:1px solid #4b5563;border-radius:",";cursor:pointer;font-weight:500;font-size:",";transition:all 0.2s ease;min-width:100px;position:relative;&:hover{background:#4b5563;color:",";border-color:",";}&:disabled{opacity:0.6;cursor:not-allowed;}",""],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.sm)||"4px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#dc2626"},({$isRefreshing:e})=>e&&s.css(["&::after{content:'';position:absolute;top:50%;left:50%;width:16px;height:16px;margin:-8px 0 0 -8px;border:2px solid transparent;border-top:2px solid currentColor;border-radius:50%;animation:spin 1s linear infinite;}@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"])),Ec=s.div.withConfig({displayName:"CustomActions",componentId:"sc-e71xhh-8"})(["display:flex;align-items:center;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"}),Ic=e=>{const{title:t,subtitle:r,isLive:o=!1,liveText:i="LIVE SESSION",statusText:c,onRefresh:a,isRefreshing:l=!1,actions:d,variant:f="dashboard",className:u}=e,m=o?i:c;return n.jsxs(yc,{$variant:f,className:u,children:[n.jsxs(bc,{children:[n.jsx(xc,{$variant:f,children:f==="dashboard"?n.jsxs(n.Fragment,{children:["🏎️ ",t.replace("Trading","TRADING").replace("Dashboard","DASHBOARD")]}):t}),r&&n.jsx(vc,{children:r})]}),n.jsxs(Sc,{children:[m&&n.jsxs(wc,{$isLive:o,$variant:!o&&c?"active":void 0,children:[n.jsx(Cc,{$isLive:o}),m]}),a&&n.jsx(Tc,{onClick:a,disabled:l,$isRefreshing:l,children:l?"Refreshing...":"Refresh"}),d&&n.jsx(Ec,{children:d})]})]})},bt=s.div.withConfig({displayName:"Container",componentId:"sc-vuv4tf-0"})(["display:flex;flex-direction:column;width:100%;max-width:",";margin:0 auto;min-height:",";"," "," "," ",""],({$maxWidth:e})=>typeof e=="number"?`${e}px`:e,({$variant:e})=>e==="dashboard"?"100vh":"auto",({$padding:e})=>{const t={sm:Y.sm,md:Y.md,lg:Y.lg,xl:Y.xl};return s.css(["padding:",";"],t[e||"lg"])},({$background:e,theme:t})=>{const r={default:t.colors.background,surface:t.colors.surface,elevated:t.colors.elevated};return s.css(["background:",";"],r[e||"default"])},({$variant:e})=>{switch(e){case"dashboard":return s.css(["gap:24px;padding-top:0;"]);case"form":return s.css(["gap:16px;max-width:800px;"]);case"analysis":return s.css(["gap:20px;max-width:1400px;"]);case"settings":return s.css(["gap:16px;max-width:1000px;"]);default:return s.css(["gap:16px;"])}},({$animated:e})=>e&&s.css(["transition:all 0.3s ease-in-out;&.entering{opacity:0;transform:translateY(20px);}&.entered{opacity:1;transform:translateY(0);}"])),Rc=s.div.withConfig({displayName:"LoadingContainer",componentId:"sc-vuv4tf-1"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:400px;gap:16px;color:#9ca3af;"]),jc=s.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-vuv4tf-2"})(["width:40px;height:40px;border:3px solid #4b5563;border-top:3px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]),kc=s.div.withConfig({displayName:"ErrorContainer",componentId:"sc-vuv4tf-3"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px;background:rgba(244,67,54,0.1);border:1px solid #f44336;border-radius:8px;color:#f44336;text-align:center;gap:16px;"]),Nc=s.div.withConfig({displayName:"ErrorIcon",componentId:"sc-vuv4tf-4"})(["font-size:48px;opacity:0.8;"]),Dc=s.div.withConfig({displayName:"ErrorMessage",componentId:"sc-vuv4tf-5"})(["font-size:16px;font-weight:500;"]),Lc=s.button.withConfig({displayName:"RetryButton",componentId:"sc-vuv4tf-6"})(["padding:8px 16px;background:#f44336;color:white;border:none;border-radius:4px;cursor:pointer;font-weight:500;transition:background 0.2s ease;&:hover{background:#d32f2f;}"]),Sr=()=>n.jsxs(Rc,{children:[n.jsx(jc,{}),n.jsx("div",{children:"Loading..."})]}),Mc=({error:e,onRetry:t})=>n.jsxs(kc,{children:[n.jsx(Nc,{children:"⚠️"}),n.jsx(Dc,{children:e}),t&&n.jsx(Lc,{onClick:t,children:"Retry"})]}),_c=e=>{const{children:t,variant:r="dashboard",maxWidth:o="100%",padding:i="lg",isLoading:c=!1,error:a=null,loadingFallback:l,errorFallback:d,className:f,animated:u=!0,background:m="default"}=e,y={$variant:r,$maxWidth:o,$padding:i,$animated:u,$background:m};return a?n.jsx(bt,{...y,className:f,children:d||n.jsx(Mc,{error:a})}):c?n.jsx(bt,{...y,className:f,children:l||n.jsx(Sr,{})}):n.jsx(bt,{...y,className:f,children:n.jsx(x.Suspense,{fallback:l||n.jsx(Sr,{}),children:t})})},Pc=s.form.withConfig({displayName:"FormContainer",componentId:"sc-1gwzj6e-0"})(["display:flex;flex-direction:column;gap:",";background:",";border-radius:",";border:1px solid ",";position:relative;"," "," ",""],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"#1f2937"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.lg)||"8px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"#4b5563"},({$variant:e})=>{switch(e){case"quick":return s.css(["padding:",";max-width:600px;"],Y.lg);case"detailed":return s.css(["padding:",";max-width:800px;"],Y.xl);case"modal":return s.css(["padding:",";max-width:500px;margin:0 auto;"],Y.lg);case"inline":return s.css(["padding:",";background:transparent;border:none;"],Y.md);default:return s.css(["padding:",";"],Y.lg)}},({$showAccent:e,theme:t})=>{var r,o,i,c,a;return e&&s.css(["&::before{content:'';position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient( 90deg,",",",","," );border-radius:"," "," 0 0;}"],((r=t.colors)==null?void 0:r.primary)||"#dc2626",((o=t.colors)==null?void 0:o.primaryDark)||"#b91c1c",((i=t.colors)==null?void 0:i.primary)||"#dc2626",((c=t.borderRadius)==null?void 0:c.lg)||"8px",((a=t.borderRadius)==null?void 0:a.lg)||"8px")},({$disabled:e})=>e&&s.css(["opacity:0.6;pointer-events:none;"])),Fc=s.div.withConfig({displayName:"FormHeader",componentId:"sc-1gwzj6e-1"})(["display:flex;flex-direction:column;gap:",";margin-bottom:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"}),Oc=s.h3.withConfig({displayName:"FormTitle",componentId:"sc-1gwzj6e-2"})(["font-size:",";font-weight:700;color:",";margin:0;text-transform:uppercase;letter-spacing:0.025em;display:flex;align-items:center;gap:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.lg)||"1.125rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"}),$c=s.div.withConfig({displayName:"FormSubtitle",componentId:"sc-1gwzj6e-3"})(["font-size:",";color:",";font-weight:500;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"}),Ac=s.div.withConfig({displayName:"FormContent",componentId:"sc-1gwzj6e-4"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"}),wr=s.div.withConfig({displayName:"FormMessage",componentId:"sc-1gwzj6e-5"})(["padding:"," ",";border-radius:",";font-size:",";font-weight:500;display:flex;align-items:center;gap:",";",""],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.sm)||"4px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({$type:e})=>{switch(e){case"error":return s.css(["background:rgba(244,67,54,0.1);border:1px solid #f44336;color:#f44336;"]);case"success":return s.css(["background:rgba(34,197,94,0.1);border:1px solid #22c55e;color:#22c55e;"]);case"info":return s.css(["background:rgba(59,130,246,0.1);border:1px solid #3b82f6;color:#3b82f6;"])}}),zc=s.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-1gwzj6e-6"})(["position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;border-radius:",";z-index:10;"],({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.lg)||"8px"}),Vc=s.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-1gwzj6e-7"})(["width:32px;height:32px;border:3px solid #4b5563;border-top:3px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]),Gc=s.div.withConfig({displayName:"AutoSaveIndicator",componentId:"sc-1gwzj6e-8"})(["position:absolute;top:8px;right:8px;font-size:",";color:",";opacity:",";transition:opacity 0.3s ease;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"},({$visible:e})=>e?1:0),Bc=e=>{const{children:t,onSubmit:r,title:o,subtitle:i,isSubmitting:c=!1,error:a=null,success:l=null,variant:d="quick",showAccent:f=!0,className:u,disabled:m=!1,autoSave:y=!1}=e,h=async g=>{g.preventDefault(),r&&!c&&!m&&await r(g)};return n.jsxs(Pc,{$variant:d,$showAccent:f,$disabled:m,className:u,onSubmit:h,noValidate:!0,children:[c&&n.jsx(zc,{children:n.jsx(Vc,{})}),y&&n.jsx(Gc,{$visible:!c,children:"Auto-save enabled"}),(o||i)&&n.jsxs(Fc,{children:[o&&n.jsx(Oc,{children:o}),i&&n.jsx($c,{children:i})]}),a&&n.jsxs(wr,{$type:"error",children:["⚠️ ",a]}),l&&n.jsxs(wr,{$type:"success",children:["✅ ",l]}),n.jsx(Ac,{children:t})]})},Hc=s.div.withConfig({displayName:"FieldContainer",componentId:"sc-sq94oz-0"})(["display:flex;flex-direction:column;gap:",";"],({$size:e})=>({sm:Y.xs,md:Y.sm,lg:Y.md})[e||"md"]),qc=s.label.withConfig({displayName:"Label",componentId:"sc-sq94oz-1"})(["font-size:",";font-weight:600;color:",";display:flex;align-items:center;gap:",";"," ",""],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({$variant:e})=>{switch(e){case"trading":return s.css(["text-transform:uppercase;letter-spacing:0.025em;"]);case"analysis":return s.css(["font-weight:500;"]);default:return s.css([""])}},({$required:e,theme:t})=>{var r;return e&&s.css(["&::after{content:'*';color:",";margin-left:2px;}"],((r=t.colors)==null?void 0:r.primary)||"#dc2626")}),Uc=s.div.withConfig({displayName:"InputContainer",componentId:"sc-sq94oz-2"})(["position:relative;display:flex;align-items:center;",""],({$disabled:e})=>e&&s.css(["opacity:0.6;pointer-events:none;"])),Dt=s.css(["width:100%;border:1px solid ",";border-radius:",";background:",";color:",";font-family:inherit;transition:all 0.2s ease;"," &:focus{outline:none;border-color:",";box-shadow:0 0 0 2px rgba(220,38,38,0.2);}&:disabled{background:#374151;color:#9ca3af;cursor:not-allowed;}&::placeholder{color:#6b7280;}"],({$hasError:e,theme:t})=>{var r;return e?((r=t.colors)==null?void 0:r.error)||"#f44336":"#4b5563"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.sm)||"4px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"#111827"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({$size:e})=>({sm:s.css(["padding:"," ",";font-size:",";"],Y.xs,Y.sm,ie.sm),md:s.css(["padding:"," ",";font-size:",";"],Y.sm,Y.md,ie.md),lg:s.css(["padding:"," ",";font-size:",";"],Y.md,Y.lg,ie.lg)})[e||"md"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"#dc2626"}),Yc=s.input.withConfig({displayName:"Input",componentId:"sc-sq94oz-3"})(["",""],Dt),Wc=s.select.withConfig({displayName:"Select",componentId:"sc-sq94oz-4"})([""," cursor:pointer;option{background:",";color:",";}"],Dt,({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"#111827"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"}),Kc=s.textarea.withConfig({displayName:"TextArea",componentId:"sc-sq94oz-5"})([""," resize:vertical;min-height:80px;font-family:inherit;"],Dt),Qc=s.div.withConfig({displayName:"PrefixContainer",componentId:"sc-sq94oz-6"})(["position:absolute;left:12px;display:flex;align-items:center;color:",";pointer-events:none;z-index:1;"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"}),Xc=s.div.withConfig({displayName:"SuffixContainer",componentId:"sc-sq94oz-7"})(["position:absolute;right:12px;display:flex;align-items:center;color:",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"}),Jc=s.div.withConfig({displayName:"ErrorMessage",componentId:"sc-sq94oz-8"})(["font-size:",";color:",";font-weight:500;display:flex;align-items:center;gap:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.error)||"#f44336"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"}),Zc=s.div.withConfig({displayName:"HelpText",componentId:"sc-sq94oz-9"})(["font-size:",";color:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"#9ca3af"}),el=s.div.withConfig({displayName:"ValidationIndicator",componentId:"sc-sq94oz-10"})(["position:absolute;right:8px;display:flex;align-items:center;"," ",""],({$validating:e})=>e&&s.css(["&::after{content:'';width:12px;height:12px;border:2px solid #4b5563;border-top:2px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;}@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]),({$valid:e,$validating:t})=>!t&&s.css(["color:",";&::after{content:'","';}"],e?"#22c55e":"#f44336",e?"✓":"✗")),tl=e=>{const{label:t,field:r,type:o="text",placeholder:i,required:c=!1,disabled:a=!1,helpText:l,options:d=[],inputProps:f={},className:u,size:m="md",variant:y="default",prefix:h,suffix:g}=e,b=!!(r.error&&r.touched),T=r.touched&&!r.validating,w=()=>{const C={id:f.id||t.toLowerCase().replace(/\s+/g,"-"),value:r.value,onChange:r.setValue,onBlur:()=>r.setTouched(!0),disabled:a,placeholder:i,$hasError:b,$size:m,...f};switch(o){case"select":return n.jsxs(Wc,{...C,children:[i&&n.jsx("option",{value:"",disabled:!0,children:i}),d.map(L=>n.jsx("option",{value:L.value,children:L.label},L.value))]});case"textarea":return n.jsx(Kc,{...C});default:return n.jsx(Yc,{...C,type:o})}};return n.jsxs(Hc,{$size:m,className:u,children:[n.jsx(qc,{$required:c,$variant:y,htmlFor:f.id||t.toLowerCase().replace(/\s+/g,"-"),children:t}),n.jsxs(Uc,{$hasError:b,$disabled:a,children:[h&&n.jsx(Qc,{children:h}),w(),g&&n.jsx(Xc,{children:g}),T&&n.jsx(el,{$valid:r.valid,$validating:r.validating})]}),b&&n.jsxs(Jc,{children:["⚠️ ",r.error]}),l&&!b&&n.jsx(Zc,{children:l})]})},Kr=(e=!1)=>{const[t,r]=x.useState(e),[o,i]=x.useState(null),[c,a]=x.useState(!1),l=x.useCallback(h=>{r(h),h&&(i(null),a(!1))},[]),d=x.useCallback(h=>{i(h),r(!1),a(!1)},[]),f=x.useCallback(()=>{i(null)},[]),u=x.useCallback(()=>{r(!1),i(null),a(!1)},[]),m=x.useCallback(async h=>{l(!0);try{const g=await h();return a(!0),r(!1),g}catch(g){const b=g instanceof Error?g.message:"An unexpected error occurred";throw d(b),g}},[l,d]),y=x.useCallback(h=>async(...g)=>{try{await m(()=>h(...g))}catch(b){console.error("Operation failed:",b)}},[m]);return{isLoading:t,error:o,isSuccess:c,isError:o!==null,setLoading:l,setError:d,clearError:f,reset:u,withLoading:m,withLoadingCallback:y}};function rl(e,t={}){const{fetchOnMount:r=!0,dependencies:o=[]}=t,[i,c]=x.useState({data:null,isLoading:!1,error:null,isInitialized:!1}),a=x.useCallback(async(...l)=>{c(d=>({...d,isLoading:!0,error:null}));try{const d=await e(...l);return c({data:d,isLoading:!1,error:null,isInitialized:!0}),d}catch(d){const f=d instanceof Error?d:new Error(String(d));throw c(u=>({...u,isLoading:!1,error:f,isInitialized:!0})),f}},[e]);return x.useEffect(()=>{r&&a()},[r,a,...o]),{...i,fetchData:a,refetch:()=>a()}}function ol(e,t){const[r,o]=x.useState(e);return x.useEffect(()=>{const i=setTimeout(()=>{o(e)},t);return()=>{clearTimeout(i)}},[e,t]),r}function nl(e={}){const{componentName:t,logToConsole:r=!0,reportToMonitoring:o=!0,onError:i}=e,[c,a]=x.useState(null),[l,d]=x.useState(!1),f=x.useCallback(y=>{if(a(y),d(!0),r){const h=t?`[${t}]`:"";console.error(`Error caught by useErrorHandler${h}:`,y)}i&&i(y)},[t,r,o,i]),u=x.useCallback(()=>{a(null),d(!1)},[]),m=x.useCallback(async y=>{try{return await y()}catch(h){f(h);return}},[f]);return x.useEffect(()=>()=>{a(null),d(!1)},[]),{error:c,hasError:l,handleError:f,resetError:u,tryExecute:m}}function Et(e,t){const r=()=>{if(typeof window>"u")return t;try{const a=window.localStorage.getItem(e);return a?JSON.parse(a):t}catch(a){return console.warn(`Error reading localStorage key "${e}":`,a),t}},[o,i]=x.useState(r),c=a=>{try{const l=a instanceof Function?a(o):a;i(l),typeof window<"u"&&window.localStorage.setItem(e,JSON.stringify(l))}catch(l){console.warn(`Error setting localStorage key "${e}":`,l)}};return x.useEffect(()=>{const a=l=>{l.key===e&&l.newValue&&i(JSON.parse(l.newValue))};return window.addEventListener("storage",a),()=>window.removeEventListener("storage",a)},[e]),[o,c]}function sl(e){const{totalItems:t,itemsPerPage:r=10,initialPage:o=1,persistKey:i}=e,[c,a]=i?Et(`${i}_page`,o):x.useState(o),[l,d]=i?Et(`${i}_itemsPerPage`,r):x.useState(r),f=x.useMemo(()=>Math.max(1,Math.ceil(t/l)),[t,l]),u=x.useMemo(()=>Math.min(Math.max(1,c),f),[c,f]);u!==c&&a(u);const m=(u-1)*l,y=Math.min(m+l-1,t-1),h=u>1,g=u<f,b=x.useMemo(()=>{const j=[];if(f<=5)for(let k=1;k<=f;k++)j.push(k);else{let k=Math.max(1,u-Math.floor(2.5));const M=Math.min(f,k+5-1);M===f&&(k=Math.max(1,M-5+1));for(let R=k;R<=M;R++)j.push(R)}return j},[u,f]),T=x.useCallback(()=>{g&&a(u+1)},[g,u,a]),w=x.useCallback(()=>{h&&a(u-1)},[h,u,a]),C=x.useCallback($=>{const j=Math.min(Math.max(1,$),f);a(j)},[f,a]),L=x.useCallback($=>{d($),a(1)},[d,a]);return{currentPage:u,itemsPerPage:l,totalPages:f,hasPreviousPage:h,hasNextPage:g,startIndex:m,endIndex:y,pageRange:b,nextPage:T,previousPage:w,goToPage:C,setItemsPerPage:L}}const il=(e,t="$",r=!1)=>{const i=Math.abs(e).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2});return e>0?r?`+${t}${i}`:`${t}${i}`:e<0?`-${t}${i}`:`${t}${i}`},al=(e,t={})=>{const{currency:r="$",showPositiveSign:o=!1,customAriaLabel:i}=t;return x.useMemo(()=>{if(e==null)return{formattedAmount:"",isProfit:!1,isLoss:!1,isNeutral:!1,isEmpty:!0,ariaLabel:i||"No profit/loss data available"};const c=e>0,a=e<0,l=e===0,d=il(e,r,o),f=`${c?"Profit":a?"Loss":"Breakeven"} of ${d}`;return{formattedAmount:d,isProfit:c,isLoss:a,isNeutral:l,isEmpty:!1,ariaLabel:i||f}},[e,r,o,i])},cl=e=>e==null?!0:Array.isArray(e)?e.length===0:typeof e=="object"?Object.keys(e).length===0:typeof e=="string"?e.trim().length===0:!1,ll=e=>{const{fetchData:t,initialData:r=null,fetchOnMount:o=!0,refreshInterval:i,isEmpty:c=cl,transformError:a,dependencies:l=[]}=e,[d,f]=x.useState(r),[u,m]=x.useState(null),y=Kr(),h=x.useMemo(()=>d===null||c(d),[d,c]),g=x.useCallback(async()=>{try{const C=await y.withLoading(t);f(C),m(new Date)}catch(C){const L=a&&C instanceof Error?a(C):C instanceof Error?C.message:"Failed to fetch data";y.setError(L),console.error("Data fetch failed:",C)}},[t,y,a]),b=x.useCallback(async()=>{await g()},[g]),T=x.useCallback(()=>{f(r),m(null),y.reset()},[r,y]),w=x.useCallback(C=>{f(C),m(new Date),y.clearError()},[y]);return x.useEffect(()=>{o&&g()},[o,g]),x.useEffect(()=>{l.length>0&&u!==null&&g()},l),x.useEffect(()=>{if(!i||i<=0)return;const C=setInterval(()=>{!y.isLoading&&!y.error&&g()},i);return()=>clearInterval(C)},[i,y.isLoading,y.error,g]),{data:d,isLoading:y.isLoading,error:y.error,isEmpty:h,isSuccess:y.isSuccess,isError:y.isError,lastFetched:u,refresh:b,clearError:y.clearError,reset:T,setData:w}},dl=(e="en-US")=>x.useMemo(()=>({formatCurrency:(d,f={})=>{const{currency:u="USD",locale:m=e,minimumFractionDigits:y=2,maximumFractionDigits:h=2,showPositiveSign:g=!1}=f,T=new Intl.NumberFormat(m,{style:"currency",currency:u,minimumFractionDigits:y,maximumFractionDigits:h}).format(Math.abs(d));return d>0&&g?`+${T}`:d<0?`-${T}`:T},formatPercent:(d,f={})=>{const{locale:u=e,minimumFractionDigits:m=2,maximumFractionDigits:y=2,showPositiveSign:h=!1}=f,g=new Intl.NumberFormat(u,{style:"percent",minimumFractionDigits:m,maximumFractionDigits:y}),b=d>1?d/100:d,T=g.format(Math.abs(b));return b>0&&h?`+${T}`:b<0?`-${T}`:T},formatNumber:(d,f={})=>{const{locale:u=e,minimumFractionDigits:m=0,maximumFractionDigits:y=2,useGrouping:h=!0}=f;return new Intl.NumberFormat(u,{minimumFractionDigits:m,maximumFractionDigits:y,useGrouping:h}).format(d)},formatDate:(d,f="medium")=>{const u=typeof d=="string"?new Date(d):d;return new Intl.DateTimeFormat(e,{dateStyle:f}).format(u)},formatTime:(d,f="short")=>{const u=typeof d=="string"?new Date(d):d;return new Intl.DateTimeFormat(e,{timeStyle:f}).format(u)},formatRelativeTime:d=>{const f=typeof d=="string"?new Date(d):d,m=Math.floor((new Date().getTime()-f.getTime())/1e3);if(typeof Intl.RelativeTimeFormat<"u"){const b=new Intl.RelativeTimeFormat(e,{numeric:"auto"}),T=[{unit:"year",seconds:31536e3},{unit:"month",seconds:2592e3},{unit:"day",seconds:86400},{unit:"hour",seconds:3600},{unit:"minute",seconds:60},{unit:"second",seconds:1}];for(const w of T){const C=Math.floor(Math.abs(m)/w.seconds);if(C>=1)return b.format(m>0?-C:C,w.unit)}return b.format(0,"second")}const y=Math.abs(m),h=m<0;if(y<60)return h?"in a few seconds":"a few seconds ago";if(y<3600){const b=Math.floor(y/60);return h?`in ${b} minute${b>1?"s":""}`:`${b} minute${b>1?"s":""} ago`}if(y<86400){const b=Math.floor(y/3600);return h?`in ${b} hour${b>1?"s":""}`:`${b} hour${b>1?"s":""} ago`}const g=Math.floor(y/86400);return h?`in ${g} day${g>1?"s":""}`:`${g} day${g>1?"s":""} ago`}}),[e]),Qr={small:s.css(["font-size:",";padding:"," ",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"12px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xxs)||"2px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"}),medium:s.css(["font-size:",";padding:"," ",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"14px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"}),large:s.css(["font-size:",";padding:"," ",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.lg)||"18px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"})},Xr={profit:s.css(["color:",";background-color:",";border:1px solid ",";"],({theme:e})=>{var t,r;return((t=e.colors)==null?void 0:t.profit)||((r=e.colors)==null?void 0:r.success)||"#4caf50"},({theme:e})=>{var t;return(t=e.colors)!=null&&t.profit?`${e.colors.profit}15`:"rgba(76, 175, 80, 0.1)"},({theme:e})=>{var t;return(t=e.colors)!=null&&t.profit?`${e.colors.profit}30`:"rgba(76, 175, 80, 0.2)"}),loss:s.css(["color:",";background-color:",";border:1px solid ",";"],({theme:e})=>{var t,r;return((t=e.colors)==null?void 0:t.loss)||((r=e.colors)==null?void 0:r.error)||"#f44336"},({theme:e})=>{var t;return(t=e.colors)!=null&&t.loss?`${e.colors.loss}15`:"rgba(244, 67, 54, 0.1)"},({theme:e})=>{var t;return(t=e.colors)!=null&&t.loss?`${e.colors.loss}30`:"rgba(244, 67, 54, 0.2)"}),neutral:s.css(["color:",";background-color:",";border:1px solid ",";"],({theme:e})=>{var t,r;return((t=e.colors)==null?void 0:t.neutral)||((r=e.colors)==null?void 0:r.textSecondary)||"#757575"},({theme:e})=>{var t;return(t=e.colors)!=null&&t.neutral?`${e.colors.neutral}15`:"rgba(117, 117, 117, 0.1)"},({theme:e})=>{var t;return(t=e.colors)!=null&&t.neutral?`${e.colors.neutral}30`:"rgba(117, 117, 117, 0.2)"}),default:s.css(["color:",";background-color:transparent;border:1px solid transparent;"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"})},ul=s.css(["display:inline-flex;align-items:center;justify-content:flex-end;font-weight:",";font-family:",";transition:",";border-radius:",";&:hover{transform:translateY(-1px);box-shadow:",";}"],({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.semibold)||"600"},({theme:e})=>{var t;return((t=e.fontFamilies)==null?void 0:t.mono)||"monospace"},({theme:e})=>{var t;return((t=e.transitions)==null?void 0:t.fast)||"all 0.2s ease"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.sm)||"4px"},({theme:e})=>{var t;return((t=e.shadows)==null?void 0:t.sm)||"0 2px 4px rgba(0, 0, 0, 0.1)"}),pl=s.css(["opacity:0.6;position:relative;&::after{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);animation:shimmer 1.5s infinite;}@keyframes shimmer{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}"]),fl=e=>Qr[e],ml=(e,t,r)=>e?"profit":t?"loss":r?"neutral":"default",gl=e=>Xr[e],Ye={name:"mercedes-green",colors:{primary:v.f1MercedesGreen,primaryDark:v.f1MercedesGreenDark,primaryLight:v.f1MercedesGreenLight,secondary:v.f1Blue,secondaryDark:v.f1BlueDark,secondaryLight:v.f1BlueLight,accent:v.f1McLarenOrange,accentDark:v.f1McLarenOrangeDark,accentLight:v.f1McLarenOrangeLight,success:v.f1MercedesGreen,warning:v.f1McLarenOrange,error:v.f1Red,danger:v.f1Red,info:v.f1Blue,background:te.background,surface:te.surface,elevated:v.gray700,cardBackground:te.surface,border:te.border,divider:"rgba(255, 255, 255, 0.1)",textPrimary:te.textPrimary,textSecondary:te.textSecondary,textDisabled:te.textDisabled,textInverse:te.textInverse,chartGrid:te.chartGrid,chartLine:te.chartLine,chartAxis:v.gray400,chartTooltip:te.tooltipBackground,profit:v.f1MercedesGreen,loss:v.f1Red,neutral:v.f1Silver,tabActive:v.f1MercedesGreen,tabInactive:v.gray600,tooltipBackground:te.tooltipBackground,modalBackground:te.modalBackground,sidebarBackground:v.gray800,headerBackground:"rgba(0, 0, 0, 0.2)",sessionActive:v.f1MercedesGreen,sessionOptimal:v.f1MercedesGreenLight,sessionCaution:v.f1RacingYellow,sessionTransition:v.f1McLarenOrange,sessionInactive:v.gray600,performanceExcellent:v.f1MercedesGreen,performanceGood:v.f1Blue,performanceAverage:v.f1Silver,performancePoor:v.f1McLarenOrange,performanceAvoid:v.f1Red},spacing:Y,breakpoints:Ge,fontSizes:ie,fontWeights:Ae,lineHeights:ze,fontFamilies:Ve,borderRadius:Be,shadows:He,transitions:qe,zIndex:Ue},hl=Object.freeze(Object.defineProperty({__proto__:null,f1Theme:Ye},Symbol.toStringTag,{value:"Module"})),nt={name:"f1-official",colors:{primary:"#e10600",primaryDark:"#b30500",primaryLight:"#ff1e1e",secondary:"#15151e",secondaryDark:"#0f0f17",secondaryLight:"#1e1e2e",accent:"#ffd700",accentDark:"#e6be1d",accentLight:"#ffdc4a",success:"#00ff41",warning:"#ffd700",error:"#ff1e1e",danger:"#ff1e1e",info:"#00b4d8",background:"#15151e",surface:"#1e1e2e",cardBackground:"#2a2a3a",elevated:"#353545",border:"#3a3a4a",divider:"#4a4a5a",textPrimary:"#ffffff",textSecondary:"#b8b8c8",textDisabled:"#8b8b9b",textInverse:"#15151e",chartGrid:"rgba(255, 255, 255, 0.1)",chartLine:"#e10600",chartAxis:"#b8b8c8",chartTooltip:"rgba(42, 42, 58, 0.9)",profit:"#00ff41",loss:"#ff1e1e",neutral:"#b8b8c8",tabActive:"#e10600",tabInactive:"#8b8b9b",tooltipBackground:"rgba(42, 42, 58, 0.9)",modalBackground:"rgba(21, 21, 30, 0.8)",sidebarBackground:"#1e1e2e",headerBackground:"rgba(21, 21, 30, 0.9)",sessionActive:"#e10600",sessionOptimal:"#ffd700",sessionCaution:"#ff8700",sessionTransition:"#00b4d8",sessionInactive:"#8b8b9b",performanceExcellent:"#00ff41",performanceGood:"#ffd700",performanceAverage:"#ff8700",performancePoor:"#ff1e1e",performanceAvoid:"#8b8b9b"},spacing:Y,breakpoints:Ge,fontSizes:ie,fontWeights:Ae,lineHeights:ze,fontFamilies:Ve,borderRadius:Be,shadows:He,transitions:qe,zIndex:Ue},yl=Object.freeze(Object.defineProperty({__proto__:null,default:nt,f1OfficialTheme:nt},Symbol.toStringTag,{value:"Module"})),bl={name:"f1-official",colors:{primary:v.f1Red,primaryDark:v.f1RedDark,primaryLight:v.f1RedLight,secondary:v.f1Blue,secondaryDark:v.f1BlueDark,secondaryLight:v.f1BlueLight,accent:"#FFD700",accentDark:"#E6C200",accentLight:"#FFF700",success:"#00FF41",warning:"#FFD700",error:"#FF1E1E",danger:"#FF1E1E",info:"#00B4D8",background:"#15151E",surface:"#1E1E2E",elevated:"#2A2A3A",cardBackground:"#2A2A3A",border:"#3A3A4A",divider:"rgba(255, 255, 255, 0.1)",textPrimary:"#FFFFFF",textSecondary:"#B8B8C8",textDisabled:"#8B8B9B",textInverse:"#15151E",chartGrid:"rgba(255, 255, 255, 0.1)",chartLine:v.f1Red,chartAxis:"#B8B8C8",chartTooltip:"rgba(21, 21, 30, 0.95)",profit:"#00FF41",loss:"#FF1E1E",neutral:"#FFD700",tabActive:v.f1Red,tabInactive:"#8B8B9B",tooltipBackground:"rgba(21, 21, 30, 0.95)",modalBackground:"rgba(21, 21, 30, 0.9)",sidebarBackground:"#1A1A24",headerBackground:"rgba(21, 21, 30, 0.95)",sessionActive:"#00FF41",sessionOptimal:"#9D4EDD",sessionCaution:"#FFD700",sessionTransition:"#00B4D8",sessionInactive:"#8B8B9B",performanceExcellent:"#00FF41",performanceGood:"#00B4D8",performanceAverage:"#FFD700",performancePoor:"#FF8700",performanceAvoid:"#FF1E1E"},spacing:Y,breakpoints:Ge,fontSizes:ie,fontWeights:Ae,lineHeights:ze,fontFamilies:Ve,borderRadius:Be,shadows:He,transitions:qe,zIndex:Ue},xl=bl,Lt={name:"mercedes-dark",colors:{primary:v.f1Silver,primaryDark:v.gray500,primaryLight:v.gray300,secondary:v.f1MercedesGreenDark,secondaryDark:"#006B5D",secondaryLight:v.f1MercedesGreen,accent:v.f1Silver,accentDark:v.gray500,accentLight:v.gray300,success:v.f1MercedesGreen,warning:v.f1Silver,error:v.red,danger:v.red,info:v.f1MercedesGreenDark,background:v.gray900,surface:v.gray800,elevated:v.gray700,cardBackground:v.gray800,border:v.gray700,divider:"rgba(255, 255, 255, 0.1)",textPrimary:v.white,textSecondary:v.gray200,textDisabled:v.gray400,textInverse:v.gray900,chartGrid:te.chartGrid,chartLine:v.f1Blue,chartAxis:v.gray400,chartTooltip:te.tooltipBackground,profit:v.f1MercedesGreen,loss:v.red,neutral:v.f1Silver,tabActive:v.f1Silver,tabInactive:v.gray600,tooltipBackground:"rgba(26, 32, 44, 0.9)",modalBackground:"rgba(26, 32, 44, 0.8)",sidebarBackground:v.gray900,headerBackground:"rgba(0, 0, 0, 0.3)",sessionActive:v.f1MercedesGreen,sessionOptimal:v.f1MercedesGreenLight,sessionCaution:v.f1Silver,sessionTransition:v.gray300,sessionInactive:v.gray600,performanceExcellent:v.f1MercedesGreen,performanceGood:v.f1Silver,performanceAverage:v.gray400,performancePoor:v.gray500,performanceAvoid:v.red},spacing:Y,breakpoints:Ge,fontSizes:ie,fontWeights:Ae,lineHeights:ze,fontFamilies:Ve,borderRadius:Be,shadows:He,transitions:qe,zIndex:Ue},vl=Object.freeze(Object.defineProperty({__proto__:null,darkTheme:Lt},Symbol.toStringTag,{value:"Module"})),Sl=s.createGlobalStyle(["*,*::before,*::after{box-sizing:border-box;}html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block;}body{line-height:1.5;font-family:",";background-color:",";color:",";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}ol,ul{list-style:none;}blockquote,q{quotes:none;}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}table{border-collapse:collapse;border-spacing:0;}a{color:",";text-decoration:none;&:hover{text-decoration:underline;}}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit;}::-webkit-scrollbar{width:8px;height:8px;}::-webkit-scrollbar-track{background:",";}::-webkit-scrollbar-thumb{background:",";border-radius:4px;}::-webkit-scrollbar-thumb:hover{background:",";}:focus{outline:2px solid ",";outline-offset:2px;}::selection{background-color:",";color:",";}"],({theme:e})=>e.fontFamilies.body,({theme:e})=>e.colors.background,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.textInverse),wl=Sl,Cl={"mercedes-green":Ye,"f1-official":nt,dark:Lt},Mt=Ye,Tl=e=>e==="f1"||e==="formula1"||e==="formula-1"?"mercedes-green":e==="light"?"f1-official":e,xt=e=>{const t=Tl(e);return Cl[t]||Mt},_t=x.createContext({theme:Mt,setTheme:()=>{}}),El=()=>x.useContext(_t),Il=({initialTheme:e=Mt,persistTheme:t=!0,storageKey:r="adhd-dashboard-theme",children:o})=>{const[i,c]=x.useState(()=>{if(t&&typeof window<"u"){const f=window.localStorage.getItem(r);if(f)try{const u=xt(f);return u||JSON.parse(f)}catch(u){console.error("Failed to parse stored theme:",u)}}return typeof e=="string"?xt(e):e});x.useEffect(()=>{typeof document<"u"&&document.documentElement.setAttribute("data-theme",i.name)},[i.name]);const a=d=>{const f=typeof d=="string"?xt(d):d;c(f),t&&typeof window<"u"&&window.localStorage.setItem(r,f.name||JSON.stringify(f))},l=({children:d})=>n.jsxs(s.ThemeProvider,{theme:i,children:[n.jsx(wl,{}),d]});return n.jsx(_t.Provider,{value:{theme:i,setTheme:a},children:n.jsx(l,{children:o})})};function Jr(e){const t=[];return t.push(`[data-theme="${e.name}"] {`),Object.entries(e.colors).forEach(([r,o])=>{t.push(`  --color-${Rl(r)}: ${o};`)}),Object.entries(e.spacing).forEach(([r,o])=>{t.push(`  --spacing-${r}: ${o};`)}),Object.entries(e.fontSizes).forEach(([r,o])=>{t.push(`  --font-size-${r}: ${o};`)}),Object.entries(e.fontWeights).forEach(([r,o])=>{t.push(`  --font-weight-${r}: ${o};`)}),Object.entries(e.fontFamilies).forEach(([r,o])=>{t.push(`  --font-family-${r}: ${o};`)}),Object.entries(e.borderRadius).forEach(([r,o])=>{t.push(`  --border-radius-${r}: ${o};`)}),Object.entries(e.shadows).forEach(([r,o])=>{t.push(`  --shadow-${r}: ${o};`)}),Object.entries(e.transitions).forEach(([r,o])=>{t.push(`  --transition-${r}: ${o};`)}),Object.entries(e.zIndex).forEach(([r,o])=>{t.push(`  --z-index-${r}: ${o};`)}),t.push("}"),t.join(`
`)}function Zr(e){const t=[];return t.push(`[data-theme="${e.name}"] {`),t.push("  /* Component Semantic Variables */"),t.push("  --primary-color: var(--color-primary);"),t.push("  --secondary-color: var(--color-secondary);"),t.push("  --accent-color: var(--color-accent);"),t.push("  --success-color: var(--color-success);"),t.push("  --warning-color: var(--color-warning);"),t.push("  --error-color: var(--color-error);"),t.push("  --info-color: var(--color-info);"),t.push("  --bg-primary: var(--color-background);"),t.push("  --bg-secondary: var(--color-surface);"),t.push("  --bg-card: var(--color-card-background);"),t.push("  --bg-elevated: var(--color-elevated);"),t.push("  --text-primary: var(--color-text-primary);"),t.push("  --text-secondary: var(--color-text-secondary);"),t.push("  --text-disabled: var(--color-text-disabled);"),t.push("  --text-inverse: var(--color-text-inverse);"),t.push("  --border-primary: var(--color-border);"),t.push("  --border-secondary: var(--color-divider);"),t.push("  --session-card-bg: var(--bg-card);"),t.push("  --session-card-border: var(--border-primary);"),t.push("  --session-card-accent: var(--primary-color);"),t.push("  --session-active: var(--color-session-active);"),t.push("  --session-optimal: var(--color-session-optimal);"),t.push("  --session-caution: var(--color-session-caution);"),t.push("  --session-transition: var(--color-session-transition);"),t.push("  --session-inactive: var(--color-session-inactive);"),t.push("}"),t.join(`
`)}function Rl(e){return e.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g,"$1-$2").toLowerCase()}function jl(e){const t=[];return t.push(`/**
 * Generated Theme CSS Variables
 * 
 * This file is auto-generated from theme definitions.
 * Do not edit manually - changes will be overwritten.
 */`),Object.values(e).forEach(r=>{t.push(""),t.push(`/* ${r.name} Theme */`),t.push(Jr(r)),t.push(""),t.push(Zr(r))}),t.join(`
`)}const kl={"mercedes-green":()=>Promise.resolve().then(()=>hl).then(e=>e.f1Theme),"f1-official":()=>Promise.resolve().then(()=>yl).then(e=>e.f1OfficialTheme),dark:()=>Promise.resolve().then(()=>vl).then(e=>e.darkTheme)};function Nl(e,t,r="StoreContext"){const o=x.createContext(void 0);o.displayName=r;const i=({children:f,initialState:u})=>{const[m,y]=x.useReducer(e,u||t),h=x.useMemo(()=>({state:m,dispatch:y}),[m]);return n.jsx(o.Provider,{value:h,children:f})};function c(){const f=x.useContext(o);if(f===void 0)throw new Error(`use${r} must be used within a ${r}Provider`);return f}function a(f){const{state:u}=c();return f(u)}function l(f){const{dispatch:u}=c();return x.useMemo(()=>(...m)=>{u(f(...m))},[u,f])}function d(f){const{dispatch:u}=c();return x.useMemo(()=>{const m={};for(const y in f)m[y]=(...h)=>{u(f[y](...h))};return m},[u,f])}return{Context:o,Provider:i,useStore:c,useSelector:a,useAction:l,useActions:d}}function Dl(...e){const t=e.pop(),r=e;let o=null,i=null;return c=>{const a=r.map(l=>l(c));return(o===null||a.length!==o.length||a.some((l,d)=>l!==o[d]))&&(i=t(...a),o=a),i}}function Ll(e,t){const{key:r,initialState:o,version:i=1,migrate:c,serialize:a=JSON.stringify,deserialize:l=JSON.parse,filter:d=w=>w,merge:f=(w,C)=>({...C,...w}),debug:u=!1}=t,m=()=>{try{const w=localStorage.getItem(r);if(w===null)return null;const{state:C,version:L}=l(w);return L!==i&&c?(u&&console.log(`Migrating state from version ${L} to ${i}`),c(C,L)):C}catch(w){return u&&console.error("Error loading state from local storage:",w),null}},y=w=>{try{const C=d(w),L=a({state:C,version:i});localStorage.setItem(r,L)}catch(C){u&&console.error("Error saving state to local storage:",C)}},h=()=>{try{localStorage.removeItem(r)}catch(w){u&&console.error("Error clearing state from local storage:",w)}},g=m(),b=g?f(g,o):o;return u&&g&&(console.log("Loaded persisted state:",g),console.log("Merged initial state:",b)),{reducer:(w,C)=>{const L=e(w,C);return y(L),L},initialState:b,clear:h}}function Ml(e,t="$"){return`${t}${e.toFixed(2)}`}function _l(e,t=1){return`${(e*100).toFixed(t)}%`}function Pl(e,t="short"){const r=typeof e=="string"?new Date(e):e;switch(t){case"medium":return r.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});case"long":return r.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});case"short":default:return r.toLocaleDateString("en-US",{year:"numeric",month:"2-digit",day:"2-digit"})}}function Fl(e,t=50){return e.length<=t?e:`${e.substring(0,t-3)}...`}function Ol(){return Math.random().toString(36).substring(2,9)}function $l(e,t){let r=null;return function(...o){const i=()=>{r=null,e(...o)};r&&clearTimeout(r),r=setTimeout(i,t)}}function Al(e,t){let r=!1;return function(...o){r||(e(...o),r=!0,setTimeout(()=>{r=!1},t))}}function zl(e={}){console.log("Monitoring service initialized",e)}function Vl(e,t){console.error("Error captured by monitoring service:",e,t)}function Gl(e){console.log("User set for monitoring service:",e)}function Bl(e,t){const r=performance.now();return{name:e,startTime:r,finish:()=>{const i=performance.now()-r;console.log(`Transaction "${e}" finished in ${i.toFixed(2)}ms`,t)}}}const H={MODEL_TYPE:"model_type",WIN_LOSS:"win_loss",R_MULTIPLE:"r_multiple",DATE:"date",SESSION:"session",DIRECTION:"direction",MARKET:"market",ACHIEVED_PL:"achieved_pl",PATTERN_QUALITY_RATING:"pattern_quality_rating"};class Hl{constructor(){be(this,"dbName","adhd-trading-dashboard");be(this,"version",2);be(this,"db",null);be(this,"stores",{trades:"trades",fvg_details:"trade_fvg_details",setups:"trade_setups",analysis:"trade_analysis",sessions:"trading_sessions"})}async initDB(){return this.db?this.db:new Promise((t,r)=>{const o=indexedDB.open(this.dbName,this.version);o.onupgradeneeded=i=>{var a;const c=i.target.result;if(!c.objectStoreNames.contains(this.stores.trades)){const l=c.createObjectStore(this.stores.trades,{keyPath:"id",autoIncrement:!0});l.createIndex(H.DATE,H.DATE,{unique:!1}),l.createIndex(H.MODEL_TYPE,H.MODEL_TYPE,{unique:!1}),l.createIndex(H.SESSION,H.SESSION,{unique:!1}),l.createIndex(H.WIN_LOSS,H.WIN_LOSS,{unique:!1}),l.createIndex(H.R_MULTIPLE,H.R_MULTIPLE,{unique:!1})}if(c.objectStoreNames.contains(this.stores.fvg_details)||c.createObjectStore(this.stores.fvg_details,{keyPath:"id",autoIncrement:!0}).createIndex("trade_id","trade_id",{unique:!1}),c.objectStoreNames.contains(this.stores.setups)||c.createObjectStore(this.stores.setups,{keyPath:"id",autoIncrement:!0}).createIndex("trade_id","trade_id",{unique:!1}),c.objectStoreNames.contains(this.stores.analysis)||c.createObjectStore(this.stores.analysis,{keyPath:"id",autoIncrement:!0}).createIndex("trade_id","trade_id",{unique:!1}),!c.objectStoreNames.contains(this.stores.sessions)){c.createObjectStore(this.stores.sessions,{keyPath:"id",autoIncrement:!0}).createIndex("name","name",{unique:!0});const d=[{name:"Pre-Market",start_time:"04:00:00",end_time:"09:30:00",description:"Pre-market trading hours"},{name:"NY Open",start_time:"09:30:00",end_time:"10:30:00",description:"New York opening hour"},{name:"10:50-11:10",start_time:"10:50:00",end_time:"11:10:00",description:"Mid-morning macro window"},{name:"11:50-12:10",start_time:"11:50:00",end_time:"12:10:00",description:"Pre-lunch macro window"},{name:"Lunch Macro",start_time:"12:00:00",end_time:"13:30:00",description:"Lunch time trading"},{name:"13:50-14:10",start_time:"13:50:00",end_time:"14:10:00",description:"Post-lunch macro window"},{name:"14:50-15:10",start_time:"14:50:00",end_time:"15:10:00",description:"Pre-close macro window"},{name:"15:15-15:45",start_time:"15:15:00",end_time:"15:45:00",description:"Late afternoon window"},{name:"MOC",start_time:"15:45:00",end_time:"16:00:00",description:"Market on close"},{name:"Post MOC",start_time:"16:00:00",end_time:"20:00:00",description:"After hours trading"}];(a=o.transaction)==null||a.addEventListener("complete",()=>{const u=c.transaction([this.stores.sessions],"readwrite").objectStore(this.stores.sessions);d.forEach(m=>u.add(m))})}},o.onsuccess=i=>{this.db=i.target.result,t(this.db)},o.onerror=i=>{console.error("Error opening IndexedDB:",i),r(new Error("Failed to open IndexedDB"))}})}async saveTradeWithDetails(t){try{const r=await this.initDB();return new Promise((o,i)=>{const c=r.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readwrite");c.onerror=f=>{console.error("Transaction error:",f),i(new Error("Failed to save trade with details"))};const a=c.objectStore(this.stores.trades),l={...t.trade,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},d=a.add(l);d.onsuccess=()=>{const f=d.result,u=[];if(t.fvg_details){const m=c.objectStore(this.stores.fvg_details),y={...t.fvg_details,trade_id:f};u.push(new Promise((h,g)=>{const b=m.add(y);b.onsuccess=()=>h(),b.onerror=()=>g(new Error("Failed to save FVG details"))}))}if(t.setup){const m=c.objectStore(this.stores.setups),y={...t.setup,trade_id:f};u.push(new Promise((h,g)=>{const b=m.add(y);b.onsuccess=()=>h(),b.onerror=()=>g(new Error("Failed to save setup data"))}))}if(t.analysis){const m=c.objectStore(this.stores.analysis),y={...t.analysis,trade_id:f};u.push(new Promise((h,g)=>{const b=m.add(y);b.onsuccess=()=>h(),b.onerror=()=>g(new Error("Failed to save analysis data"))}))}c.oncomplete=()=>{o(f)}},d.onerror=f=>{console.error("Error saving trade:",f),i(new Error("Failed to save trade"))}})}catch(r){throw console.error("Error in saveTradeWithDetails:",r),new Error("Failed to save trade with details")}}async getTradeById(t){try{const r=await this.initDB();return new Promise((o,i)=>{const c=r.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readonly"),l=c.objectStore(this.stores.trades).get(t);l.onsuccess=()=>{const d=l.result;if(!d){o(null);return}const f={trade:d},y=c.objectStore(this.stores.fvg_details).index("trade_id").get(t);y.onsuccess=()=>{y.result&&(f.fvg_details=y.result);const b=c.objectStore(this.stores.setups).index("trade_id").get(t);b.onsuccess=()=>{b.result&&(f.setup=b.result);const C=c.objectStore(this.stores.analysis).index("trade_id").get(t);C.onsuccess=()=>{C.result&&(f.analysis=C.result),o(f)},C.onerror=L=>{console.error("Error getting analysis data:",L),o(f)}},b.onerror=T=>{console.error("Error getting setup data:",T),o(f)}},y.onerror=h=>{console.error("Error getting FVG details:",h),o(f)}},l.onerror=d=>{console.error("Error getting trade:",d),i(new Error("Failed to get trade"))}})}catch(r){return console.error("Error in getTradeById:",r),null}}async getPerformanceMetrics(){try{const t=await this.initDB();return new Promise((r,o)=>{const a=t.transaction([this.stores.trades],"readonly").objectStore(this.stores.trades).getAll();a.onsuccess=()=>{const l=a.result;if(l.length===0){r({totalTrades:0,winningTrades:0,losingTrades:0,winRate:0,profitFactor:0,averageWin:0,averageLoss:0,largestWin:0,largestLoss:0,totalPnl:0,maxDrawdown:0,maxDrawdownPercent:0,sharpeRatio:0,sortinoRatio:0,calmarRatio:0,averageRMultiple:0,expectancy:0,sqn:0,period:"all",startDate:"",endDate:""});return}const d=l.length,f=l.filter(F=>F[H.WIN_LOSS]==="Win").length,u=l.filter(F=>F[H.WIN_LOSS]==="Loss").length,m=d>0?f/d*100:0,y=l.filter(F=>F.achieved_pl!==void 0).map(F=>F.achieved_pl),h=y.reduce((F,K)=>F+K,0),g=y.filter(F=>F>0),b=y.filter(F=>F<0),T=g.length>0?g.reduce((F,K)=>F+K,0)/g.length:0,w=b.length>0?Math.abs(b.reduce((F,K)=>F+K,0)/b.length):0,C=g.length>0?Math.max(...g):0,L=b.length>0?Math.abs(Math.min(...b)):0,$=g.reduce((F,K)=>F+K,0),j=Math.abs(b.reduce((F,K)=>F+K,0)),k=j>0?$/j:0,M=l.filter(F=>F[H.R_MULTIPLE]!==void 0).map(F=>F[H.R_MULTIPLE]),R=M.length>0?M.reduce((F,K)=>F+K,0)/M.length:0,N=R*(m/100);let W=0,q=0,D=0;for(const F of l)if(F.achieved_pl!==void 0){W+=F.achieved_pl,W>q&&(q=W);const K=q-W;K>D&&(D=K)}const G=q>0?D/q*100:0,J=M.length>0?Math.sqrt(M.length)*R/Math.sqrt(M.reduce((F,K)=>F+Math.pow(K-R,2),0)/M.length):0,Z=l.map(F=>F.date).sort(),le=Z.length>0?Z[0]:"",ee=Z.length>0?Z[Z.length-1]:"";r({totalTrades:d,winningTrades:f,losingTrades:u,winRate:m,profitFactor:k,averageWin:T,averageLoss:w,largestWin:C,largestLoss:L,totalPnl:h,maxDrawdown:D,maxDrawdownPercent:G,sharpeRatio:0,sortinoRatio:0,calmarRatio:0,averageRMultiple:R,expectancy:N,sqn:J,period:"all",startDate:le,endDate:ee})},a.onerror=l=>{console.error("Error getting performance metrics:",l),o(new Error("Failed to get performance metrics"))}})}catch(t){throw console.error("Error in getPerformanceMetrics:",t),new Error("Failed to get performance metrics")}}async filterTrades(t){try{const r=await this.initDB();return new Promise((o,i)=>{const c=r.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readonly"),l=c.objectStore(this.stores.trades).getAll();l.onsuccess=async()=>{let d=l.result;t.dateFrom&&(d=d.filter(u=>u.date>=t.dateFrom)),t.dateTo&&(d=d.filter(u=>u.date<=t.dateTo)),t.model_type&&(d=d.filter(u=>u[H.MODEL_TYPE]===t.model_type)),t.session&&(d=d.filter(u=>u[H.SESSION]===t.session)),t.direction&&(d=d.filter(u=>u[H.DIRECTION]===t.direction)),t.win_loss&&(d=d.filter(u=>u[H.WIN_LOSS]===t.win_loss)),t.market&&(d=d.filter(u=>u[H.MARKET]===t.market)),t.min_r_multiple!==void 0&&(d=d.filter(u=>u[H.R_MULTIPLE]!==void 0&&u[H.R_MULTIPLE]>=t.min_r_multiple)),t.max_r_multiple!==void 0&&(d=d.filter(u=>u[H.R_MULTIPLE]!==void 0&&u[H.R_MULTIPLE]<=t.max_r_multiple)),t.min_pattern_quality!==void 0&&(d=d.filter(u=>u[H.PATTERN_QUALITY_RATING]!==void 0&&u[H.PATTERN_QUALITY_RATING]>=t.min_pattern_quality)),t.max_pattern_quality!==void 0&&(d=d.filter(u=>u[H.PATTERN_QUALITY_RATING]!==void 0&&u[H.PATTERN_QUALITY_RATING]<=t.max_pattern_quality));const f=[];for(const u of d){const m={trade:u},g=c.objectStore(this.stores.fvg_details).index("trade_id").get(u.id);await new Promise(j=>{g.onsuccess=()=>{g.result&&(m.fvg_details=g.result),j()},g.onerror=()=>j()});const w=c.objectStore(this.stores.setups).index("trade_id").get(u.id);await new Promise(j=>{w.onsuccess=()=>{w.result&&(m.setup=w.result),j()},w.onerror=()=>j()});const $=c.objectStore(this.stores.analysis).index("trade_id").get(u.id);await new Promise(j=>{$.onsuccess=()=>{$.result&&(m.analysis=$.result),j()},$.onerror=()=>j()}),f.push(m)}o(f)},l.onerror=d=>{console.error("Error filtering trades:",d),i(new Error("Failed to filter trades"))}})}catch(r){throw console.error("Error in filterTrades:",r),new Error("Failed to filter trades")}}async getAllTrades(){try{return await this.filterTrades({})}catch(t){return console.error("Error in getAllTrades:",t),[]}}async deleteTrade(t){try{const r=await this.initDB();return new Promise((o,i)=>{const c=r.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readwrite");c.onerror=w=>{console.error("Transaction error:",w),i(new Error("Failed to delete trade"))};const d=c.objectStore(this.stores.fvg_details).index("trade_id").openCursor(IDBKeyRange.only(t));d.onsuccess=w=>{const C=w.target.result;C&&(C.delete(),C.continue())};const m=c.objectStore(this.stores.setups).index("trade_id").openCursor(IDBKeyRange.only(t));m.onsuccess=w=>{const C=w.target.result;C&&(C.delete(),C.continue())};const g=c.objectStore(this.stores.analysis).index("trade_id").openCursor(IDBKeyRange.only(t));g.onsuccess=w=>{const C=w.target.result;C&&(C.delete(),C.continue())};const T=c.objectStore(this.stores.trades).delete(t);c.oncomplete=()=>{o()},T.onerror=w=>{console.error("Error deleting trade:",w),i(new Error("Failed to delete trade"))}})}catch(r){throw console.error("Error in deleteTrade:",r),new Error("Failed to delete trade")}}async updateTradeWithDetails(t,r){try{const o=await this.initDB();return new Promise((i,c)=>{const a=o.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readwrite");a.onerror=u=>{console.error("Transaction error:",u),c(new Error("Failed to update trade"))};const l=a.objectStore(this.stores.trades),d={...r.trade,id:t,updated_at:new Date().toISOString()},f=l.put(d);f.onsuccess=()=>{if(r.fvg_details){const u=a.objectStore(this.stores.fvg_details),m={...r.fvg_details,trade_id:t};u.put(m)}if(r.setup){const u=a.objectStore(this.stores.setups),m={...r.setup,trade_id:t};u.put(m)}if(r.analysis){const u=a.objectStore(this.stores.analysis),m={...r.analysis,trade_id:t};u.put(m)}},a.oncomplete=()=>{i()},f.onerror=u=>{console.error("Error updating trade:",u),c(new Error("Failed to update trade"))}})}catch(o){throw console.error("Error in updateTradeWithDetails:",o),new Error("Failed to update trade")}}}const eo=new Hl,ql=eo,Ul=eo;exports.AVAILABLE_THEMES=kl;exports.AppErrorBoundary=Qs;exports.AtomicDesignValidationEngine=Oe;exports.Badge=$e;exports.Button=se;exports.Card=Pr;exports.DashboardSection=Qa;exports.DashboardTemplate=tc;exports.DataCard=Ga;exports.DualTimeDisplay=ls;exports.EmptyState=Ct;exports.EnhancedFormField=pi;exports.ErrorBoundary=Or;exports.F1Container=_c;exports.F1Form=Bc;exports.F1FormField=tl;exports.F1Header=Ic;exports.FeatureErrorBoundary=Xs;exports.FormField=Ii;exports.HierarchicalSessionSelector=ha;exports.Input=xe;exports.LoadingCell=bs;exports.LoadingPlaceholder=Dr;exports.LoadingSpinner=Ts;exports.MacroPeriodType=_;exports.Modal=Fi;exports.OrderSide=Ir;exports.OrderStatus=Rr;exports.OrderType=Er;exports.SETUP_ELEMENTS=we;exports.Select=Ce;exports.SelectDropdown=ms;exports.SessionType=U;exports.SessionUtils=re;exports.SetupBuilder=cc;exports.SortableTable=wi;exports.StatusIndicator=Vn;exports.TRADE_COLUMN_IDS=I;exports.TRADING_ATOMS=st;exports.TRADING_MOLECULES=ue;exports.TRADING_ORGANISMS=Re;exports.TabPanel=oi;exports.Table=Zi;exports.Tag=Un;exports.ThemeContext=_t;exports.ThemeProvider=Il;exports.TimeInForce=jr;exports.TimePicker=Xn;exports.TradeAnalysis=gc;exports.TradeConverters=vt;exports.TradeDirection=Cr;exports.TradeMetrics=dc;exports.TradeStatus=Tr;exports.TradeTable=za;exports.TradeTableFilters=Wr;exports.TradeTableRow=Yr;exports.UnifiedErrorBoundary=It;exports.VALID_TRADING_MODELS=rn;exports.baseColors=v;exports.borderRadius=Be;exports.breakpoints=Ge;exports.captureError=Vl;exports.convertLocalToNY=Jn;exports.convertNYToLocal=wt;exports.convertSessionToDualTime=at;exports.createSelector=Dl;exports.createStoreContext=Nl;exports.darkModeColors=te;exports.darkTheme=Lt;exports.debounce=$l;exports.f1OfficialTheme=nt;exports.f1Theme=Ye;exports.fontFamilies=Ve;exports.fontSizes=ie;exports.fontWeights=Ae;exports.formatCurrency=Ml;exports.formatDate=Pl;exports.formatPercentage=_l;exports.formatTime=Tt;exports.formatTimeForDesktop=os;exports.formatTimeForMobile=_r;exports.formatTimeInterval=Lr;exports.generateAllThemeCSS=jl;exports.generateCSSVariables=Jr;exports.generateId=Ol;exports.generateSemanticVariables=Zr;exports.generateSmartSuggestions=tn;exports.getAtomById=Oo;exports.getAtomsByCategory=Fo;exports.getAtomsForModel=$o;exports.getCompactTradeTableColumns=qr;exports.getCurrentDualTime=rt;exports.getCurrentNYMinutes=Zn;exports.getCurrentNYTime=es;exports.getExpressionsForAtomType=Uo;exports.getMoleculeById=Ho;exports.getMoleculesByAtom=Bo;exports.getMoleculesByType=Go;exports.getMoleculesForModel=qo;exports.getOrganismById=Wo;exports.getOrganismMatches=en;exports.getOrganismsByConfidence=Qo;exports.getOrganismsByModel=Ko;exports.getPerformanceTradeTableColumns=Ur;exports.getProfitLossColors=gl;exports.getProfitLossSize=fl;exports.getProfitLossVariant=ml;exports.getSessionStatus=ns;exports.getSuggestedSetupName=Xo;exports.getTimeUntilNYTime=Fe;exports.getTradeTableColumns=Hr;exports.getUserTimezone=it;exports.initMonitoring=zl;exports.isCurrentTimeInNYWindow=Mr;exports.lightModeColors=hc;exports.lightTheme=xl;exports.lineHeights=ze;exports.matchMoleculesToOrganisms=kr;exports.mercedesGreenTheme=Ye;exports.minutesToTime=rs;exports.persistState=Ll;exports.profitLossBaseStyles=ul;exports.profitLossColors=Xr;exports.profitLossLoadingStyles=pl;exports.profitLossSizes=Qr;exports.setUser=Gl;exports.shadows=He;exports.sortFunctions=fi;exports.spacing=Y;exports.startTransaction=Bl;exports.throttle=Al;exports.timeToMinutes=_e;exports.tradeStorage=Ul;exports.tradeStorageService=ql;exports.transitions=qe;exports.truncateText=Fl;exports.useAsyncData=rl;exports.useDataFormatting=dl;exports.useDataSection=ll;exports.useDebounce=ol;exports.useErrorHandler=nl;exports.useFormField=Ar;exports.useLoadingState=Kr;exports.useLocalStorage=Et;exports.usePagination=sl;exports.useProfitLossFormatting=al;exports.useSessionSelection=Vr;exports.useSortableTable=zr;exports.useTheme=El;exports.validateMoleculeForModel=Jo;exports.validateMoleculeForModelBasic=Yo;exports.validateMoleculeSelection=Zo;exports.validationRules=$r;exports.zIndex=Ue;
