var co = Object.defineProperty;
var lo = (e, t, r) => t in e ? co(e, t, { enumerable: !0, configurable: !0, writable: !0, value: r }) : e[t] = r;
var Se = (e, t, r) => (lo(e, typeof t != "symbol" ? t + "" : t, r), r);
import Ce, { useState as H, useRef as Ir, useEffect as ae, Component as uo, useMemo as q, use<PERSON>allback as z, Suspense as po, createContext as Rr, useContext as jr, useReducer as fo } from "react";
import c, { css as g, keyframes as De, createGlobalStyle as go, ThemeProvider as mo } from "styled-components";
import { createPortal as ho } from "react-dom";
var xo = /* @__PURE__ */ ((e) => (e.LONG = "LONG", e.SHORT = "SHORT", e))(xo || {}), yo = /* @__PURE__ */ ((e) => (e.OPEN = "OPEN", e.CLOSED = "CLOSED", e.CANCELED = "CANCELED", e.REJECTED = "REJECTED", e.PENDING = "PENDING", e))(yo || {}), bo = /* @__PURE__ */ ((e) => (e.MARKET = "MARKET", e.LIMIT = "LIMIT", e.STOP = "STOP", e.STOP_LIMIT = "STOP_LIMIT", e))(bo || {}), vo = /* @__PURE__ */ ((e) => (e.BUY = "BUY", e.SELL = "SELL", e))(vo || {}), wo = /* @__PURE__ */ ((e) => (e.PENDING = "PENDING", e.FILLED = "FILLED", e.PARTIALLY_FILLED = "PARTIALLY_FILLED", e.CANCELED = "CANCELED", e.REJECTED = "REJECTED", e))(wo || {}), So = /* @__PURE__ */ ((e) => (e.GTC = "GTC", e.IOC = "IOC", e.FOK = "FOK", e.DAY = "DAY", e))(So || {});
const Wt = {
  /**
   * Convert CompleteTradeData to legacy Trade interface
   */
  completeTradeToLegacy: (e) => {
    var r;
    const t = e.trade;
    return {
      id: ((r = t.id) == null ? void 0 : r.toString()) || "0",
      symbol: t.market || "MNQ",
      date: t.date,
      direction: t.direction,
      size: t.no_of_contracts || 1,
      entry: t.entry_price || 0,
      exit: t.exit_price || 0,
      stopLoss: 0,
      // Not in new schema
      takeProfit: 0,
      // Not in new schema
      profitLoss: t.achieved_pl || 0,
      strategy: t.setup || "",
      notes: t.notes || "",
      tags: [],
      // Not in new schema
      images: []
      // Not in new schema
    };
  },
  /**
   * Convert legacy Trade to CompleteTradeData
   */
  legacyToCompleteTrade: (e) => ({
    trade: {
      id: parseInt(e.id) || void 0,
      date: e.date,
      model_type: "Unknown",
      direction: e.direction,
      market: e.symbol,
      entry_price: e.entry,
      exit_price: e.exit,
      achieved_pl: e.profitLoss,
      no_of_contracts: e.size,
      setup: e.strategy,
      notes: e.notes,
      created_at: (/* @__PURE__ */ new Date()).toISOString(),
      updated_at: (/* @__PURE__ */ new Date()).toISOString()
    }
  }),
  /**
   * Convert array of CompleteTradeData to legacy Trade array
   */
  completeTradeArrayToLegacy: (e) => e.map(Wt.completeTradeToLegacy),
  /**
   * Convert array of legacy Trade to CompleteTradeData array
   */
  legacyArrayToCompleteTrade: (e) => e.map(Wt.legacyToCompleteTrade)
};
var Q = /* @__PURE__ */ ((e) => (e.LONDON = "london", e.NEW_YORK_AM = "new-york-am", e.NEW_YORK_PM = "new-york-pm", e.ASIA = "asia", e.PRE_MARKET = "pre-market", e.AFTER_HOURS = "after-hours", e.OVERNIGHT = "overnight", e))(Q || {}), P = /* @__PURE__ */ ((e) => (e.MORNING_BREAKOUT = "morning-breakout", e.MID_MORNING_REVERSION = "mid-morning-reversion", e.PRE_LUNCH = "pre-lunch", e.LUNCH_MACRO_EXTENDED = "lunch-macro-extended", e.LUNCH_MACRO = "lunch-macro", e.POST_LUNCH = "post-lunch", e.PRE_CLOSE = "pre-close", e.POWER_HOUR = "power-hour", e.MOC = "moc", e.LONDON_OPEN = "london-open", e.LONDON_NY_OVERLAP = "london-ny-overlap", e.CUSTOM = "custom", e))(P || {});
const Co = [{
  id: "mnor-fpfvg",
  name: "MNOR-FPFVG",
  category: "session-fvg",
  description: "Midnight New York Opening Range First Presented Fair Value Gap",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "am-fpfvg",
  name: "AM-FPFVG",
  category: "session-fvg",
  description: "Asian Morning First Presented Fair Value Gap",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "pm-fpfvg",
  name: "PM-FPFVG",
  category: "session-fvg",
  description: "Post Market First Presented Fair Value Gap",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "asia-fpfvg",
  name: "Asia-FPFVG",
  category: "session-fvg",
  description: "Asian Session First Presented Fair Value Gap",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "premarket-fpfvg",
  name: "Premarket-FPFVG",
  category: "session-fvg",
  description: "Premarket Session First Presented Fair Value Gap",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}], Eo = [{
  id: "nwog",
  name: "NWOG",
  category: "htf-pd-array",
  description: "New Week Opening Gap",
  modelEligibility: {
    "FVG-RD": !1,
    // Too large for 1m redelivery mechanics
    "RD-Cont": !0
  }
}, {
  id: "old-nwog",
  name: "Old-NWOG",
  category: "htf-pd-array",
  description: "Previous Week Opening Gap",
  modelEligibility: {
    "FVG-RD": !1,
    "RD-Cont": !0
  }
}, {
  id: "ndog",
  name: "NDOG",
  category: "htf-pd-array",
  description: "New Day Opening Gap",
  modelEligibility: {
    "FVG-RD": !1,
    // Too large for 1m redelivery mechanics
    "RD-Cont": !0
  }
}, {
  id: "old-ndog",
  name: "Old-NDOG",
  category: "htf-pd-array",
  description: "Previous Day Opening Gap",
  modelEligibility: {
    "FVG-RD": !1,
    "RD-Cont": !0
  }
}, {
  id: "daily-top-bottom-fvg",
  name: "Daily-Top/Bottom-FVG",
  category: "htf-pd-array",
  description: "Daily Timeframe Top or Bottom Fair Value Gap",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}], To = [{
  id: "strong-fvg",
  name: "Strong-FVG",
  category: "strength-fvg",
  description: "Strong Fair Value Gap with significant momentum",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "rdrb-fvg",
  name: "RDRB-FVG",
  category: "strength-fvg",
  description: "Redelivery Rejection Block Fair Value Gap",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}], Io = [{
  id: "0930-or-h",
  name: "09:30-OR-H",
  category: "liquidity-pool-time",
  description: "9:30 Opening Range High",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "0930-or-l",
  name: "09:30-OR-L",
  category: "liquidity-pool-time",
  description: "9:30 Opening Range Low",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "london-h",
  name: "London-H",
  category: "liquidity-pool-time",
  description: "London Session High",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "london-l",
  name: "London-L",
  category: "liquidity-pool-time",
  description: "London Session Low",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "premarket-h",
  name: "Premarket-H",
  category: "liquidity-pool-time",
  description: "Premarket Session High",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "premarket-l",
  name: "Premarket-L",
  category: "liquidity-pool-time",
  description: "Premarket Session Low",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "lunch-h",
  name: "Lunch-H",
  category: "liquidity-pool-time",
  description: "Lunch Session High",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "lunch-l",
  name: "Lunch-L",
  category: "liquidity-pool-time",
  description: "Lunch Session Low",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}], Ro = [{
  id: "monthly-h",
  name: "Monthly-H",
  category: "liquidity-pool-htf",
  description: "Monthly High",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "monthly-l",
  name: "Monthly-L",
  category: "liquidity-pool-htf",
  description: "Monthly Low",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "weekly-h",
  name: "Weekly-H",
  category: "liquidity-pool-htf",
  description: "Weekly High",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "weekly-l",
  name: "Weekly-L",
  category: "liquidity-pool-htf",
  description: "Weekly Low",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "daily-h",
  name: "Daily-H",
  category: "liquidity-pool-htf",
  description: "Daily High",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "daily-l",
  name: "Daily-L",
  category: "liquidity-pool-htf",
  description: "Daily Low",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}], jo = [{
  id: "prev-day-h",
  name: "Prev-Day-H",
  category: "historical-pd-array",
  description: "Previous Day High",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "prev-day-l",
  name: "Prev-Day-L",
  category: "historical-pd-array",
  description: "Previous Day Low",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "3day-h",
  name: "3Day-H",
  category: "historical-pd-array",
  description: "3-Day High",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "3day-l",
  name: "3Day-L",
  category: "historical-pd-array",
  description: "3-Day Low",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "prev-week-h",
  name: "Prev-Week-H",
  category: "historical-pd-array",
  description: "Previous Week High",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "prev-week-l",
  name: "Prev-Week-L",
  category: "historical-pd-array",
  description: "Previous Week Low",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}], Ct = [...Co, ...Eo, ...To, ...Io, ...Ro, ...jo], _c = (e) => Ct.filter((t) => t.category === e), Mc = (e) => Ct.find((t) => t.id === e), Pc = (e) => Ct.filter((t) => t.modelEligibility[e]), No = [{
  id: "mol1",
  atom: "NDOG",
  type: "state",
  expression: "balanced_no_wick",
  description: "NDOG balanced without wick revisit",
  modelEligibility: {
    "FVG-RD": !1,
    // NDOG not valid for FVG-RD state/behavior
    "RD-Cont": !0
  }
}, {
  id: "mol2",
  atom: "MNOR-FPFVG",
  type: "state",
  expression: "balanced_no_redelivery",
  description: "MNOR-FPFVG balanced without redelivery",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "mol3",
  atom: "Strong-FVG",
  type: "state",
  expression: "untouched",
  description: "Strong-FVG hasn't been interacted with",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "mol4",
  atom: "NWOG",
  type: "state",
  expression: "unengaged",
  description: "NWOG price hasn't reached the structure",
  modelEligibility: {
    "FVG-RD": !1,
    // NWOG not valid for FVG-RD state/behavior
    "RD-Cont": !0
  }
}, {
  id: "mol5",
  atom: "London-H/L",
  type: "state",
  expression: "untaken",
  description: "London High/Low liquidity level remains intact",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "mol6",
  atom: "Daily-Top/Bottom-FVG",
  type: "state",
  expression: "balanced",
  description: "Daily Top/Bottom FVG has been balanced/filled",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "mol7",
  atom: "RDRB-FVG",
  type: "state",
  expression: "balanced_no_wick",
  description: "RDRB-FVG balanced without wick revisit",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}], Do = [{
  id: "mol8",
  atom: "London-H/L",
  type: "behavior",
  expression: "swept",
  description: "London High/Low liquidity taken or structure cleared",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "mol9",
  atom: "Strong-FVG",
  type: "behavior",
  expression: "formed",
  description: "Strong-FVG new structure created",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "mol10",
  atom: "RDRB-FVG",
  type: "behavior",
  expression: "tapped",
  description: "RDRB-FVG structure touched/tested",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "mol11",
  atom: "Premarket-H/L",
  type: "behavior",
  expression: "swept",
  description: "Premarket High/Low liquidity taken",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "mol12",
  atom: "09:30-OR-H/L",
  type: "behavior",
  expression: "swept",
  description: "9:30 Opening Range High/Low swept",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "mol13",
  atom: "NWOG",
  type: "behavior",
  expression: "reclaimed",
  description: "NWOG structure regained after loss",
  modelEligibility: {
    "FVG-RD": !1,
    // NWOG not valid for FVG-RD state/behavior
    "RD-Cont": !0
  }
}, {
  id: "mol14",
  atom: "Lunch-H/L",
  type: "behavior",
  expression: "swept",
  description: "Lunch High/Low liquidity taken",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}], Lo = [{
  id: "mol15",
  atom: "London-H/L",
  type: "target",
  expression: "draw_on_liquidity",
  description: "London High/Low targeted for liquidity",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "mol16",
  atom: "MNOR-FPFVG",
  type: "target",
  expression: "expected_redelivery",
  description: "MNOR-FPFVG anticipated for redelivery",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "mol17",
  atom: "NDOG",
  type: "target",
  expression: "draw_on_liquidity",
  description: "NDOG targeted for liquidity draw",
  modelEligibility: {
    "FVG-RD": !1,
    // NDOG can be target but not state/behavior in FVG-RD
    "RD-Cont": !0
  }
}, {
  id: "mol18",
  atom: "Premarket-H/L",
  type: "target",
  expression: "return_point",
  description: "Premarket High/Low expected for price return",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "mol19",
  atom: "Daily-H/L",
  type: "target",
  expression: "draw_on_liquidity",
  description: "Daily High/Low targeted for liquidity",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}, {
  id: "mol20",
  atom: "Weekly-H/L",
  type: "target",
  expression: "draw_on_liquidity",
  description: "Weekly High/Low targeted for liquidity",
  modelEligibility: {
    "FVG-RD": !0,
    "RD-Cont": !0
  }
}], be = [...No, ...Do, ...Lo], Oc = (e) => be.filter((t) => t.type === e), Fc = (e) => be.filter((t) => t.atom === e), $c = (e) => be.find((t) => t.id === e), Ac = (e) => be.filter((t) => t.modelEligibility[e]), zc = (e, t) => be.filter((r) => r.atom === e && r.type === t).map((r) => r.expression), Vc = (e, t, r, o) => {
  const i = be.find((a) => a.atom === e && a.type === t && a.expression === r);
  return i ? i.modelEligibility[o] : !1;
}, ze = [{
  id: "org001",
  name: "NDOG Reaction Setup Targeting London Liquidity",
  modelTemplate: "RD-Cont",
  modelAlias: "Redelivery Continuation",
  molecules: [{
    atom: "NDOG",
    type: "state",
    expression: "balanced_no_wick"
  }, {
    atom: "Strong-FVG",
    type: "behavior",
    expression: "formed"
  }, {
    atom: "London-H/L",
    type: "target",
    expression: "draw_on_liquidity"
  }],
  biasAssistance: [{
    atom: "15m Top-FVG",
    timeframe: "15m",
    expression: "balanced_no_redelivery",
    influence: "strengthens setup confidence"
  }],
  confidenceRating: "high",
  validationStatus: "untested",
  triggerCondition: "All molecules must be active within the same session window"
}, {
  id: "org002",
  name: "Premarket Sweep Continuation Setup Targeting Daily High",
  modelTemplate: "RD-Cont",
  modelAlias: "Redelivery Continuation",
  molecules: [{
    atom: "Premarket-H/L",
    type: "behavior",
    expression: "swept"
  }, {
    atom: "Strong-FVG",
    type: "behavior",
    expression: "formed"
  }, {
    atom: "Daily-H/L",
    type: "target",
    expression: "draw_on_liquidity"
  }],
  biasAssistance: [{
    atom: "Daily-Top/Bottom-FVG",
    expression: "balanced",
    influence: "provides directional bias"
  }],
  confidenceRating: "medium",
  validationStatus: "untested",
  triggerCondition: "Strong-FVG must form within 15m of sweep"
}, {
  id: "org003",
  name: "MNOR Redelivery Reversal Setup Targeting Premarket High",
  modelTemplate: "FVG-RD",
  modelAlias: "Fair Value Gap Redelivery",
  molecules: [{
    atom: "MNOR-FPFVG",
    type: "state",
    expression: "balanced_no_redelivery"
  }, {
    atom: "MNOR-FPFVG",
    type: "target",
    expression: "expected_redelivery"
  }, {
    atom: "Premarket-H/L",
    type: "target",
    expression: "return_point"
  }],
  biasAssistance: [{
    atom: "Daily-Top/Bottom-FVG",
    expression: "balanced",
    influence: "confirms directional bias"
  }],
  confidenceRating: "high",
  validationStatus: "untested",
  triggerCondition: "MNOR must balance without wick, followed by a strong FVG"
}, {
  id: "org004",
  name: "Lunch Sweep Reversal Setup Targeting NDOG",
  modelTemplate: "RD-Cont",
  modelAlias: "Redelivery Continuation",
  molecules: [{
    atom: "Lunch-H/L",
    type: "behavior",
    expression: "swept"
  }, {
    atom: "Strong-FVG",
    type: "behavior",
    expression: "formed"
  }, {
    atom: "NDOG",
    type: "target",
    expression: "draw_on_liquidity"
  }],
  biasAssistance: [{
    atom: "Daily-Top/Bottom-FVG",
    expression: "balanced",
    influence: "provides directional context"
  }],
  confidenceRating: "medium",
  validationStatus: "untested",
  triggerCondition: "Reversal after lunch sweep confirmed by FVG"
}, {
  id: "org005",
  name: "Premarket Reclaim Setup Targeting London High",
  modelTemplate: "RD-Cont",
  modelAlias: "Redelivery Continuation",
  molecules: [{
    atom: "NWOG",
    type: "behavior",
    expression: "reclaimed"
  }, {
    atom: "Strong-FVG",
    type: "behavior",
    expression: "formed"
  }, {
    atom: "London-H/L",
    type: "target",
    expression: "draw_on_liquidity"
  }],
  biasAssistance: [{
    atom: "Daily-Top/Bottom-FVG",
    expression: "balanced",
    influence: "supports reclaim direction"
  }],
  confidenceRating: "medium",
  validationStatus: "untested",
  triggerCondition: "Return above NWOG after premarket sweep"
}, {
  id: "org006",
  name: "09:30 Sweep Redelivery Setup Targeting Premarket High",
  modelTemplate: "FVG-RD",
  modelAlias: "Fair Value Gap Redelivery",
  molecules: [{
    atom: "09:30-OR-H/L",
    type: "behavior",
    expression: "swept"
  }, {
    atom: "Strong-FVG",
    type: "behavior",
    expression: "formed"
  }, {
    atom: "Premarket-H/L",
    type: "target",
    expression: "return_point"
  }],
  biasAssistance: [{
    atom: "MNOR-FPFVG",
    expression: "balanced_no_wick",
    influence: "provides context state"
  }],
  confidenceRating: "high",
  validationStatus: "untested",
  triggerCondition: "Strong-FVG forms off OR sweep after MNOR context"
}, {
  id: "org007",
  name: "RDRB-FVG Continuation Setup Targeting Weekly High",
  modelTemplate: "RD-Cont",
  modelAlias: "Redelivery Continuation",
  molecules: [{
    atom: "RDRB-FVG",
    type: "state",
    expression: "balanced_no_wick"
  }, {
    atom: "RDRB-FVG",
    type: "behavior",
    expression: "tapped"
  }, {
    atom: "Weekly-H/L",
    type: "target",
    expression: "draw_on_liquidity"
  }],
  biasAssistance: [{
    atom: "NWOG",
    expression: "unengaged",
    influence: "supports higher timeframe target"
  }],
  confidenceRating: "high",
  validationStatus: "untested",
  triggerCondition: "Entry off RDRB-FVG + tap, aiming for HTF liquidity"
}], Gc = (e) => ze.find((t) => t.id === e), Bc = (e) => ze.filter((t) => t.modelTemplate === e), qc = (e) => ze.filter((t) => t.confidenceRating === e), ko = (e) => {
  const t = [];
  for (const r of ze) {
    const o = r.molecules;
    let i = 0;
    for (const s of o)
      e.some((d) => d.atom === s.atom && d.type === s.type && d.expression === s.expression) && i++;
    let a;
    i === o.length && e.length === o.length ? a = "exact" : i > 0 ? a = "partial" : a = "none", a !== "none" && t.push({
      organism: r,
      confidence: a
    });
  }
  return t.sort((r, o) => r.confidence === "exact" && o.confidence !== "exact" ? -1 : r.confidence !== "exact" && o.confidence === "exact" ? 1 : 0);
}, Hc = (e) => {
  const t = ko(e);
  if (t.length === 0)
    return {
      name: "Custom Setup",
      confidence: "none"
    };
  const r = t[0];
  return {
    name: r.organism.name,
    confidence: r.confidence,
    organism: r.organism
  };
};
class Je {
  /**
   * Validate molecule selection for model eligibility
   */
  static validateMoleculeForModel(t, r, o, i) {
    const a = be.find((s) => s.atom === t && s.type === r && s.expression === o);
    if (!a)
      return {
        isValid: !1,
        level: "error",
        message: `Invalid molecule combination: ${t} → ${r} → ${o}`,
        blockedReasons: ["Molecule combination does not exist in library"]
      };
    if (!a.modelEligibility[i]) {
      const s = this.getModelAlternatives(t, r, o, i);
      return {
        isValid: !1,
        level: "error",
        message: `❌ ${t} ${r} molecules cannot be used in ${i} setups`,
        suggestions: s,
        blockedReasons: [`${t} is not eligible for ${i} model`]
      };
    }
    return {
      isValid: !0,
      level: "success",
      message: `✅ Valid ${i} molecule: ${t} → ${r} → ${o}`
    };
  }
  /**
   * Get alternative molecules when current selection is blocked
   */
  static getModelAlternatives(t, r, o, i) {
    const a = [], s = be.filter((l) => l.type === r && l.expression === o && l.modelEligibility[i] && l.atom !== t).map((l) => l.atom);
    if (s.length > 0 && a.push(`Try these ${r} atoms instead: ${s.join(", ")}`), a.length === 0) {
      const l = i === "FVG-RD" ? "RD-Cont" : "FVG-RD";
      a.push(`Consider switching to ${l} model for this molecule`);
    }
    return a;
  }
  /**
   * Validate complete molecule selection
   */
  static validateMoleculeSelection(t, r) {
    if (t.length === 0)
      return {
        isValid: !0,
        level: "info",
        message: "💡 Start by selecting your first molecule above"
      };
    if (r)
      for (const a of t) {
        const s = this.validateMoleculeForModel(a.atom, a.type, a.expression, r);
        if (!s.isValid)
          return s;
      }
    const o = this.findDuplicateMolecules(t);
    if (o.length > 0)
      return {
        isValid: !1,
        level: "warning",
        message: `⚠️ Duplicate molecules detected: ${o.join(", ")}`,
        suggestions: ["Remove duplicate molecules to continue"]
      };
    const i = this.analyzeMoleculeTypeBalance(t);
    return i.issues.length > 0 ? {
      isValid: !0,
      level: "warning",
      message: `⚠️ Molecule balance: ${i.issues.join(", ")}`,
      suggestions: i.suggestions
    } : {
      isValid: !0,
      level: "success",
      message: `✅ Valid combination - ${t.length} molecules selected`
    };
  }
  /**
   * Find duplicate molecules in selection
   */
  static findDuplicateMolecules(t) {
    const r = /* @__PURE__ */ new Set(), o = [];
    for (const i of t) {
      const a = `${i.atom}-${i.type}-${i.expression}`;
      r.has(a) && o.push(`${i.atom} ${i.type}`), r.add(a);
    }
    return o;
  }
  /**
   * Analyze molecule type balance
   */
  static analyzeMoleculeTypeBalance(t) {
    const r = {
      state: t.filter((a) => a.type === "state").length,
      behavior: t.filter((a) => a.type === "behavior").length,
      target: t.filter((a) => a.type === "target").length
    }, o = [], i = [];
    return r.target === 0 && t.length >= 2 && (o.push("No target molecule"), i.push("Add a target molecule to define your objective")), r.state > 2 && (o.push("Too many state molecules"), i.push("Consider reducing state molecules for cleaner setup")), r.behavior > 2 && (o.push("Too many behavior molecules"), i.push("Focus on 1-2 key behavior molecules")), {
      issues: o,
      suggestions: i
    };
  }
  /**
   * Get real-time organism matches with completeness
   */
  static getOrganismMatches(t) {
    const r = [];
    for (const o of ze) {
      const i = o.molecules;
      let a = 0;
      const s = [];
      for (const f of i)
        t.some((m) => m.atom === f.atom && m.type === f.type && m.expression === f.expression) ? a++ : s.push(f);
      const l = Math.round(a / i.length * 100);
      let d;
      l === 100 && t.length === i.length ? d = "complete" : l > 0 ? d = "partial" : d = "possible", (l > 0 || t.length === 0) && r.push({
        organism: o,
        completeness: l,
        missingMolecules: s,
        status: d
      });
    }
    return r.sort((o, i) => i.completeness - o.completeness);
  }
  /**
   * Generate smart suggestions based on current selection
   */
  static generateSmartSuggestions(t, r) {
    const o = [], i = this.getOrganismMatches(t);
    if (t.length === 0)
      return o.push({
        type: "completion",
        message: '🎯 Start with "Market Structure" state molecule for trend context',
        action: {
          atom: "Market Structure",
          type: "state",
          expression: "Bullish"
        }
      }), o.push({
        type: "completion",
        message: '💡 Or start with "FVG" behavior molecule for gap-based setups',
        action: {
          atom: "FVG",
          type: "behavior",
          expression: "Bullish FVG"
        }
      }), o;
    const a = i.find((s) => s.status === "partial" && s.completeness >= 50);
    if (a && a.missingMolecules.length > 0) {
      const s = a.missingMolecules[0];
      o.push({
        type: "completion",
        message: `🎯 ${a.completeness}% complete: Add ${s.atom} ${s.type} to complete "${a.organism.name}"`,
        action: {
          atom: s.atom,
          type: s.type,
          expression: s.expression
        }
      });
    }
    if (t.length === 1) {
      const s = t[0];
      s.type === "state" && o.push({
        type: "completion",
        message: "🏎️ Add a behavior molecule to define your entry pattern",
        action: {
          atom: "Order Block",
          type: "behavior",
          expression: "Bullish OB"
        }
      }), s.type === "behavior" && o.push({
        type: "completion",
        message: "🎯 Add a target molecule to define your profit objective",
        action: {
          atom: "Liquidity",
          type: "target",
          expression: "BSL"
        }
      });
    }
    if (!r && t.length >= 2) {
      const s = this.suggestModelTemplate(t);
      s && o.push({
        type: "model-change",
        message: `🏎️ Based on your molecules, ${s} model is recommended`,
        action: {
          modelTemplate: s
        }
      });
    }
    return o;
  }
  /**
   * Suggest model template based on molecule selection
   */
  static suggestModelTemplate(t) {
    return t.some((i) => {
      const a = be.find((s) => s.atom === i.atom && s.type === i.type && s.expression === i.expression);
      return a && !a.modelEligibility["FVG-RD"] && a.modelEligibility["RD-Cont"];
    }) ? "RD-Cont" : t.some((i) => i.atom.includes("FVG")) ? "FVG-RD" : null;
  }
}
const Uc = Je.validateMoleculeForModel, Yc = Je.validateMoleculeSelection, Wc = Je.getOrganismMatches, Kc = Je.generateSmartSuggestions, ke = {
  constant: {
    parentArrays: ["NWOG", "Old-NWOG", "NDOG", "Old-NDOG", "Monthly-FVG", "Weekly-FVG", "Daily-FVG", "15min-Top/Bottom-FVG", "1h-Top/Bottom-FVG"],
    fvgTypes: ["Strong-FVG", "AM-FPFVG", "PM-FPFVG", "Asia-FPFVG", "Premarket-FPFVG", "MNOR-FVG", "Macro-FVG", "News-FVG", "Top/Bottom-FVG"]
  },
  action: {
    liquidityEvents: ["None", "London-H/L", "Premarket-H/L", "09:30-Opening-Range-H/L", "Lunch-H/L", "Prev-Day-H/L", "Prev-Week-H/L", "Monthly-H/L", "Macro-H/L"]
  },
  variable: {
    rdTypes: ["None", "True-RD", "IMM-RD", "Dispersed-RD", "Wide-Gap-RD"]
  },
  entry: {
    methods: ["Simple-Entry", "Complex-Entry", "Complex-Entry/Mini"]
  }
}, Qc = ["RD-Cont", "FVG-RD", "Combined"];
var n = {}, _o = {
  get exports() {
    return n;
  },
  set exports(e) {
    n = e;
  }
}, _e = {};
/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Kt;
function Mo() {
  if (Kt)
    return _e;
  Kt = 1;
  var e = Ce, t = Symbol.for("react.element"), r = Symbol.for("react.fragment"), o = Object.prototype.hasOwnProperty, i = e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner, a = { key: !0, ref: !0, __self: !0, __source: !0 };
  function s(l, d, f) {
    var u, m = {}, y = null, x = null;
    f !== void 0 && (y = "" + f), d.key !== void 0 && (y = "" + d.key), d.ref !== void 0 && (x = d.ref);
    for (u in d)
      o.call(d, u) && !a.hasOwnProperty(u) && (m[u] = d[u]);
    if (l && l.defaultProps)
      for (u in d = l.defaultProps, d)
        m[u] === void 0 && (m[u] = d[u]);
    return { $$typeof: t, type: l, key: y, ref: x, props: m, _owner: i.current };
  }
  return _e.Fragment = r, _e.jsx = s, _e.jsxs = s, _e;
}
var Me = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Qt;
function Po() {
  return Qt || (Qt = 1, process.env.NODE_ENV !== "production" && function() {
    var e = Ce, t = Symbol.for("react.element"), r = Symbol.for("react.portal"), o = Symbol.for("react.fragment"), i = Symbol.for("react.strict_mode"), a = Symbol.for("react.profiler"), s = Symbol.for("react.provider"), l = Symbol.for("react.context"), d = Symbol.for("react.forward_ref"), f = Symbol.for("react.suspense"), u = Symbol.for("react.suspense_list"), m = Symbol.for("react.memo"), y = Symbol.for("react.lazy"), x = Symbol.for("react.offscreen"), h = Symbol.iterator, b = "@@iterator";
    function E(p) {
      if (p === null || typeof p != "object")
        return null;
      var v = h && p[h] || p[b];
      return typeof v == "function" ? v : null;
    }
    var S = e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
    function C(p) {
      {
        for (var v = arguments.length, T = new Array(v > 1 ? v - 1 : 0), M = 1; M < v; M++)
          T[M - 1] = arguments[M];
        k("error", p, T);
      }
    }
    function k(p, v, T) {
      {
        var M = S.ReactDebugCurrentFrame, V = M.getStackAddendum();
        V !== "" && (v += "%s", T = T.concat([V]));
        var U = T.map(function(A) {
          return String(A);
        });
        U.unshift("Warning: " + v), Function.prototype.apply.call(console[p], console, U);
      }
    }
    var $ = !1, j = !1, N = !1, _ = !1, R = !1, D;
    D = Symbol.for("react.module.reference");
    function K(p) {
      return !!(typeof p == "string" || typeof p == "function" || p === o || p === a || R || p === i || p === f || p === u || _ || p === x || $ || j || N || typeof p == "object" && p !== null && (p.$$typeof === y || p.$$typeof === m || p.$$typeof === s || p.$$typeof === l || p.$$typeof === d || // This needs to include all possible module reference object
      // types supported by any Flight configuration anywhere since
      // we don't know which Flight build this will end up being used
      // with.
      p.$$typeof === D || p.getModuleId !== void 0));
    }
    function W(p, v, T) {
      var M = p.displayName;
      if (M)
        return M;
      var V = v.displayName || v.name || "";
      return V !== "" ? T + "(" + V + ")" : T;
    }
    function L(p) {
      return p.displayName || "Context";
    }
    function B(p) {
      if (p == null)
        return null;
      if (typeof p.tag == "number" && C("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), typeof p == "function")
        return p.displayName || p.name || null;
      if (typeof p == "string")
        return p;
      switch (p) {
        case o:
          return "Fragment";
        case r:
          return "Portal";
        case a:
          return "Profiler";
        case i:
          return "StrictMode";
        case f:
          return "Suspense";
        case u:
          return "SuspenseList";
      }
      if (typeof p == "object")
        switch (p.$$typeof) {
          case l:
            var v = p;
            return L(v) + ".Consumer";
          case s:
            var T = p;
            return L(T._context) + ".Provider";
          case d:
            return W(p, p.render, "ForwardRef");
          case m:
            var M = p.displayName || null;
            return M !== null ? M : B(p.type) || "Memo";
          case y: {
            var V = p, U = V._payload, A = V._init;
            try {
              return B(A(U));
            } catch {
              return null;
            }
          }
        }
      return null;
    }
    var te = Object.assign, re = 0, fe, oe, O, J, we, G, me;
    function _t() {
    }
    _t.__reactDisabledLog = !0;
    function Fr() {
      {
        if (re === 0) {
          fe = console.log, oe = console.info, O = console.warn, J = console.error, we = console.group, G = console.groupCollapsed, me = console.groupEnd;
          var p = {
            configurable: !0,
            enumerable: !0,
            value: _t,
            writable: !0
          };
          Object.defineProperties(console, {
            info: p,
            log: p,
            warn: p,
            error: p,
            group: p,
            groupCollapsed: p,
            groupEnd: p
          });
        }
        re++;
      }
    }
    function $r() {
      {
        if (re--, re === 0) {
          var p = {
            configurable: !0,
            enumerable: !0,
            writable: !0
          };
          Object.defineProperties(console, {
            log: te({}, p, {
              value: fe
            }),
            info: te({}, p, {
              value: oe
            }),
            warn: te({}, p, {
              value: O
            }),
            error: te({}, p, {
              value: J
            }),
            group: te({}, p, {
              value: we
            }),
            groupCollapsed: te({}, p, {
              value: G
            }),
            groupEnd: te({}, p, {
              value: me
            })
          });
        }
        re < 0 && C("disabledDepth fell below zero. This is a bug in React. Please file an issue.");
      }
    }
    var ct = S.ReactCurrentDispatcher, lt;
    function Ve(p, v, T) {
      {
        if (lt === void 0)
          try {
            throw Error();
          } catch (V) {
            var M = V.stack.trim().match(/\n( *(at )?)/);
            lt = M && M[1] || "";
          }
        return `
` + lt + p;
      }
    }
    var dt = !1, Ge;
    {
      var Ar = typeof WeakMap == "function" ? WeakMap : Map;
      Ge = new Ar();
    }
    function Mt(p, v) {
      if (!p || dt)
        return "";
      {
        var T = Ge.get(p);
        if (T !== void 0)
          return T;
      }
      var M;
      dt = !0;
      var V = Error.prepareStackTrace;
      Error.prepareStackTrace = void 0;
      var U;
      U = ct.current, ct.current = null, Fr();
      try {
        if (v) {
          var A = function() {
            throw Error();
          };
          if (Object.defineProperty(A.prototype, "props", {
            set: function() {
              throw Error();
            }
          }), typeof Reflect == "object" && Reflect.construct) {
            try {
              Reflect.construct(A, []);
            } catch (he) {
              M = he;
            }
            Reflect.construct(p, [], A);
          } else {
            try {
              A.call();
            } catch (he) {
              M = he;
            }
            p.call(A.prototype);
          }
        } else {
          try {
            throw Error();
          } catch (he) {
            M = he;
          }
          p();
        }
      } catch (he) {
        if (he && M && typeof he.stack == "string") {
          for (var F = he.stack.split(`
`), ne = M.stack.split(`
`), Z = F.length - 1, ee = ne.length - 1; Z >= 1 && ee >= 0 && F[Z] !== ne[ee]; )
            ee--;
          for (; Z >= 1 && ee >= 0; Z--, ee--)
            if (F[Z] !== ne[ee]) {
              if (Z !== 1 || ee !== 1)
                do
                  if (Z--, ee--, ee < 0 || F[Z] !== ne[ee]) {
                    var de = `
` + F[Z].replace(" at new ", " at ");
                    return p.displayName && de.includes("<anonymous>") && (de = de.replace("<anonymous>", p.displayName)), typeof p == "function" && Ge.set(p, de), de;
                  }
                while (Z >= 1 && ee >= 0);
              break;
            }
        }
      } finally {
        dt = !1, ct.current = U, $r(), Error.prepareStackTrace = V;
      }
      var Te = p ? p.displayName || p.name : "", Yt = Te ? Ve(Te) : "";
      return typeof p == "function" && Ge.set(p, Yt), Yt;
    }
    function zr(p, v, T) {
      return Mt(p, !1);
    }
    function Vr(p) {
      var v = p.prototype;
      return !!(v && v.isReactComponent);
    }
    function Be(p, v, T) {
      if (p == null)
        return "";
      if (typeof p == "function")
        return Mt(p, Vr(p));
      if (typeof p == "string")
        return Ve(p);
      switch (p) {
        case f:
          return Ve("Suspense");
        case u:
          return Ve("SuspenseList");
      }
      if (typeof p == "object")
        switch (p.$$typeof) {
          case d:
            return zr(p.render);
          case m:
            return Be(p.type, v, T);
          case y: {
            var M = p, V = M._payload, U = M._init;
            try {
              return Be(U(V), v, T);
            } catch {
            }
          }
        }
      return "";
    }
    var qe = Object.prototype.hasOwnProperty, Pt = {}, Ot = S.ReactDebugCurrentFrame;
    function He(p) {
      if (p) {
        var v = p._owner, T = Be(p.type, p._source, v ? v.type : null);
        Ot.setExtraStackFrame(T);
      } else
        Ot.setExtraStackFrame(null);
    }
    function Gr(p, v, T, M, V) {
      {
        var U = Function.call.bind(qe);
        for (var A in p)
          if (U(p, A)) {
            var F = void 0;
            try {
              if (typeof p[A] != "function") {
                var ne = Error((M || "React class") + ": " + T + " type `" + A + "` is invalid; it must be a function, usually from the `prop-types` package, but received `" + typeof p[A] + "`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");
                throw ne.name = "Invariant Violation", ne;
              }
              F = p[A](v, A, M, T, null, "SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");
            } catch (Z) {
              F = Z;
            }
            F && !(F instanceof Error) && (He(V), C("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).", M || "React class", T, A, typeof F), He(null)), F instanceof Error && !(F.message in Pt) && (Pt[F.message] = !0, He(V), C("Failed %s type: %s", T, F.message), He(null));
          }
      }
    }
    var Br = Array.isArray;
    function ut(p) {
      return Br(p);
    }
    function qr(p) {
      {
        var v = typeof Symbol == "function" && Symbol.toStringTag, T = v && p[Symbol.toStringTag] || p.constructor.name || "Object";
        return T;
      }
    }
    function Hr(p) {
      try {
        return Ft(p), !1;
      } catch {
        return !0;
      }
    }
    function Ft(p) {
      return "" + p;
    }
    function $t(p) {
      if (Hr(p))
        return C("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.", qr(p)), Ft(p);
    }
    var Le = S.ReactCurrentOwner, Ur = {
      key: !0,
      ref: !0,
      __self: !0,
      __source: !0
    }, At, zt, pt;
    pt = {};
    function Yr(p) {
      if (qe.call(p, "ref")) {
        var v = Object.getOwnPropertyDescriptor(p, "ref").get;
        if (v && v.isReactWarning)
          return !1;
      }
      return p.ref !== void 0;
    }
    function Wr(p) {
      if (qe.call(p, "key")) {
        var v = Object.getOwnPropertyDescriptor(p, "key").get;
        if (v && v.isReactWarning)
          return !1;
      }
      return p.key !== void 0;
    }
    function Kr(p, v) {
      if (typeof p.ref == "string" && Le.current && v && Le.current.stateNode !== v) {
        var T = B(Le.current.type);
        pt[T] || (C('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref', B(Le.current.type), p.ref), pt[T] = !0);
      }
    }
    function Qr(p, v) {
      {
        var T = function() {
          At || (At = !0, C("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", v));
        };
        T.isReactWarning = !0, Object.defineProperty(p, "key", {
          get: T,
          configurable: !0
        });
      }
    }
    function Xr(p, v) {
      {
        var T = function() {
          zt || (zt = !0, C("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", v));
        };
        T.isReactWarning = !0, Object.defineProperty(p, "ref", {
          get: T,
          configurable: !0
        });
      }
    }
    var Jr = function(p, v, T, M, V, U, A) {
      var F = {
        // This tag allows us to uniquely identify this as a React Element
        $$typeof: t,
        // Built-in properties that belong on the element
        type: p,
        key: v,
        ref: T,
        props: A,
        // Record the component responsible for creating this element.
        _owner: U
      };
      return F._store = {}, Object.defineProperty(F._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: !1
      }), Object.defineProperty(F, "_self", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: M
      }), Object.defineProperty(F, "_source", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: V
      }), Object.freeze && (Object.freeze(F.props), Object.freeze(F)), F;
    };
    function Zr(p, v, T, M, V) {
      {
        var U, A = {}, F = null, ne = null;
        T !== void 0 && ($t(T), F = "" + T), Wr(v) && ($t(v.key), F = "" + v.key), Yr(v) && (ne = v.ref, Kr(v, V));
        for (U in v)
          qe.call(v, U) && !Ur.hasOwnProperty(U) && (A[U] = v[U]);
        if (p && p.defaultProps) {
          var Z = p.defaultProps;
          for (U in Z)
            A[U] === void 0 && (A[U] = Z[U]);
        }
        if (F || ne) {
          var ee = typeof p == "function" ? p.displayName || p.name || "Unknown" : p;
          F && Qr(A, ee), ne && Xr(A, ee);
        }
        return Jr(p, F, ne, V, M, Le.current, A);
      }
    }
    var ft = S.ReactCurrentOwner, Vt = S.ReactDebugCurrentFrame;
    function Ee(p) {
      if (p) {
        var v = p._owner, T = Be(p.type, p._source, v ? v.type : null);
        Vt.setExtraStackFrame(T);
      } else
        Vt.setExtraStackFrame(null);
    }
    var gt;
    gt = !1;
    function mt(p) {
      return typeof p == "object" && p !== null && p.$$typeof === t;
    }
    function Gt() {
      {
        if (ft.current) {
          var p = B(ft.current.type);
          if (p)
            return `

Check the render method of \`` + p + "`.";
        }
        return "";
      }
    }
    function eo(p) {
      {
        if (p !== void 0) {
          var v = p.fileName.replace(/^.*[\\\/]/, ""), T = p.lineNumber;
          return `

Check your code at ` + v + ":" + T + ".";
        }
        return "";
      }
    }
    var Bt = {};
    function to(p) {
      {
        var v = Gt();
        if (!v) {
          var T = typeof p == "string" ? p : p.displayName || p.name;
          T && (v = `

Check the top-level render call using <` + T + ">.");
        }
        return v;
      }
    }
    function qt(p, v) {
      {
        if (!p._store || p._store.validated || p.key != null)
          return;
        p._store.validated = !0;
        var T = to(v);
        if (Bt[T])
          return;
        Bt[T] = !0;
        var M = "";
        p && p._owner && p._owner !== ft.current && (M = " It was passed a child from " + B(p._owner.type) + "."), Ee(p), C('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.', T, M), Ee(null);
      }
    }
    function Ht(p, v) {
      {
        if (typeof p != "object")
          return;
        if (ut(p))
          for (var T = 0; T < p.length; T++) {
            var M = p[T];
            mt(M) && qt(M, v);
          }
        else if (mt(p))
          p._store && (p._store.validated = !0);
        else if (p) {
          var V = E(p);
          if (typeof V == "function" && V !== p.entries)
            for (var U = V.call(p), A; !(A = U.next()).done; )
              mt(A.value) && qt(A.value, v);
        }
      }
    }
    function ro(p) {
      {
        var v = p.type;
        if (v == null || typeof v == "string")
          return;
        var T;
        if (typeof v == "function")
          T = v.propTypes;
        else if (typeof v == "object" && (v.$$typeof === d || // Note: Memo only checks outer props here.
        // Inner props are checked in the reconciler.
        v.$$typeof === m))
          T = v.propTypes;
        else
          return;
        if (T) {
          var M = B(v);
          Gr(T, p.props, "prop", M, p);
        } else if (v.PropTypes !== void 0 && !gt) {
          gt = !0;
          var V = B(v);
          C("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?", V || "Unknown");
        }
        typeof v.getDefaultProps == "function" && !v.getDefaultProps.isReactClassApproved && C("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.");
      }
    }
    function oo(p) {
      {
        for (var v = Object.keys(p.props), T = 0; T < v.length; T++) {
          var M = v[T];
          if (M !== "children" && M !== "key") {
            Ee(p), C("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.", M), Ee(null);
            break;
          }
        }
        p.ref !== null && (Ee(p), C("Invalid attribute `ref` supplied to `React.Fragment`."), Ee(null));
      }
    }
    function Ut(p, v, T, M, V, U) {
      {
        var A = K(p);
        if (!A) {
          var F = "";
          (p === void 0 || typeof p == "object" && p !== null && Object.keys(p).length === 0) && (F += " You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");
          var ne = eo(V);
          ne ? F += ne : F += Gt();
          var Z;
          p === null ? Z = "null" : ut(p) ? Z = "array" : p !== void 0 && p.$$typeof === t ? (Z = "<" + (B(p.type) || "Unknown") + " />", F = " Did you accidentally export a JSX literal instead of a component?") : Z = typeof p, C("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s", Z, F);
        }
        var ee = Zr(p, v, T, V, U);
        if (ee == null)
          return ee;
        if (A) {
          var de = v.children;
          if (de !== void 0)
            if (M)
              if (ut(de)) {
                for (var Te = 0; Te < de.length; Te++)
                  Ht(de[Te], p);
                Object.freeze && Object.freeze(de);
              } else
                C("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
            else
              Ht(de, p);
        }
        return p === o ? oo(ee) : ro(ee), ee;
      }
    }
    function no(p, v, T) {
      return Ut(p, v, T, !0);
    }
    function io(p, v, T) {
      return Ut(p, v, T, !1);
    }
    var so = io, ao = no;
    Me.Fragment = o, Me.jsx = so, Me.jsxs = ao;
  }()), Me;
}
(function(e) {
  process.env.NODE_ENV === "production" ? e.exports = Mo() : e.exports = Po();
})(_o);
const Oo = {
  small: g(["padding:", ";font-size:", ";min-height:20px;min-width:", ";"], ({
    theme: e
  }) => `${e.spacing.xxs} ${e.spacing.xs}`, ({
    theme: e
  }) => e.fontSizes.xs, ({
    dot: e
  }) => e ? "8px" : "20px"),
  medium: g(["padding:", ";font-size:", ";min-height:24px;min-width:", ";"], ({
    theme: e
  }) => `${e.spacing.xs} ${e.spacing.sm}`, ({
    theme: e
  }) => e.fontSizes.sm, ({
    dot: e
  }) => e ? "10px" : "24px"),
  large: g(["padding:", ";font-size:", ";min-height:32px;min-width:", ";"], ({
    theme: e
  }) => `${e.spacing.sm} ${e.spacing.md}`, ({
    theme: e
  }) => e.fontSizes.md, ({
    dot: e
  }) => e ? "12px" : "32px")
}, Fo = (e, t, r = !1) => g(["", ""], ({
  theme: o
}) => {
  let i, a, s;
  switch (e) {
    case "primary":
      i = t ? o.colors.primary : `${o.colors.primary}20`, a = t ? o.colors.textInverse : o.colors.primary, s = o.colors.primary;
      break;
    case "secondary":
      i = t ? o.colors.secondary : `${o.colors.secondary}20`, a = t ? o.colors.textInverse : o.colors.secondary, s = o.colors.secondary;
      break;
    case "success":
      i = t ? o.colors.success : `${o.colors.success}20`, a = t ? o.colors.textInverse : o.colors.success, s = o.colors.success;
      break;
    case "warning":
      i = t ? o.colors.warning : `${o.colors.warning}20`, a = t ? o.colors.textInverse : o.colors.warning, s = o.colors.warning;
      break;
    case "error":
      i = t ? o.colors.error : `${o.colors.error}20`, a = t ? o.colors.textInverse : o.colors.error, s = o.colors.error;
      break;
    case "info":
      i = t ? o.colors.info : `${o.colors.info}20`, a = t ? o.colors.textInverse : o.colors.info, s = o.colors.info;
      break;
    case "neutral":
      i = t ? o.colors.textSecondary : `${o.colors.textSecondary}10`, a = t ? o.colors.textInverse : o.colors.textSecondary, s = o.colors.textSecondary;
      break;
    default:
      i = t ? o.colors.textSecondary : `${o.colors.textSecondary}20`, a = t ? o.colors.textInverse : o.colors.textSecondary, s = o.colors.textSecondary;
  }
  return r ? `
          background-color: transparent;
          color: ${s};
          border: 1px solid ${s};
        ` : `
        background-color: ${i};
        color: ${a};
        border: 1px solid transparent;
      `;
}), Nr = /* @__PURE__ */ c.span.withConfig({
  displayName: "IconContainer",
  componentId: "sc-10uskub-0"
})(["display:flex;align-items:center;justify-content:center;"]), $o = /* @__PURE__ */ c(Nr).withConfig({
  displayName: "StartIcon",
  componentId: "sc-10uskub-1"
})(["margin-right:", ";"], ({
  theme: e
}) => e.spacing.xxs), Ao = /* @__PURE__ */ c(Nr).withConfig({
  displayName: "EndIcon",
  componentId: "sc-10uskub-2"
})(["margin-left:", ";"], ({
  theme: e
}) => e.spacing.xxs), zo = /* @__PURE__ */ c.span.withConfig({
  displayName: "StyledBadge",
  componentId: "sc-10uskub-3"
})(["display:", ";align-items:center;justify-content:center;border-radius:", ";font-weight:", ";white-space:nowrap;", " ", " ", " ", " ", ""], ({
  inline: e
}) => e ? "inline-flex" : "flex", ({
  theme: e,
  rounded: t,
  dot: r
}) => r ? "50%" : t ? "9999px" : e.borderRadius.sm, ({
  theme: e
}) => e.fontWeights.medium, ({
  size: e
}) => Oo[e], ({
  variant: e,
  solid: t,
  outlined: r
}) => Fo(e, t, r || !1), ({
  dot: e
}) => e && g(["padding:0;height:8px;width:8px;"]), ({
  counter: e
}) => e && g(["min-width:1.5em;height:1.5em;padding:0 0.5em;border-radius:1em;"]), ({
  clickable: e
}) => e && g(["cursor:pointer;transition:opacity ", ";&:hover{opacity:0.8;}&:active{opacity:0.6;}"], ({
  theme: t
}) => t.transitions.fast)), Ze = ({
  children: e,
  variant: t = "default",
  size: r = "medium",
  solid: o = !1,
  className: i = "",
  style: a,
  onClick: s,
  rounded: l = !1,
  dot: d = !1,
  counter: f = !1,
  outlined: u = !1,
  startIcon: m,
  endIcon: y,
  max: x,
  inline: h = !0
}) => {
  let b = e;
  return f && typeof e == "number" && x !== void 0 && e > x && (b = `${x}+`), /* @__PURE__ */ n.jsx(zo, { variant: t, size: r, solid: o, clickable: !!s, className: i, style: a, onClick: s, rounded: l, dot: d, counter: f, outlined: u, inline: h, children: !d && /* @__PURE__ */ n.jsxs(n.Fragment, { children: [
    m && /* @__PURE__ */ n.jsx($o, { children: m }),
    b,
    y && /* @__PURE__ */ n.jsx(Ao, { children: y })
  ] }) });
}, Vo = /* @__PURE__ */ De(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]), Go = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-1rze74q-0"
})(["width:16px;height:16px;border:2px solid rgba(255,255,255,0.3);border-radius:50%;border-top-color:#fff;animation:", " 0.8s linear infinite;margin-right:", ";"], Vo, ({
  theme: e
}) => e.spacing.xs), Bo = {
  small: g(["padding:", ";font-size:", ";min-height:32px;"], ({
    theme: e
  }) => `${e.spacing.xxs} ${e.spacing.sm}`, ({
    theme: e
  }) => e.fontSizes.xs),
  medium: g(["padding:", ";font-size:", ";min-height:40px;"], ({
    theme: e
  }) => `${e.spacing.xs} ${e.spacing.md}`, ({
    theme: e
  }) => e.fontSizes.sm),
  large: g(["padding:", ";font-size:", ";min-height:48px;"], ({
    theme: e
  }) => `${e.spacing.sm} ${e.spacing.lg}`, ({
    theme: e
  }) => e.fontSizes.md)
}, qo = {
  primary: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", ";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:", ";transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.textPrimary || e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.primaryDark, ({
    theme: e
  }) => e.colors.primaryDark),
  secondary: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", ";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:", ";transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.secondary, ({
    theme: e
  }) => e.colors.textPrimary || e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.secondaryDark, ({
    theme: e
  }) => e.colors.secondaryDark),
  outline: g(["background-color:transparent;color:", ";border:1px solid ", ";&:hover:not(:disabled){background-color:", "0d;transform:translateY(-1px);}&:active:not(:disabled){background-color:", "1a;transform:translateY(0);}"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.primary),
  text: g(["background-color:transparent;color:", ";border:none;padding-left:", ";padding-right:", ";&:hover:not(:disabled){background-color:", "0d;}&:active:not(:disabled){background-color:", "1a;}"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.spacing.xs, ({
    theme: e
  }) => e.spacing.xs, ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.primary),
  success: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", "dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.success, ({
    theme: e
  }) => e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.success),
  danger: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", "dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.error, ({
    theme: e
  }) => e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.error)
}, Ho = /* @__PURE__ */ c.button.withConfig({
  displayName: "StyledButton",
  componentId: "sc-1rze74q-1"
})(["display:inline-flex;align-items:center;justify-content:center;border-radius:", ";font-weight:", ";cursor:pointer;transition:all ", ";position:relative;overflow:hidden;", " ", " ", " &:disabled{opacity:0.6;cursor:not-allowed;box-shadow:none;transform:translateY(0);}", " ", ""], ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => {
  var t;
  return ((t = e.fontWeights) == null ? void 0 : t.medium) || 500;
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.transitions) == null ? void 0 : t.fast) || "0.2s ease";
}, ({
  size: e = "medium"
}) => Bo[e], ({
  variant: e = "primary"
}) => qo[e], ({
  fullWidth: e
}) => e && g(["width:100%;"]), ({
  $hasStartIcon: e
}) => e && g(["& > *:first-child{margin-right:", ";}"], ({
  theme: t
}) => t.spacing.xs), ({
  $hasEndIcon: e
}) => e && g(["& > *:last-child{margin-left:", ";}"], ({
  theme: t
}) => t.spacing.xs)), Uo = /* @__PURE__ */ c.div.withConfig({
  displayName: "ButtonContent",
  componentId: "sc-1rze74q-2"
})(["display:flex;align-items:center;justify-content:center;"]), le = ({
  children: e,
  variant: t = "primary",
  disabled: r = !1,
  loading: o = !1,
  size: i = "medium",
  fullWidth: a = !1,
  startIcon: s,
  endIcon: l,
  onClick: d,
  className: f = "",
  type: u = "button",
  ...m
}) => /* @__PURE__ */ n.jsx(Ho, { variant: t, disabled: r || o, size: i, fullWidth: a, onClick: d, className: f, type: u, $hasStartIcon: !!s && !o, $hasEndIcon: !!l && !o, ...m, children: /* @__PURE__ */ n.jsxs(Uo, { children: [
  o && /* @__PURE__ */ n.jsx(Go, {}),
  !o && s,
  e,
  !o && l
] }) }), Yo = /* @__PURE__ */ c.div.withConfig({
  displayName: "InputWrapper",
  componentId: "sc-uv3rzi-0"
})(["display:flex;flex-direction:column;width:", ";position:relative;"], ({
  fullWidth: e
}) => e ? "100%" : "auto"), Wo = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-uv3rzi-1"
})(["font-size:", ";color:", ";margin-bottom:", ";font-weight:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => {
  var t;
  return ((t = e.fontWeights) == null ? void 0 : t.medium) || 500;
}), Ko = /* @__PURE__ */ c.div.withConfig({
  displayName: "InputContainer",
  componentId: "sc-uv3rzi-2"
})(["display:flex;align-items:center;position:relative;width:100%;border-radius:", ";border:1px solid ", ";background-color:", ";transition:all ", ";", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e,
  hasError: t,
  hasSuccess: r,
  isFocused: o
}) => t ? e.colors.error : r ? e.colors.success : o ? e.colors.primary : e.colors.border, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => {
  var t;
  return ((t = e.transitions) == null ? void 0 : t.fast) || "0.2s ease";
}, ({
  disabled: e,
  theme: t
}) => e && g(["opacity:0.6;background-color:", ";cursor:not-allowed;"], t.colors.background), ({
  isFocused: e,
  theme: t,
  hasError: r,
  hasSuccess: o
}) => e && g(["box-shadow:0 0 0 2px ", ";"], r ? `${t.colors.error}33` : o ? `${t.colors.success}33` : `${t.colors.primary}33`), ({
  $size: e
}) => {
  switch (e) {
    case "small":
      return g(["height:32px;"]);
    case "large":
      return g(["height:48px;"]);
    default:
      return g(["height:40px;"]);
  }
}), Xt = /* @__PURE__ */ c.div.withConfig({
  displayName: "IconContainer",
  componentId: "sc-uv3rzi-3"
})(["display:flex;align-items:center;justify-content:center;padding:0 ", ";color:", ";"], ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.colors.textSecondary), Qo = /* @__PURE__ */ c.input.withConfig({
  displayName: "StyledInput",
  componentId: "sc-uv3rzi-4"
})(["flex:1;border:none;background:transparent;color:", ";width:100%;outline:none;&:disabled{cursor:not-allowed;}&::placeholder{color:", ";}", " ", " ", ""], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.textDisabled, ({
  hasStartIcon: e
}) => e && g(["padding-left:0;"]), ({
  hasEndIcon: e
}) => e && g(["padding-right:0;"]), ({
  $size: e,
  theme: t
}) => e === "small" ? g(["font-size:", ";padding:", " ", ";"], t.fontSizes.xs, t.spacing.xxs, t.spacing.xs) : e === "large" ? g(["font-size:", ";padding:", " ", ";"], t.fontSizes.md, t.spacing.sm, t.spacing.md) : g(["font-size:", ";padding:", " ", ";"], t.fontSizes.sm, t.spacing.xs, t.spacing.sm)), Xo = /* @__PURE__ */ c.button.withConfig({
  displayName: "ClearButton",
  componentId: "sc-uv3rzi-5"
})(["background:none;border:none;cursor:pointer;color:", ";padding:0 ", ";display:flex;align-items:center;justify-content:center;&:hover{color:", ";}&:focus{outline:none;}"], ({
  theme: e
}) => e.colors.textDisabled, ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.colors.textSecondary), Jo = /* @__PURE__ */ c.div.withConfig({
  displayName: "HelperTextContainer",
  componentId: "sc-uv3rzi-6"
})(["display:flex;justify-content:space-between;margin-top:", ";font-size:", ";color:", ";"], ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => e.fontSizes.xs, ({
  theme: e,
  hasError: t,
  hasSuccess: r
}) => t ? e.colors.error : r ? e.colors.success : e.colors.textSecondary), Ie = ({
  value: e,
  onChange: t,
  placeholder: r = "",
  disabled: o = !1,
  error: i = "",
  type: a = "text",
  name: s = "",
  id: l = "",
  className: d = "",
  required: f = !1,
  autoComplete: u = "",
  label: m = "",
  helperText: y = "",
  startIcon: x,
  endIcon: h,
  loading: b = !1,
  success: E = !1,
  clearable: S = !1,
  onClear: C,
  maxLength: k,
  showCharCount: $ = !1,
  size: j = "medium",
  fullWidth: N = !1,
  ..._
}) => {
  const [R, D] = H(!1), K = Ir(null), W = () => {
    C ? C() : t(""), K.current && K.current.focus();
  }, L = (oe) => {
    D(!0), _.onFocus && _.onFocus(oe);
  }, B = (oe) => {
    D(!1), _.onBlur && _.onBlur(oe);
  }, te = S && e && !o, re = (e == null ? void 0 : e.length) || 0, fe = $ || k !== void 0 && k > 0;
  return /* @__PURE__ */ n.jsxs(Yo, { className: d, fullWidth: N, children: [
    m && /* @__PURE__ */ n.jsxs(Wo, { htmlFor: l, children: [
      m,
      f && " *"
    ] }),
    /* @__PURE__ */ n.jsxs(Ko, { hasError: !!i, hasSuccess: !!E, disabled: !!o, $size: j, hasStartIcon: !!x, hasEndIcon: !!(h || te), isFocused: !!R, children: [
      x && /* @__PURE__ */ n.jsx(Xt, { children: x }),
      /* @__PURE__ */ n.jsx(
        Qo,
        {
          ref: K,
          type: a,
          value: e,
          onChange: (oe) => t(oe.target.value),
          placeholder: r,
          disabled: !!(o || b),
          name: s,
          id: l,
          required: !!f,
          autoComplete: u,
          hasStartIcon: !!x,
          hasEndIcon: !!(h || te),
          $size: j,
          maxLength: k,
          onFocus: L,
          onBlur: B,
          ..._
        }
      ),
      te && /* @__PURE__ */ n.jsx(Xo, { type: "button", onClick: W, tabIndex: -1, children: "✕" }),
      h && /* @__PURE__ */ n.jsx(Xt, { children: h })
    ] }),
    (i || y || fe) && /* @__PURE__ */ n.jsxs(Jo, { hasError: !!i, hasSuccess: !!E, children: [
      /* @__PURE__ */ n.jsx("div", { children: i || y }),
      fe && /* @__PURE__ */ n.jsxs("div", { children: [
        re,
        k !== void 0 && `/${k}`
      ] })
    ] })
  ] });
}, Jt = {
  small: g(["height:100px;"]),
  medium: g(["height:200px;"]),
  large: g(["height:300px;"]),
  custom: (e) => g(["height:", ";width:", ";"], e.customHeight, e.customWidth || "100%")
}, Zo = {
  default: g(["background-color:", ";border-radius:", ";"], ({
    theme: e
  }) => e.colors.background, ({
    theme: e
  }) => e.borderRadius.md),
  card: g(["background-color:", ";border-radius:", ";box-shadow:", ";"], ({
    theme: e
  }) => e.colors.surface, ({
    theme: e
  }) => e.borderRadius.md, ({
    theme: e
  }) => e.shadows.sm),
  text: g(["background-color:transparent;height:auto !important;min-height:1.5em;"]),
  list: g(["background-color:", ";border-radius:", ";margin-bottom:", ";"], ({
    theme: e
  }) => e.colors.background, ({
    theme: e
  }) => e.borderRadius.sm, ({
    theme: e
  }) => e.spacing.sm)
}, en = /* @__PURE__ */ De(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]), tn = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-12vczt5-0"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;", " ", ""], ({
  size: e,
  customHeight: t,
  customWidth: r
}) => e === "custom" ? Jt.custom({
  customHeight: t,
  customWidth: r
}) : Jt[e], ({
  variant: e
}) => Zo[e]), rn = /* @__PURE__ */ c.div.withConfig({
  displayName: "Spinner",
  componentId: "sc-12vczt5-1"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:", " 1s linear infinite;margin-bottom:", ";"], ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary, en, ({
  theme: e
}) => e.spacing.sm), on = /* @__PURE__ */ c.div.withConfig({
  displayName: "Text",
  componentId: "sc-12vczt5-2"
})(["color:", ";font-size:", ";"], ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.fontSizes.sm), nn = ({
  variant: e = "default",
  size: t = "medium",
  height: r = "200px",
  width: o = "",
  text: i = "Loading...",
  showSpinner: a = !0,
  className: s = ""
}) => /* @__PURE__ */ n.jsxs(tn, { variant: e, size: t, customHeight: r, customWidth: o, className: s, children: [
  a && /* @__PURE__ */ n.jsx(rn, {}),
  i && /* @__PURE__ */ n.jsx(on, { children: i })
] }), sn = /* @__PURE__ */ c.div.withConfig({
  displayName: "SelectWrapper",
  componentId: "sc-wvk2um-0"
})(["display:flex;flex-direction:column;width:", ";position:relative;"], ({
  fullWidth: e
}) => e ? "100%" : "auto"), an = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-wvk2um-1"
})(["font-size:", ";color:", ";margin-bottom:", ";font-weight:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => {
  var t;
  return ((t = e.fontWeights) == null ? void 0 : t.medium) || 500;
}), cn = /* @__PURE__ */ c.div.withConfig({
  displayName: "SelectContainer",
  componentId: "sc-wvk2um-2"
})(["display:flex;align-items:center;position:relative;width:100%;border-radius:", ";border:1px solid ", ";background-color:", ";transition:all ", ";", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e,
  hasError: t,
  hasSuccess: r,
  isFocused: o
}) => t ? e.colors.error : r ? e.colors.success : o ? e.colors.primary : e.colors.border, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => {
  var t;
  return ((t = e.transitions) == null ? void 0 : t.fast) || "0.2s ease";
}, ({
  disabled: e,
  theme: t
}) => e && g(["opacity:0.6;background-color:", ";cursor:not-allowed;"], t.colors.background), ({
  isFocused: e,
  theme: t,
  hasError: r,
  hasSuccess: o
}) => e && g(["box-shadow:0 0 0 2px ", ";"], r ? `${t.colors.error}33` : o ? `${t.colors.success}33` : `${t.colors.primary}33`), ({
  $size: e
}) => {
  switch (e) {
    case "small":
      return g(["height:32px;"]);
    case "large":
      return g(["height:48px;"]);
    default:
      return g(["height:40px;"]);
  }
}), ln = /* @__PURE__ */ c.div.withConfig({
  displayName: "IconContainer",
  componentId: "sc-wvk2um-3"
})(["display:flex;align-items:center;justify-content:center;padding:0 ", ";color:", ";"], ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.colors.textSecondary), dn = /* @__PURE__ */ c.select.withConfig({
  displayName: "StyledSelect",
  componentId: "sc-wvk2um-4"
})(["flex:1;border:none;background:transparent;color:", `;width:100%;outline:none;appearance:none;background-image:url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right `, " center;background-size:16px;padding-right:", ";&:disabled{cursor:not-allowed;}", " ", ""], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.spacing.xl, ({
  hasStartIcon: e
}) => e && g(["padding-left:0;"]), ({
  $size: e,
  theme: t
}) => e === "small" ? g(["font-size:", ";padding:", " ", ";"], t.fontSizes.xs, t.spacing.xxs, t.spacing.xs) : e === "large" ? g(["font-size:", ";padding:", " ", ";"], t.fontSizes.md, t.spacing.sm, t.spacing.md) : g(["font-size:", ";padding:", " ", ";"], t.fontSizes.sm, t.spacing.xs, t.spacing.sm)), un = /* @__PURE__ */ c.div.withConfig({
  displayName: "HelperTextContainer",
  componentId: "sc-wvk2um-5"
})(["display:flex;justify-content:space-between;margin-top:", ";font-size:", ";color:", ";"], ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => e.fontSizes.xs, ({
  theme: e,
  hasError: t,
  hasSuccess: r
}) => t ? e.colors.error : r ? e.colors.success : e.colors.textSecondary), pn = /* @__PURE__ */ c.optgroup.withConfig({
  displayName: "OptionGroup",
  componentId: "sc-wvk2um-6"
})(["font-weight:", ";color:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.fontWeights) == null ? void 0 : t.medium) || 500;
}, ({
  theme: e
}) => e.colors.textPrimary), Pe = ({
  options: e,
  value: t,
  onChange: r,
  disabled: o = !1,
  error: i = "",
  name: a = "",
  id: s = "",
  className: l = "",
  required: d = !1,
  placeholder: f = "",
  label: u = "",
  helperText: m = "",
  size: y = "medium",
  fullWidth: x = !0,
  loading: h = !1,
  success: b = !1,
  startIcon: E,
  ...S
}) => {
  const [C, k] = H(!1), $ = (D) => {
    k(!0), S.onFocus && S.onFocus(D);
  }, j = (D) => {
    k(!1), S.onBlur && S.onBlur(D);
  }, N = {}, _ = [];
  e.forEach((D) => {
    D.group ? (N[D.group] || (N[D.group] = []), N[D.group].push(D)) : _.push(D);
  });
  const R = Object.keys(N).length > 0;
  return /* @__PURE__ */ n.jsxs(sn, { className: l, fullWidth: x, children: [
    u && /* @__PURE__ */ n.jsxs(an, { htmlFor: s, children: [
      u,
      d && " *"
    ] }),
    /* @__PURE__ */ n.jsxs(cn, { hasError: !!i, hasSuccess: !!b, disabled: !!(o || h), $size: y, hasStartIcon: !!E, isFocused: !!C, children: [
      E && /* @__PURE__ */ n.jsx(ln, { children: E }),
      /* @__PURE__ */ n.jsxs(
        dn,
        {
          value: t,
          onChange: (D) => r(D.target.value),
          disabled: !!(o || h),
          name: a,
          id: s,
          required: !!d,
          hasStartIcon: !!E,
          $size: y,
          onFocus: $,
          onBlur: j,
          ...S,
          children: [
            f && /* @__PURE__ */ n.jsx("option", { value: "", disabled: !0, children: f }),
            R ? /* @__PURE__ */ n.jsxs(n.Fragment, { children: [
              _.map((D) => /* @__PURE__ */ n.jsx("option", { value: D.value, disabled: D.disabled, children: D.label }, D.value)),
              Object.entries(N).map(([D, K]) => /* @__PURE__ */ n.jsx(pn, { label: D, children: K.map((W) => /* @__PURE__ */ n.jsx("option", { value: W.value, disabled: W.disabled, children: W.label }, W.value)) }, D))
            ] }) : (
              // Render all options without groups
              e.map((D) => /* @__PURE__ */ n.jsx("option", { value: D.value, disabled: D.disabled, children: D.label }, D.value))
            )
          ]
        }
      )
    ] }),
    (i || m) && /* @__PURE__ */ n.jsx(un, { hasError: !!i, hasSuccess: !!b, children: /* @__PURE__ */ n.jsx("div", { children: i || m }) })
  ] });
}, Zt = {
  small: "8px",
  medium: "12px",
  large: "16px"
}, fn = {
  small: g(["font-size:", ";margin-left:", ";"], ({
    theme: e
  }) => e.fontSizes.xs, ({
    theme: e
  }) => e.spacing.xs),
  medium: g(["font-size:", ";margin-left:", ";"], ({
    theme: e
  }) => e.fontSizes.sm, ({
    theme: e
  }) => e.spacing.sm),
  large: g(["font-size:", ";margin-left:", ";"], ({
    theme: e
  }) => e.fontSizes.md, ({
    theme: e
  }) => e.spacing.md)
}, gn = /* @__PURE__ */ g(["@keyframes pulse{0%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0.7);}70%{transform:scale(1);box-shadow:0 0 0 6px rgba(var(--pulse-color),0);}100%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0);}}animation:pulse 2s infinite;"]), mn = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-gwj3m-0"
})(["display:inline-flex;align-items:center;"]), hn = /* @__PURE__ */ c.div.withConfig({
  displayName: "Indicator",
  componentId: "sc-gwj3m-1"
})(["border-radius:50%;width:", ";height:", ";", ""], ({
  size: e
}) => Zt[e], ({
  size: e
}) => Zt[e], ({
  status: e,
  theme: t,
  pulse: r
}) => {
  let o, i;
  switch (e) {
    case "success":
      o = t.colors.success, i = "76, 175, 80";
      break;
    case "error":
      o = t.colors.error, i = "244, 67, 54";
      break;
    case "warning":
      o = t.colors.warning, i = "255, 152, 0";
      break;
    case "info":
      o = t.colors.info, i = "33, 150, 243";
      break;
    default:
      o = t.colors.textSecondary, i = "158, 158, 158";
  }
  return g(["background-color:", ";", ""], o, r && g(["--pulse-color:", ";", ""], i, gn));
}), xn = /* @__PURE__ */ c.span.withConfig({
  displayName: "Label",
  componentId: "sc-gwj3m-2"
})(["", " ", ""], ({
  size: e
}) => fn[e], ({
  status: e,
  theme: t
}) => {
  let r;
  switch (e) {
    case "success":
      r = t.colors.success;
      break;
    case "error":
      r = t.colors.error;
      break;
    case "warning":
      r = t.colors.warning;
      break;
    case "info":
      r = t.colors.info;
      break;
    default:
      r = t.colors.textSecondary;
  }
  return g(["color:", ";font-weight:", ";"], r, t.fontWeights.medium);
}), Xc = ({
  status: e,
  size: t = "medium",
  pulse: r = !1,
  showLabel: o = !1,
  label: i = "",
  className: a = ""
}) => {
  const s = i || e.charAt(0).toUpperCase() + e.slice(1);
  return /* @__PURE__ */ n.jsxs(mn, { className: a, children: [
    /* @__PURE__ */ n.jsx(hn, { status: e, size: t, pulse: r }),
    o && /* @__PURE__ */ n.jsx(xn, { status: e, size: t, children: s })
  ] });
}, yn = {
  small: g(["padding:", ";font-size:", ";"], ({
    theme: e
  }) => `${e.spacing.xxs} ${e.spacing.xs}`, ({
    theme: e
  }) => e.fontSizes.xs),
  medium: g(["padding:", ";font-size:", ";"], ({
    theme: e
  }) => `${e.spacing.xs} ${e.spacing.sm}`, ({
    theme: e
  }) => e.fontSizes.sm),
  large: g(["padding:", ";font-size:", ";"], ({
    theme: e
  }) => `${e.spacing.sm} ${e.spacing.md}`, ({
    theme: e
  }) => e.fontSizes.md)
}, bn = (e) => g(["", ""], ({
  theme: t
}) => {
  let r, o, i;
  switch (e) {
    case "primary":
      r = `${t.colors.primary}10`, o = t.colors.primary, i = `${t.colors.primary}30`;
      break;
    case "secondary":
      r = `${t.colors.secondary}10`, o = t.colors.secondary, i = `${t.colors.secondary}30`;
      break;
    case "success":
      r = `${t.colors.success}10`, o = t.colors.success, i = `${t.colors.success}30`;
      break;
    case "warning":
      r = `${t.colors.warning}10`, o = t.colors.warning, i = `${t.colors.warning}30`;
      break;
    case "error":
      r = `${t.colors.error}10`, o = t.colors.error, i = `${t.colors.error}30`;
      break;
    case "info":
      r = `${t.colors.info}10`, o = t.colors.info, i = `${t.colors.info}30`;
      break;
    default:
      r = `${t.colors.textSecondary}10`, o = t.colors.textSecondary, i = `${t.colors.textSecondary}30`;
  }
  return `
        background-color: ${r};
        color: ${o};
        border: 1px solid ${i};
      `;
}), vn = /* @__PURE__ */ c.span.withConfig({
  displayName: "StyledTag",
  componentId: "sc-11nmnw9-0"
})(["display:inline-flex;align-items:center;border-radius:", ";font-weight:", ";", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.pill, ({
  theme: e
}) => e.fontWeights.medium, ({
  size: e
}) => yn[e], ({
  variant: e
}) => bn(e), ({
  clickable: e
}) => e && g(["cursor:pointer;transition:opacity ", ";&:hover{opacity:0.8;}&:active{opacity:0.6;}"], ({
  theme: t
}) => t.transitions.fast)), wn = /* @__PURE__ */ c.button.withConfig({
  displayName: "RemoveButton",
  componentId: "sc-11nmnw9-1"
})(["display:inline-flex;align-items:center;justify-content:center;background:none;border:none;cursor:pointer;color:inherit;opacity:0.7;margin-left:", ";padding:0;", " &:hover{opacity:1;}"], ({
  theme: e
}) => e.spacing.xs, ({
  size: e,
  theme: t
}) => {
  const r = {
    small: "12px",
    medium: "14px",
    large: "16px"
  };
  return `
      width: ${r[e]};
      height: ${r[e]};
      font-size: ${t.fontSizes.xs};
    `;
}), Jc = ({
  children: e,
  variant: t = "default",
  size: r = "medium",
  removable: o = !1,
  onRemove: i,
  className: a = "",
  onClick: s
}) => {
  const l = (d) => {
    d.stopPropagation(), i == null || i();
  };
  return /* @__PURE__ */ n.jsxs(vn, { variant: t, size: r, clickable: !!s, className: a, onClick: s, children: [
    e,
    o && /* @__PURE__ */ n.jsx(wn, { size: r, onClick: l, children: "×" })
  ] });
}, Sn = /* @__PURE__ */ c.div.withConfig({
  displayName: "TimePickerContainer",
  componentId: "sc-v5w9zw-0"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => e.spacing.xs), Cn = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-v5w9zw-1"
})(["font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.fontWeights.medium, ({
  theme: e
}) => e.colors.textPrimary), En = /* @__PURE__ */ c.input.withConfig({
  displayName: "TimeInput",
  componentId: "sc-v5w9zw-2"
})(["padding:", ";border:1px solid ", ";border-radius:", ";font-size:", ";color:", ";background-color:", ";transition:border-color ", ";&:focus{outline:none;border-color:", ";}&:disabled{background-color:", ";cursor:not-allowed;}"], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.fontSizes.md, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.transitions.fast, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.chartGrid), Tn = ({
  id: e,
  name: t,
  value: r,
  onChange: o,
  label: i,
  required: a = !1,
  disabled: s = !1,
  className: l,
  placeholder: d = "HH:MM",
  min: f,
  max: u
}) => /* @__PURE__ */ n.jsxs(Sn, { className: l, children: [
  i && /* @__PURE__ */ n.jsxs(Cn, { htmlFor: e, children: [
    i,
    a && /* @__PURE__ */ n.jsx("span", { style: {
      color: "red"
    }, children: " *" })
  ] }),
  /* @__PURE__ */ n.jsx(En, { id: e, name: t, type: "time", value: r, onChange: o, required: a, disabled: s, placeholder: d, min: f, max: u })
] }), Zc = Tn, Et = () => Intl.DateTimeFormat().resolvedOptions().timeZone, vt = (e, t = /* @__PURE__ */ new Date()) => {
  const i = new Intl.DateTimeFormat("en", {
    timeZone: e,
    timeZoneName: "short"
  }).formatToParts(t).find((a) => a.type === "timeZoneName");
  return (i == null ? void 0 : i.value) || e;
}, er = (e, t) => {
  const r = t || Et(), [o, i] = e.split(":").map(Number), a = /* @__PURE__ */ new Date(), s = a.getFullYear(), l = String(a.getMonth() + 1).padStart(2, "0"), d = String(a.getDate()).padStart(2, "0"), f = `${String(o).padStart(2, "0")}:${String(i).padStart(2, "0")}:00`, u = `${s}-${l}-${d}T${f}`, m = new Date(u), y = /* @__PURE__ */ new Date(), x = new Date(y.toLocaleString("en-US", {
    timeZone: "America/New_York"
  })), b = new Date(y.toLocaleString("en-US", {
    timeZone: r
  })).getTime() - x.getTime();
  return new Date(m.getTime() + b).toLocaleTimeString("en-GB", {
    timeZone: r,
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  });
}, el = (e, t) => {
  const r = /* @__PURE__ */ new Date();
  return (/* @__PURE__ */ new Date(`${r.toDateString()} ${e}:00`)).toLocaleTimeString("en-US", {
    timeZone: "America/New_York",
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  });
}, wt = (e) => {
  const t = e || Et(), r = /* @__PURE__ */ new Date(), o = r.toLocaleTimeString("en-US", {
    timeZone: "America/New_York",
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  }), i = r.toLocaleTimeString("en-GB", {
    timeZone: t,
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  }), a = vt("America/New_York", r), s = vt(t, r);
  return {
    nyTime: o,
    localTime: i,
    nyTimezone: a,
    localTimezone: s,
    formatted: `${o} ${a} | ${i} ${s}`
  };
}, tl = () => {
  const t = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    timeZone: "America/New_York",
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  });
  return Ke(t);
}, rl = () => {
  const e = /* @__PURE__ */ new Date();
  return new Date(e.toLocaleString("en-US", {
    timeZone: "America/New_York"
  }));
}, In = (e) => {
  const t = Math.floor(e / 60), r = e % 60;
  let o = "";
  return t > 0 ? o = r > 0 ? `${t}h ${r}m` : `${t}h` : o = `${r}m`, {
    totalMinutes: e,
    hours: t,
    minutes: r,
    formatted: o
  };
}, Qe = (e) => {
  const r = (/* @__PURE__ */ new Date()).toLocaleString("en-US", {
    timeZone: "America/New_York"
  }), o = new Date(r), [i, a] = e.split(":").map(Number), s = new Date(o);
  s.setHours(i, a, 0, 0), s <= o && s.setDate(s.getDate() + 1);
  const l = s.getTime() - o.getTime(), d = Math.floor(l / (1e3 * 60));
  return In(d);
}, Rn = (e) => Qe(e), Tt = (e, t, r) => {
  const o = r || Et(), i = er(e, o), a = er(t, o), s = vt(o);
  return {
    nyStart: e,
    nyEnd: t,
    localStart: i,
    localEnd: a,
    formatted: `${e}-${t} NY | ${i}-${a} ${s}`
  };
}, jn = (e, t) => {
  const o = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    timeZone: "America/New_York",
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  }), i = Ke(o), a = Ke(e), s = Ke(t);
  return i >= a && i <= s;
}, Ke = (e) => {
  const [t, r] = e.split(":").map(Number);
  return t * 60 + r;
}, ol = (e) => {
  const t = Math.floor(e / 60), r = e % 60;
  return `${t.toString().padStart(2, "0")}:${r.toString().padStart(2, "0")}`;
}, Nn = (e) => {
  const t = e.localTimezone.includes("GMT") ? "🇮🇪" : "🌍";
  return `${e.localTime} ${t} | ${e.nyTime} 🇺🇸`;
}, nl = (e) => `${e.localTime} Local (${e.localTimezone}) | ${e.nyTime} NY (${e.nyTimezone})`, il = (e, t, r, o) => {
  const i = jn(t, r), a = Tt(t, r, o);
  if (i)
    return {
      isActive: !0,
      timeRemaining: Rn(r),
      sessionTime: a,
      status: "active"
    };
  const s = Qe(t);
  return {
    isActive: !1,
    timeUntilStart: s,
    sessionTime: a,
    status: s.totalMinutes < 24 * 60 ? "upcoming" : "ended"
  };
}, ve = /* @__PURE__ */ c.div.withConfig({
  displayName: "TimeContainer",
  componentId: "sc-10dqpqu-0"
})(["display:flex;align-items:center;gap:", ";font-family:'SF Mono','Monaco','Inconsolata','Roboto Mono',monospace;font-weight:600;"], ({
  format: e
}) => e === "mobile" ? "4px" : "8px"), Re = /* @__PURE__ */ c.span.withConfig({
  displayName: "NYTime",
  componentId: "sc-10dqpqu-1"
})(["color:#3b82f6;font-size:inherit;"]), je = /* @__PURE__ */ c.span.withConfig({
  displayName: "LocalTime",
  componentId: "sc-10dqpqu-2"
})(["color:#10b981;font-size:inherit;"]), Ne = /* @__PURE__ */ c.span.withConfig({
  displayName: "Separator",
  componentId: "sc-10dqpqu-3"
})(["color:#6b7280;font-size:inherit;"]), Xe = /* @__PURE__ */ c.span.withConfig({
  displayName: "Timezone",
  componentId: "sc-10dqpqu-4"
})(["color:#9ca3af;font-size:0.85em;font-weight:500;"]), ht = /* @__PURE__ */ c.span.withConfig({
  displayName: "LiveIndicator",
  componentId: "sc-10dqpqu-5"
})(["color:#ef4444;font-size:0.75em;font-weight:bold;animation:pulse 2s infinite;@keyframes pulse{0%,100%{opacity:1;}50%{opacity:0.5;}}"]), tr = /* @__PURE__ */ c.div.withConfig({
  displayName: "CountdownContainer",
  componentId: "sc-10dqpqu-6"
})(["display:flex;align-items:center;gap:8px;"]), rr = /* @__PURE__ */ c.span.withConfig({
  displayName: "CountdownValue",
  componentId: "sc-10dqpqu-7"
})(["color:#f59e0b;font-weight:bold;"]), xt = /* @__PURE__ */ c.span.withConfig({
  displayName: "CountdownLabel",
  componentId: "sc-10dqpqu-8"
})(["color:#9ca3af;font-size:0.9em;"]), Dn = ({
  format: e,
  showLive: t,
  updateInterval: r
}) => {
  const [o, i] = H(wt());
  return ae(() => {
    const a = setInterval(() => {
      i(wt());
    }, r * 1e3);
    return () => clearInterval(a);
  }, [r]), e === "mobile" ? /* @__PURE__ */ n.jsxs(ve, { format: e, children: [
    /* @__PURE__ */ n.jsx("span", { children: Nn(o) }),
    t && /* @__PURE__ */ n.jsx(ht, { children: "LIVE" })
  ] }) : e === "compact" ? /* @__PURE__ */ n.jsxs(ve, { format: e, children: [
    /* @__PURE__ */ n.jsx(Re, { children: o.nyTime }),
    /* @__PURE__ */ n.jsx(Ne, { children: "|" }),
    /* @__PURE__ */ n.jsx(je, { children: o.localTime }),
    t && /* @__PURE__ */ n.jsx(ht, { children: "LIVE" })
  ] }) : /* @__PURE__ */ n.jsxs(ve, { format: e, children: [
    /* @__PURE__ */ n.jsx(Re, { children: o.nyTime }),
    /* @__PURE__ */ n.jsx(Xe, { children: o.nyTimezone }),
    /* @__PURE__ */ n.jsx(Ne, { children: "|" }),
    /* @__PURE__ */ n.jsx(je, { children: o.localTime }),
    /* @__PURE__ */ n.jsx(Xe, { children: o.localTimezone }),
    t && /* @__PURE__ */ n.jsx(ht, { children: "LIVE" })
  ] });
}, Ln = ({
  nyTime: e,
  format: t
}) => {
  const r = wt(), o = Tt(e, e);
  return t === "mobile" ? /* @__PURE__ */ n.jsx(ve, { format: t, children: /* @__PURE__ */ n.jsxs("span", { children: [
    o.localStart,
    " 🇮🇪 | ",
    e,
    " 🇺🇸"
  ] }) }) : t === "compact" ? /* @__PURE__ */ n.jsxs(ve, { format: t, children: [
    /* @__PURE__ */ n.jsx(Re, { children: e }),
    /* @__PURE__ */ n.jsx(Ne, { children: "|" }),
    /* @__PURE__ */ n.jsx(je, { children: o.localStart })
  ] }) : /* @__PURE__ */ n.jsxs(ve, { format: t, children: [
    /* @__PURE__ */ n.jsx(Re, { children: e }),
    /* @__PURE__ */ n.jsx(Xe, { children: r.nyTimezone }),
    /* @__PURE__ */ n.jsx(Ne, { children: "|" }),
    /* @__PURE__ */ n.jsx(je, { children: o.localStart }),
    /* @__PURE__ */ n.jsx(Xe, { children: r.localTimezone })
  ] });
}, kn = ({
  targetNYTime: e,
  format: t,
  updateInterval: r
}) => {
  const [o, i] = H(Qe(e));
  return ae(() => {
    const a = setInterval(() => {
      i(Qe(e));
    }, r * 1e3);
    return () => clearInterval(a);
  }, [e, r]), t === "mobile" ? /* @__PURE__ */ n.jsxs(tr, { children: [
    /* @__PURE__ */ n.jsx(rr, { children: o.formatted }),
    /* @__PURE__ */ n.jsxs(xt, { children: [
      "until ",
      e
    ] })
  ] }) : /* @__PURE__ */ n.jsxs(tr, { children: [
    /* @__PURE__ */ n.jsx(xt, { children: "Next in:" }),
    /* @__PURE__ */ n.jsx(rr, { children: o.formatted }),
    /* @__PURE__ */ n.jsxs(xt, { children: [
      "(",
      e,
      " NY)"
    ] })
  ] });
}, _n = ({
  sessionStart: e,
  sessionEnd: t,
  format: r
}) => {
  const o = Tt(e, t);
  return r === "mobile" ? /* @__PURE__ */ n.jsx(ve, { format: r, children: /* @__PURE__ */ n.jsx("span", { children: o.formatted }) }) : r === "compact" ? /* @__PURE__ */ n.jsxs(ve, { format: r, children: [
    /* @__PURE__ */ n.jsxs(Re, { children: [
      e,
      "-",
      t
    ] }),
    /* @__PURE__ */ n.jsx(Ne, { children: "|" }),
    /* @__PURE__ */ n.jsxs(je, { children: [
      o.localStart,
      "-",
      o.localEnd
    ] })
  ] }) : /* @__PURE__ */ n.jsxs(ve, { format: r, children: [
    /* @__PURE__ */ n.jsx("div", { children: /* @__PURE__ */ n.jsxs(Re, { children: [
      e,
      "-",
      t,
      " NY"
    ] }) }),
    /* @__PURE__ */ n.jsx(Ne, { children: "|" }),
    /* @__PURE__ */ n.jsx("div", { children: /* @__PURE__ */ n.jsxs(je, { children: [
      o.localStart,
      "-",
      o.localEnd,
      " Local"
    ] }) })
  ] });
}, sl = (e) => {
  const {
    mode: t = "current",
    nyTime: r,
    targetNYTime: o,
    sessionStart: i,
    sessionEnd: a,
    format: s = "desktop",
    showLive: l = !1,
    className: d,
    updateInterval: f = 1
  } = e, u = {
    className: d,
    style: {
      fontSize: s === "mobile" ? "14px" : s === "compact" ? "13px" : "14px"
    }
  };
  switch (t) {
    case "static":
      return r ? /* @__PURE__ */ n.jsx("div", { ...u, children: /* @__PURE__ */ n.jsx(Ln, { nyTime: r, format: s }) }) : (console.warn("DualTimeDisplay: nyTime is required for static mode"), null);
    case "countdown":
      return o ? /* @__PURE__ */ n.jsx("div", { ...u, children: /* @__PURE__ */ n.jsx(kn, { targetNYTime: o, format: s, updateInterval: f }) }) : (console.warn("DualTimeDisplay: targetNYTime is required for countdown mode"), null);
    case "session":
      return !i || !a ? (console.warn("DualTimeDisplay: sessionStart and sessionEnd are required for session mode"), null) : /* @__PURE__ */ n.jsx("div", { ...u, children: /* @__PURE__ */ n.jsx(_n, { sessionStart: i, sessionEnd: a, format: s }) });
    case "current":
    default:
      return /* @__PURE__ */ n.jsx("div", { ...u, children: /* @__PURE__ */ n.jsx(Dn, { format: s, showLive: l, updateInterval: f }) });
  }
}, Mn = /* @__PURE__ */ c.div.withConfig({
  displayName: "SelectContainer",
  componentId: "sc-w0dp8e-0"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => e.spacing.xs), Pn = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-w0dp8e-1"
})(["font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.fontWeights.medium, ({
  theme: e
}) => e.colors.textPrimary), On = /* @__PURE__ */ c.select.withConfig({
  displayName: "Select",
  componentId: "sc-w0dp8e-2"
})(["padding:", ";border:1px solid ", ";border-radius:", ";font-size:", ";color:", ";background-color:", ";transition:border-color ", ";&:focus{outline:none;border-color:", ";}&:disabled{background-color:", ";cursor:not-allowed;}"], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.fontSizes.md, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.transitions.fast, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.chartGrid), Fn = ({
  id: e,
  name: t,
  value: r,
  onChange: o,
  options: i,
  label: a,
  required: s = !1,
  disabled: l = !1,
  className: d,
  placeholder: f
}) => /* @__PURE__ */ n.jsxs(Mn, { className: d, children: [
  a && /* @__PURE__ */ n.jsxs(Pn, { htmlFor: e, children: [
    a,
    s && /* @__PURE__ */ n.jsx("span", { style: {
      color: "red"
    }, children: " *" })
  ] }),
  /* @__PURE__ */ n.jsxs(On, { id: e, name: t, value: r, onChange: o, required: s, disabled: l, children: [
    f && /* @__PURE__ */ n.jsx("option", { value: "", disabled: !0, children: f }),
    i.map((u) => /* @__PURE__ */ n.jsx("option", { value: u.value, children: u.label }, u.value))
  ] })
] }), al = Fn, $n = /* @__PURE__ */ c.span.withConfig({
  displayName: "StyledLoadingCell",
  componentId: "sc-1i0qdjp-0"
})(["display:inline-flex;align-items:center;justify-content:flex-end;opacity:0.6;position:relative;", " border-radius:", ";&::after{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);animation:shimmer 1.5s infinite;}@keyframes shimmer{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}"], ({
  $size: e,
  theme: t
}) => {
  var r, o, i, a, s, l, d, f, u;
  switch (e) {
    case "small":
      return g(["font-size:", ";padding:", " ", ";"], ((r = t.fontSizes) == null ? void 0 : r.xs) || "12px", ((o = t.spacing) == null ? void 0 : o.xxs) || "2px", ((i = t.spacing) == null ? void 0 : i.xs) || "4px");
    case "large":
      return g(["font-size:", ";padding:", " ", ";"], ((a = t.fontSizes) == null ? void 0 : a.lg) || "18px", ((s = t.spacing) == null ? void 0 : s.sm) || "8px", ((l = t.spacing) == null ? void 0 : l.md) || "12px");
    default:
      return g(["font-size:", ";padding:", " ", ";"], ((d = t.fontSizes) == null ? void 0 : d.sm) || "14px", ((f = t.spacing) == null ? void 0 : f.xs) || "4px", ((u = t.spacing) == null ? void 0 : u.sm) || "8px");
  }
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.borderRadius) == null ? void 0 : t.sm) || "4px";
}), An = /* @__PURE__ */ c.span.withConfig({
  displayName: "LoadingPlaceholder",
  componentId: "sc-1i0qdjp-1"
})(["display:inline-block;width:", ";height:1em;background-color:currentColor;opacity:0.3;border-radius:2px;"], ({
  $width: e
}) => e || "60px"), zn = (e) => {
  const {
    size: t = "medium",
    width: r,
    className: o,
    "aria-label": i
  } = e;
  return /* @__PURE__ */ n.jsx($n, { className: o, $size: t, $width: r, "aria-label": i || "Loading data", role: "cell", "aria-busy": "true", children: /* @__PURE__ */ n.jsx(An, { $width: r }) });
}, cl = zn, Vn = /* @__PURE__ */ De(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]), Gn = /* @__PURE__ */ De(["0%{background-position:0% 0%;}100%{background-position:100% 100%;}"]), Bn = /* @__PURE__ */ c.div.withConfig({
  displayName: "StyledSpinner",
  componentId: "sc-1hoaoss-0"
})(["display:inline-block;position:relative;", " &::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;border-radius:50%;border:2px solid transparent;", " animation:", " ", "s linear infinite;}", ""], ({
  $size: e
}) => {
  switch (e) {
    case "xs":
      return g(["width:16px;height:16px;"]);
    case "sm":
      return g(["width:20px;height:20px;"]);
    case "md":
      return g(["width:32px;height:32px;"]);
    case "lg":
      return g(["width:48px;height:48px;"]);
    case "xl":
      return g(["width:64px;height:64px;"]);
    default:
      return g(["width:32px;height:32px;"]);
  }
}, ({
  $variant: e,
  theme: t
}) => {
  var r, o, i, a, s, l;
  switch (e) {
    case "primary":
      return g(["border-top-color:", ";border-right-color:", ";"], ((r = t.colors) == null ? void 0 : r.primary) || "#dc2626", ((o = t.colors) == null ? void 0 : o.primary) || "#dc2626");
    case "secondary":
      return g(["border-top-color:", ";border-right-color:", ";"], ((i = t.colors) == null ? void 0 : i.textSecondary) || "#9ca3af", ((a = t.colors) == null ? void 0 : a.textSecondary) || "#9ca3af");
    case "white":
      return g(["border-top-color:#ffffff;border-right-color:#ffffff;"]);
    case "red":
      return g(["border-top-color:#dc2626;border-right-color:#dc2626;"]);
    default:
      return g(["border-top-color:", ";border-right-color:", ";"], ((s = t.colors) == null ? void 0 : s.primary) || "#dc2626", ((l = t.colors) == null ? void 0 : l.primary) || "#dc2626");
  }
}, Vn, ({
  $speed: e
}) => 1 / e, ({
  $showStripes: e,
  $variant: t
}) => e && g(["&::after{content:'';position:absolute;top:2px;left:2px;right:2px;bottom:2px;border-radius:50%;background:", ";background-size:8px 8px;animation:", " ", "s linear infinite;}"], t === "red" || t === "primary" ? "linear-gradient(45deg, transparent 25%, rgba(220, 38, 38, 0.3) 25%, rgba(220, 38, 38, 0.3) 50%, transparent 50%, transparent 75%, rgba(220, 38, 38, 0.3) 75%)" : "linear-gradient(45deg, transparent 25%, rgba(156, 163, 175, 0.3) 25%, rgba(156, 163, 175, 0.3) 50%, transparent 50%, transparent 75%, rgba(156, 163, 175, 0.3) 75%)", Gn, (r) => 2 / r.$speed)), qn = /* @__PURE__ */ c.div.withConfig({
  displayName: "SpinnerContainer",
  componentId: "sc-1hoaoss-1"
})(["display:inline-flex;align-items:center;justify-content:center;"]), Hn = (e) => {
  const {
    size: t = "md",
    variant: r = "primary",
    className: o,
    "aria-label": i,
    speed: a = 1,
    showStripes: s = !1
  } = e;
  return /* @__PURE__ */ n.jsx(qn, { className: o, children: /* @__PURE__ */ n.jsx(Bn, { $size: t, $variant: r, $speed: a, $showStripes: s, role: "status", "aria-label": i || "Loading", "aria-live": "polite" }) });
}, ll = Hn, Un = {
  none: g(["padding:0;"]),
  small: g(["padding:", ";"], ({
    theme: e
  }) => e.spacing.sm),
  medium: g(["padding:", ";"], ({
    theme: e
  }) => e.spacing.md),
  large: g(["padding:", ";"], ({
    theme: e
  }) => e.spacing.lg)
}, Yn = {
  default: g(["background-color:", ";"], ({
    theme: e
  }) => e.colors.surface),
  primary: g(["background-color:", "10;border-color:", "30;"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.primary),
  secondary: g(["background-color:", "10;border-color:", "30;"], ({
    theme: e
  }) => e.colors.secondary, ({
    theme: e
  }) => e.colors.secondary),
  outlined: g(["background-color:transparent;border:1px solid ", ";"], ({
    theme: e
  }) => e.colors.border),
  elevated: g(["background-color:", ";box-shadow:", ";border:none;"], ({
    theme: e
  }) => e.colors.surface, ({
    theme: e
  }) => e.shadows.md)
}, Wn = /* @__PURE__ */ c.div.withConfig({
  displayName: "CardContainer",
  componentId: "sc-mv9m67-0"
})(["border-radius:", ";overflow:hidden;transition:all ", ";position:relative;", " ", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.transitions.fast, ({
  bordered: e,
  theme: t
}) => e && g(["border:1px solid ", ";"], t.colors.border), ({
  padding: e
}) => Un[e], ({
  variant: e
}) => Yn[e], ({
  clickable: e
}) => e && g(["cursor:pointer;&:hover{transform:translateY(-2px);box-shadow:", ";}&:active{transform:translateY(0);}"], ({
  theme: t
}) => t.shadows.md)), Kn = /* @__PURE__ */ c.div.withConfig({
  displayName: "CardHeader",
  componentId: "sc-mv9m67-1"
})(["display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:", ";"], ({
  theme: e
}) => e.spacing.md), Qn = /* @__PURE__ */ c.div.withConfig({
  displayName: "HeaderContent",
  componentId: "sc-mv9m67-2"
})(["flex:1;"]), Xn = /* @__PURE__ */ c.h3.withConfig({
  displayName: "CardTitle",
  componentId: "sc-mv9m67-3"
})(["margin:0;font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.lg, ({
  theme: e
}) => e.fontWeights.semibold, ({
  theme: e
}) => e.colors.textPrimary), Jn = /* @__PURE__ */ c.div.withConfig({
  displayName: "CardSubtitle",
  componentId: "sc-mv9m67-4"
})(["margin-top:", ";font-size:", ";color:", ";"], ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.colors.textSecondary), Zn = /* @__PURE__ */ c.div.withConfig({
  displayName: "ActionsContainer",
  componentId: "sc-mv9m67-5"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.sm), ei = /* @__PURE__ */ c.div.withConfig({
  displayName: "CardContent",
  componentId: "sc-mv9m67-6"
})([""]), ti = /* @__PURE__ */ c.div.withConfig({
  displayName: "CardFooter",
  componentId: "sc-mv9m67-7"
})(["margin-top:", ";padding-top:", ";border-top:1px solid ", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.colors.border), ri = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-mv9m67-8"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:", ";display:flex;align-items:center;justify-content:center;z-index:1;"], ({
  theme: e
}) => `${e.colors.background}80`), oi = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorContainer",
  componentId: "sc-mv9m67-9"
})(["padding:", ";background-color:", "10;border-radius:", ";color:", ";margin-bottom:", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.colors.error, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.colors.error, ({
  theme: e
}) => e.spacing.md), ni = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-mv9m67-10"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"], ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary), ii = ({
  children: e,
  title: t = "",
  subtitle: r = "",
  bordered: o = !0,
  variant: i = "default",
  padding: a = "medium",
  className: s = "",
  footer: l,
  actions: d,
  isLoading: f = !1,
  hasError: u = !1,
  errorMessage: m = "An error occurred",
  clickable: y = !1,
  onClick: x,
  ...h
}) => {
  const b = t || r || d;
  return /* @__PURE__ */ n.jsxs(Wn, { bordered: o, variant: i, padding: a, clickable: y, className: s, onClick: y ? x : void 0, ...h, children: [
    f && /* @__PURE__ */ n.jsx(ri, { children: /* @__PURE__ */ n.jsx(ni, {}) }),
    b && /* @__PURE__ */ n.jsxs(Kn, { children: [
      /* @__PURE__ */ n.jsxs(Qn, { children: [
        t && /* @__PURE__ */ n.jsx(Xn, { children: t }),
        r && /* @__PURE__ */ n.jsx(Jn, { children: r })
      ] }),
      d && /* @__PURE__ */ n.jsx(Zn, { children: d })
    ] }),
    u && /* @__PURE__ */ n.jsx(oi, { children: /* @__PURE__ */ n.jsx("p", { children: m }) }),
    /* @__PURE__ */ n.jsx(ei, { children: e }),
    l && /* @__PURE__ */ n.jsx(ti, { children: l })
  ] });
}, si = /* @__PURE__ */ c.h3.withConfig({
  displayName: "Title",
  componentId: "sc-1jsjvya-0"
})(["margin:0 0 ", " 0;color:", ";font-weight:", ";", ""], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.fontWeights.semibold, ({
  size: e,
  theme: t
}) => {
  const r = {
    small: t.fontSizes.md,
    medium: t.fontSizes.lg,
    large: t.fontSizes.xl
  };
  return g(["font-size:", ";"], r[e]);
}), ai = /* @__PURE__ */ c.p.withConfig({
  displayName: "Description",
  componentId: "sc-1jsjvya-1"
})(["margin:0 0 ", " 0;color:", ";", ""], ({
  theme: e
}) => e.spacing.lg, ({
  theme: e
}) => e.colors.textSecondary, ({
  size: e,
  theme: t
}) => {
  const r = {
    small: t.fontSizes.sm,
    medium: t.fontSizes.md,
    large: t.fontSizes.lg
  };
  return g(["font-size:", ";"], r[e]);
}), ci = {
  default: g(["background-color:transparent;"]),
  compact: g(["background-color:transparent;text-align:left;align-items:flex-start;"]),
  card: g(["background-color:", ";border-radius:", ";box-shadow:", ";"], ({
    theme: e
  }) => e.colors.surface, ({
    theme: e
  }) => e.borderRadius.md, ({
    theme: e
  }) => e.shadows.sm)
}, li = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-1jsjvya-2"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;width:100%;", " ", ""], ({
  variant: e
}) => ci[e], ({
  size: e,
  theme: t
}) => {
  switch (e) {
    case "small":
      return g(["padding:", ";min-height:120px;"], t.spacing.md);
    case "large":
      return g(["padding:", ";min-height:300px;"], t.spacing.xl);
    default:
      return g(["padding:", ";min-height:200px;"], t.spacing.lg);
  }
}), di = /* @__PURE__ */ c.div.withConfig({
  displayName: "IconContainer",
  componentId: "sc-1jsjvya-3"
})(["margin-bottom:", ";", ""], ({
  theme: e
}) => e.spacing.md, ({
  size: e,
  theme: t
}) => {
  const r = {
    small: "32px",
    medium: "48px",
    large: "64px"
  };
  return g(["font-size:", ";svg{width:", ";height:", ";color:", ";}"], r[e], r[e], r[e], t.colors.textSecondary);
}), ui = /* @__PURE__ */ c.div.withConfig({
  displayName: "ActionContainer",
  componentId: "sc-1jsjvya-4"
})(["margin-top:", ";"], ({
  theme: e
}) => e.spacing.md), pi = /* @__PURE__ */ c.div.withConfig({
  displayName: "ChildrenContainer",
  componentId: "sc-1jsjvya-5"
})(["margin-top:", ";width:100%;"], ({
  theme: e
}) => e.spacing.lg), or = ({
  title: e = "",
  description: t = "",
  icon: r,
  actionText: o = "",
  onAction: i,
  variant: a = "default",
  size: s = "medium",
  className: l = "",
  children: d
}) => /* @__PURE__ */ n.jsxs(li, { variant: a, size: s, className: l, children: [
  r && /* @__PURE__ */ n.jsx(di, { size: s, children: r }),
  e && /* @__PURE__ */ n.jsx(si, { size: s, children: e }),
  t && /* @__PURE__ */ n.jsx(ai, { size: s, children: t }),
  o && i && /* @__PURE__ */ n.jsx(ui, { children: /* @__PURE__ */ n.jsx(le, { variant: "primary", size: s === "small" ? "small" : "medium", onClick: i, children: o }) }),
  d && /* @__PURE__ */ n.jsx(pi, { children: d })
] }), nr = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorContainer",
  componentId: "sc-jxqb9h-0"
})(["padding:1.5rem;margin:", ";border-radius:0.5rem;background-color:", ";color:#ffffff;", ""], (e) => e.isAppLevel ? "0" : "1rem 0", (e) => e.isAppLevel ? "#1a1f2c" : "#f44336", (e) => e.isAppLevel && `
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  `), fi = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorCard",
  componentId: "sc-jxqb9h-1"
})(["background-color:#252a37;border-radius:0.5rem;padding:2rem;width:100%;box-shadow:0 4px 6px rgba(0,0,0,0.1);"]), ir = /* @__PURE__ */ c.h3.withConfig({
  displayName: "ErrorTitle",
  componentId: "sc-jxqb9h-2"
})(["margin-top:0;font-size:", ";font-weight:700;text-align:", ";"], (e) => e.isAppLevel ? "1.5rem" : "1.25rem", (e) => e.isAppLevel ? "center" : "left"), Ue = /* @__PURE__ */ c.p.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-jxqb9h-3"
})(["margin-bottom:1rem;text-align:", ";"], (e) => e.isAppLevel ? "center" : "left"), sr = /* @__PURE__ */ c.details.withConfig({
  displayName: "ErrorDetails",
  componentId: "sc-jxqb9h-4"
})(["margin-bottom:1rem;summary{cursor:pointer;color:#2196f3;font-weight:500;margin-bottom:0.5rem;}"]), ar = /* @__PURE__ */ c.pre.withConfig({
  displayName: "ErrorStack",
  componentId: "sc-jxqb9h-5"
})(["font-size:0.875rem;background-color:rgba(0,0,0,0.1);padding:0.5rem;border-radius:0.25rem;overflow:auto;max-height:200px;"]), gi = /* @__PURE__ */ c.div.withConfig({
  displayName: "ButtonContainer",
  componentId: "sc-jxqb9h-6"
})(["display:flex;gap:0.5rem;justify-content:flex-start;"]), Dr = /* @__PURE__ */ c.button.withConfig({
  displayName: "RetryButton",
  componentId: "sc-jxqb9h-7"
})(["background-color:#ffffff;color:#f44336;border:none;border-radius:0.25rem;padding:0.5rem 1rem;font-weight:700;cursor:pointer;transition:background-color 0.2s;&:hover{background-color:#f5f5f5;}"]), mi = /* @__PURE__ */ c.button.withConfig({
  displayName: "SkipButton",
  componentId: "sc-jxqb9h-8"
})(["padding:0.5rem 1rem;background-color:transparent;color:#ffffff;border:1px solid #ffffff;border-radius:0.25rem;font-size:0.875rem;font-weight:500;cursor:pointer;transition:all 0.2s;&:hover{background-color:rgba(255,255,255,0.1);}"]), hi = /* @__PURE__ */ c(Dr).withConfig({
  displayName: "ReloadButton",
  componentId: "sc-jxqb9h-9"
})(["margin-top:1rem;width:100%;"]), xi = ({
  error: e,
  resetError: t,
  isAppLevel: r,
  name: o,
  onSkip: i
}) => {
  const a = () => {
    window.location.reload();
  };
  return r ? /* @__PURE__ */ n.jsx(nr, { isAppLevel: !0, children: /* @__PURE__ */ n.jsxs(fi, { children: [
    /* @__PURE__ */ n.jsx(ir, { isAppLevel: !0, children: "Something went wrong" }),
    /* @__PURE__ */ n.jsx(Ue, { isAppLevel: !0, children: "We're sorry, but an unexpected error has occurred. Please try reloading the application." }),
    /* @__PURE__ */ n.jsxs(sr, { children: [
      /* @__PURE__ */ n.jsx("summary", { children: "Technical Details" }),
      /* @__PURE__ */ n.jsx(Ue, { children: e.message }),
      e.stack && /* @__PURE__ */ n.jsx(ar, { children: e.stack })
    ] }),
    /* @__PURE__ */ n.jsx(hi, { onClick: a, children: "Reload Application" })
  ] }) }) : /* @__PURE__ */ n.jsxs(nr, { children: [
    /* @__PURE__ */ n.jsx(ir, { children: o ? `Error in ${o}` : "Something went wrong" }),
    /* @__PURE__ */ n.jsx(Ue, { children: o ? `We encountered a problem while loading ${o}. You can try again${i ? " or skip this feature" : ""}.` : "An unexpected error occurred. Please try again." }),
    /* @__PURE__ */ n.jsxs(sr, { children: [
      /* @__PURE__ */ n.jsx("summary", { children: "Technical Details" }),
      /* @__PURE__ */ n.jsx(Ue, { children: e.message }),
      e.stack && /* @__PURE__ */ n.jsx(ar, { children: e.stack })
    ] }),
    /* @__PURE__ */ n.jsxs(gi, { children: [
      /* @__PURE__ */ n.jsx(Dr, { onClick: t, children: "Try Again" }),
      i && /* @__PURE__ */ n.jsx(mi, { onClick: i, children: "Skip This Feature" })
    ] })
  ] });
};
class yi extends uo {
  constructor(r) {
    super(r);
    Se(this, "resetError", () => {
      this.setState({
        hasError: !1,
        error: null
      });
    });
    this.state = {
      hasError: !1,
      error: null
    };
  }
  static getDerivedStateFromError(r) {
    return {
      hasError: !0,
      error: r
    };
  }
  componentDidCatch(r, o) {
    const {
      name: i
    } = this.props, a = i ? `ErrorBoundary(${i})` : "ErrorBoundary";
    console.error(`Error caught by ${a}:`, r, o), this.props.onError && this.props.onError(r, o);
  }
  componentDidUpdate(r) {
    this.state.hasError && this.props.resetOnPropsChange && r.children !== this.props.children && this.resetError();
  }
  componentWillUnmount() {
    this.state.hasError && this.props.resetOnUnmount && this.resetError();
  }
  render() {
    const {
      hasError: r,
      error: o
    } = this.state, {
      children: i,
      fallback: a,
      name: s,
      isFeatureBoundary: l,
      onSkip: d
    } = this.props;
    return r && o ? typeof a == "function" ? a({
      error: o,
      resetError: this.resetError
    }) : a || /* @__PURE__ */ n.jsx(xi, { error: o, resetError: this.resetError, isAppLevel: !l, name: s, onSkip: d }) : i;
  }
}
const Lr = ({
  isAppLevel: e = !1,
  isFeatureBoundary: t = !1,
  children: r,
  ...o
}) => {
  const i = e ? "app" : t ? "feature" : "component", a = {
    resetOnPropsChange: i !== "app",
    // App-level boundaries should not reset on props change
    resetOnUnmount: i !== "app",
    // App-level boundaries should not reset on unmount
    isFeatureBoundary: i === "feature"
  };
  return /* @__PURE__ */ n.jsx(yi, { ...a, ...o, children: r });
}, dl = (e) => /* @__PURE__ */ n.jsx(Lr, { isAppLevel: !0, ...e }), ul = ({
  featureName: e,
  children: t,
  ...r
}) => /* @__PURE__ */ n.jsx(Lr, { isFeatureBoundary: !0, name: e, children: t, ...r }), bi = /* @__PURE__ */ c.div.withConfig({
  displayName: "TabContainer",
  componentId: "sc-lgz9vh-0"
})(["display:flex;flex-direction:column;width:100%;"]), vi = /* @__PURE__ */ c.div.withConfig({
  displayName: "TabList",
  componentId: "sc-lgz9vh-1"
})(["display:flex;border-bottom:1px solid ", ";margin-bottom:", ";"], ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.spacing.md), wi = /* @__PURE__ */ c.button.withConfig({
  displayName: "TabButton",
  componentId: "sc-lgz9vh-2"
})(["padding:", " ", ";background:none;border:none;border-bottom:2px solid ", ";color:", ";font-weight:", ";cursor:pointer;transition:all ", ";&:hover{color:", ";}&:focus{outline:none;color:", ";}"], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.spacing.md, ({
  active: e,
  theme: t
}) => e ? t.colors.primary : "transparent", ({
  active: e,
  theme: t
}) => e ? t.colors.primary : t.colors.textSecondary, ({
  active: e,
  theme: t
}) => e ? t.fontWeights.semibold : t.fontWeights.regular, ({
  theme: e
}) => e.transitions.fast, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.primary), Si = /* @__PURE__ */ c.div.withConfig({
  displayName: "TabContent",
  componentId: "sc-lgz9vh-3"
})(["padding:", " 0;"], ({
  theme: e
}) => e.spacing.sm), Ci = ({
  tabs: e,
  defaultTab: t,
  className: r,
  activeTab: o,
  onTabClick: i
}) => {
  var f;
  const [a, s] = H(t || e[0].id), l = o !== void 0 ? o : a, d = (u, m) => {
    u.preventDefault(), u.stopPropagation(), i ? i(m) : s(m);
  };
  return /* @__PURE__ */ n.jsxs(bi, { className: r, children: [
    /* @__PURE__ */ n.jsx(vi, { children: e.map((u) => /* @__PURE__ */ n.jsx(
      wi,
      {
        active: l === u.id,
        onClick: (m) => d(m, u.id),
        type: "button",
        form: "",
        tabIndex: 0,
        "data-tab-id": u.id,
        children: u.label
      },
      u.id
    )) }),
    /* @__PURE__ */ n.jsx(Si, { children: (f = e.find((u) => u.id === l)) == null ? void 0 : f.content })
  ] });
}, pl = Ci, Ei = {
  required: (e = "This field is required") => ({
    validate: (t) => typeof t == "string" ? t.trim().length > 0 : typeof t == "number" ? !isNaN(t) : Array.isArray(t) ? t.length > 0 : t != null && t !== void 0,
    message: e
  }),
  email: (e = "Please enter a valid email address") => ({
    validate: (t) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t),
    message: e
  }),
  minLength: (e, t) => ({
    validate: (r) => r.length >= e,
    message: t || `Must be at least ${e} characters`
  }),
  maxLength: (e, t) => ({
    validate: (r) => r.length <= e,
    message: t || `Must be no more than ${e} characters`
  }),
  min: (e, t) => ({
    validate: (r) => r >= e,
    message: t || `Must be at least ${e}`
  }),
  max: (e, t) => ({
    validate: (r) => r <= e,
    message: t || `Must be no more than ${e}`
  }),
  pattern: (e, t) => ({
    validate: (r) => e.test(r),
    message: t
  })
}, Ti = (e = {}) => {
  const {
    initialValue: t = "",
    required: r = !1,
    type: o = "text",
    validationRules: i = [],
    validateOnChange: a = !1,
    validateOnBlur: s = !0,
    transform: l
  } = e, d = q(() => {
    const R = [...i];
    return r && !i.some((D) => D.message.toLowerCase().includes("required")) && R.unshift(Ei.required()), R;
  }, [r, i]), [f, u] = H(t), [m, y] = H(null), [x, h] = H(!1), [b, E] = H(!1), S = q(() => f !== t, [f, t]), C = q(() => m === null && !b, [m, b]), k = q(() => m === null && !b, [m, b]), $ = z(async () => {
    E(!0);
    try {
      for (const R of d)
        if (!R.validate(f))
          return y(R.message), E(!1), !1;
      return y(null), E(!1), !0;
    } catch {
      return y("Validation error occurred"), E(!1), !1;
    }
  }, [f, d]), j = z(() => {
    u(t), y(null), h(!1), E(!1);
  }, [t]), N = z((R) => {
    let D;
    o === "number" ? D = parseFloat(R.target.value) || 0 : D = R.target.value, l && (D = l(D)), u(D), a && x && setTimeout(() => $(), 0);
  }, [o, l, a, x, $]), _ = z((R) => {
    h(!0), s && $();
  }, [s, $]);
  return {
    // State
    value: f,
    error: m,
    touched: x,
    dirty: S,
    valid: C,
    isValid: k,
    validating: b,
    // Actions
    setValue: u,
    setError: y,
    setTouched: h,
    validate: $,
    reset: j,
    handleChange: N,
    handleBlur: _
  };
}, Ii = /* @__PURE__ */ c.div.withConfig({
  displayName: "FieldContainer",
  componentId: "sc-oh07s1-0"
})(["display:flex;flex-direction:column;gap:", ";width:100%;margin-bottom:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xs) || "4px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.md) || "12px";
}), Ri = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-oh07s1-1"
})(["font-size:", ";font-weight:", ";color:", ";", ""], ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.sm) || "14px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.fontWeights) == null ? void 0 : t.medium) || "500";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textPrimary) || "#ffffff";
}, ({
  $required: e
}) => e && g(["&::after{content:' *';color:", ";}"], ({
  theme: t
}) => {
  var r;
  return ((r = t.colors) == null ? void 0 : r.error) || "#dc2626";
})), It = /* @__PURE__ */ g(["width:100%;border:1px solid ", ";border-radius:", ";background-color:", ";color:", ";font-size:", ";padding:", ";transition:", ";&:focus{outline:none;border-color:", ";box-shadow:0 0 0 2px ", ";}&:disabled{background-color:", ";color:", ";cursor:not-allowed;}&::placeholder{color:", ";}"], ({
  theme: e,
  $hasError: t
}) => {
  var r, o;
  return t ? ((r = e.colors) == null ? void 0 : r.error) || "#dc2626" : ((o = e.colors) == null ? void 0 : o.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.borderRadius) == null ? void 0 : t.sm) || "4px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.surface) || "#1f2937";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textPrimary) || "#ffffff";
}, ({
  theme: e,
  $size: t
}) => {
  var r, o, i;
  switch (t) {
    case "sm":
      return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
    case "lg":
      return ((o = e.fontSizes) == null ? void 0 : o.lg) || "18px";
    default:
      return ((i = e.fontSizes) == null ? void 0 : i.md) || "16px";
  }
}, ({
  theme: e,
  $size: t
}) => {
  var r, o, i, a, s, l;
  switch (t) {
    case "sm":
      return `${((r = e.spacing) == null ? void 0 : r.xs) || "4px"} ${((o = e.spacing) == null ? void 0 : o.sm) || "8px"}`;
    case "lg":
      return `${((i = e.spacing) == null ? void 0 : i.md) || "12px"} ${((a = e.spacing) == null ? void 0 : a.lg) || "16px"}`;
    default:
      return `${((s = e.spacing) == null ? void 0 : s.sm) || "8px"} ${((l = e.spacing) == null ? void 0 : l.md) || "12px"}`;
  }
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.transitions) == null ? void 0 : t.fast) || "all 0.2s ease";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626";
}, ({
  theme: e
}) => {
  var t;
  return (t = e.colors) != null && t.primary ? `${e.colors.primary}20` : "rgba(220, 38, 38, 0.2)";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.chartGrid) || "#374151";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textSecondary) || "#9ca3af";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textSecondary) || "#9ca3af";
}), ji = /* @__PURE__ */ c.input.withConfig({
  displayName: "StyledInput",
  componentId: "sc-oh07s1-2"
})(["", ""], It), Ni = /* @__PURE__ */ c.textarea.withConfig({
  displayName: "StyledTextarea",
  componentId: "sc-oh07s1-3"
})(["", " resize:vertical;min-height:80px;"], It), Di = /* @__PURE__ */ c.select.withConfig({
  displayName: "StyledSelect",
  componentId: "sc-oh07s1-4"
})(["", " cursor:pointer;"], It), Li = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-oh07s1-5"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.xs) || "12px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.error) || "#dc2626";
}), ki = /* @__PURE__ */ c.div.withConfig({
  displayName: "HelpText",
  componentId: "sc-oh07s1-6"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.xs) || "12px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textSecondary) || "#9ca3af";
}), _i = (e) => {
  const {
    name: t,
    label: r,
    placeholder: o,
    disabled: i = !1,
    className: a,
    size: s = "md",
    helpText: l,
    inputType: d = "input",
    options: f = [],
    rows: u = 4,
    onChange: m,
    onBlur: y,
    ...x
  } = e, h = Ti({
    ...x,
    validateOnBlur: !0
  });
  Ce.useEffect(() => {
    m && m(h.value);
  }, [h.value, m]);
  const b = (C) => {
    h.handleBlur(C), y && y();
  }, E = {
    id: t,
    name: t,
    value: h.value,
    onChange: h.handleChange,
    onBlur: b,
    disabled: i,
    placeholder: o,
    $hasError: !!h.error,
    $disabled: i,
    $size: s
  }, S = () => {
    switch (d) {
      case "textarea":
        return /* @__PURE__ */ n.jsx(Ni, { ...E, rows: u });
      case "select":
        return /* @__PURE__ */ n.jsxs(Di, { ...E, children: [
          o && /* @__PURE__ */ n.jsx("option", { value: "", disabled: !0, children: o }),
          f.map((C) => /* @__PURE__ */ n.jsx("option", { value: C.value, children: C.label }, C.value))
        ] });
      default:
        return /* @__PURE__ */ n.jsx(ji, { ...E, type: x.type || "text" });
    }
  };
  return /* @__PURE__ */ n.jsxs(Ii, { className: a, children: [
    r && /* @__PURE__ */ n.jsx(Ri, { htmlFor: t, $required: !!x.required, children: r }),
    S(),
    h.error && h.touched && /* @__PURE__ */ n.jsx(Li, { role: "alert", children: h.error }),
    l && !h.error && /* @__PURE__ */ n.jsx(ki, { children: l })
  ] });
}, fl = _i, gl = {
  string: (e) => (t, r) => {
    const o = String(t[e] || ""), i = String(r[e] || "");
    return o.localeCompare(i);
  },
  number: (e) => (t, r) => {
    const o = Number(t[e]) || 0, i = Number(r[e]) || 0;
    return o - i;
  },
  date: (e) => (t, r) => {
    const o = new Date(t[e]).getTime(), i = new Date(r[e]).getTime();
    return o - i;
  },
  boolean: (e) => (t, r) => {
    const o = !!t[e], i = !!r[e];
    return Number(o) - Number(i);
  }
}, Mi = ({
  data: e,
  columns: t,
  defaultSort: r
}) => {
  const [o, i] = H(r ? {
    field: r.field,
    direction: r.direction
  } : null), a = z((u) => {
    const m = t.find((y) => y.field === u);
    m != null && m.sortable && i((y) => {
      var x;
      if ((y == null ? void 0 : y.field) === u)
        return {
          field: u,
          direction: y.direction === "asc" ? "desc" : "asc"
        };
      {
        const h = typeof ((x = e[0]) == null ? void 0 : x[u]) == "number" ? "desc" : "asc";
        return {
          field: u,
          direction: h
        };
      }
    });
  }, [t, e]), s = q(() => {
    if (!o)
      return e;
    const u = t.find((y) => y.field === o.field);
    return u ? [...e].sort((y, x) => {
      let h = 0;
      if (u.sortFn)
        h = u.sortFn(y, x);
      else {
        const b = y[o.field], E = x[o.field];
        typeof b == "string" && typeof E == "string" ? h = b.localeCompare(E) : typeof b == "number" && typeof E == "number" ? h = b - E : h = String(b).localeCompare(String(E));
      }
      return o.direction === "asc" ? h : -h;
    }) : e;
  }, [e, o, t]), l = z((u) => !o || o.field !== u ? null : o.direction === "asc" ? "↑" : "↓", [o]), d = z((u) => (o == null ? void 0 : o.field) === u, [o]), f = z((u) => (o == null ? void 0 : o.field) === u ? o.direction : null, [o]);
  return {
    sortedData: s,
    sortConfig: o,
    handleSort: a,
    getSortIcon: l,
    isSorted: d,
    getSortDirection: f
  };
}, cr = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-13j9udn-0"
})(["overflow-x:auto;border-radius:", ";border:1px solid ", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.borderRadius) == null ? void 0 : t.md) || "8px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.border) || "#4b5563";
}), Pi = /* @__PURE__ */ c.table.withConfig({
  displayName: "Table",
  componentId: "sc-13j9udn-1"
})(["width:100%;border-collapse:collapse;font-size:", ";"], ({
  theme: e,
  $size: t
}) => {
  var r, o, i;
  switch (t) {
    case "sm":
      return ((r = e.fontSizes) == null ? void 0 : r.xs) || "12px";
    case "lg":
      return ((o = e.fontSizes) == null ? void 0 : o.md) || "16px";
    default:
      return ((i = e.fontSizes) == null ? void 0 : i.sm) || "14px";
  }
}), Oi = /* @__PURE__ */ c.thead.withConfig({
  displayName: "TableHead",
  componentId: "sc-13j9udn-2"
})(["background-color:", ";border-bottom:2px solid ", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.surface) || "#1f2937";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.border) || "#4b5563";
}), Fi = /* @__PURE__ */ c.tbody.withConfig({
  displayName: "TableBody",
  componentId: "sc-13j9udn-3"
})([""]), lr = /* @__PURE__ */ c.tr.withConfig({
  displayName: "TableRow",
  componentId: "sc-13j9udn-4"
})(["", " ", " ", " border-bottom:1px solid ", ";"], ({
  $striped: e,
  theme: t
}) => {
  var r;
  return e && g(["&:nth-child(even){background-color:", ";}"], ((r = t.colors) == null ? void 0 : r.background) || "#0f0f0f");
}, ({
  $hoverable: e,
  theme: t
}) => {
  var r;
  return e && g(["&:hover{background-color:", ";}"], ((r = t.colors) == null ? void 0 : r.surface) || "#1f2937");
}, ({
  $clickable: e
}) => e && g(["cursor:pointer;"]), ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.border) || "#4b5563";
}), $i = /* @__PURE__ */ c.th.withConfig({
  displayName: "TableHeaderCell",
  componentId: "sc-13j9udn-5"
})(["text-align:left;font-weight:", ";color:", ";cursor:", ";user-select:none;transition:", ";padding:", ";&:hover{", "}&:focus{outline:2px solid ", ";outline-offset:-2px;}"], ({
  theme: e
}) => {
  var t;
  return ((t = e.fontWeights) == null ? void 0 : t.semibold) || "600";
}, ({
  theme: e,
  $active: t
}) => {
  var r, o;
  return t ? ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626" : ((o = e.colors) == null ? void 0 : o.textPrimary) || "#ffffff";
}, ({
  $sortable: e
}) => e ? "pointer" : "default", ({
  theme: e
}) => {
  var t;
  return ((t = e.transitions) == null ? void 0 : t.fast) || "all 0.2s ease";
}, ({
  theme: e,
  $size: t
}) => {
  var r, o, i, a, s, l;
  switch (t) {
    case "sm":
      return `${((r = e.spacing) == null ? void 0 : r.xs) || "4px"} ${((o = e.spacing) == null ? void 0 : o.sm) || "8px"}`;
    case "lg":
      return `${((i = e.spacing) == null ? void 0 : i.md) || "12px"} ${((a = e.spacing) == null ? void 0 : a.lg) || "16px"}`;
    default:
      return `${((s = e.spacing) == null ? void 0 : s.sm) || "8px"} ${((l = e.spacing) == null ? void 0 : l.md) || "12px"}`;
  }
}, ({
  $sortable: e,
  theme: t
}) => {
  var r;
  return e && g(["color:", ";"], ((r = t.colors) == null ? void 0 : r.primary) || "#dc2626");
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626";
}), Ai = /* @__PURE__ */ c.td.withConfig({
  displayName: "TableCell",
  componentId: "sc-13j9udn-6"
})(["padding:", ";color:", ";"], ({
  theme: e,
  $size: t
}) => {
  var r, o, i, a, s, l;
  switch (t) {
    case "sm":
      return `${((r = e.spacing) == null ? void 0 : r.xs) || "4px"} ${((o = e.spacing) == null ? void 0 : o.sm) || "8px"}`;
    case "lg":
      return `${((i = e.spacing) == null ? void 0 : i.md) || "12px"} ${((a = e.spacing) == null ? void 0 : a.lg) || "16px"}`;
    default:
      return `${((s = e.spacing) == null ? void 0 : s.sm) || "8px"} ${((l = e.spacing) == null ? void 0 : l.md) || "12px"}`;
  }
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textPrimary) || "#ffffff";
}), zi = /* @__PURE__ */ c.span.withConfig({
  displayName: "SortIcon",
  componentId: "sc-13j9udn-7"
})(["display:inline-block;margin-left:", ";font-size:", ";&::after{content:'", "';}"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xs) || "4px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.sm) || "14px";
}, ({
  $direction: e
}) => e === "asc" ? "↑" : "↓"), Vi = /* @__PURE__ */ c.div.withConfig({
  displayName: "EmptyState",
  componentId: "sc-13j9udn-8"
})(["padding:", ";text-align:center;color:", ";font-style:italic;"], ({
  theme: e,
  $size: t
}) => {
  var r, o, i;
  switch (t) {
    case "sm":
      return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
    case "lg":
      return ((o = e.spacing) == null ? void 0 : o.xl) || "24px";
    default:
      return ((i = e.spacing) == null ? void 0 : i.lg) || "16px";
  }
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textSecondary) || "#9ca3af";
}), Gi = ({
  data: e,
  columns: t,
  className: r,
  emptyMessage: o = "No data available",
  defaultSort: i,
  renderCell: a,
  onRowClick: s,
  size: l = "md",
  striped: d = !0,
  hoverable: f = !0
}) => {
  const {
    sortedData: u,
    handleSort: m,
    getSortIcon: y,
    isSorted: x
  } = Mi({
    data: e,
    columns: t,
    defaultSort: i
  });
  return e.length === 0 ? /* @__PURE__ */ n.jsx(cr, { className: r, children: /* @__PURE__ */ n.jsx(Vi, { $size: l, children: o }) }) : /* @__PURE__ */ n.jsx(cr, { className: r, children: /* @__PURE__ */ n.jsxs(Pi, { $size: l, $striped: d, $hoverable: f, children: [
    /* @__PURE__ */ n.jsx(Oi, { children: /* @__PURE__ */ n.jsx(lr, { $striped: !1, $hoverable: !1, $clickable: !1, children: t.map((h) => /* @__PURE__ */ n.jsxs($i, { $sortable: h.sortable || !1, $active: x(h.field), $size: l, onClick: () => h.sortable && m(h.field), tabIndex: h.sortable ? 0 : -1, onKeyDown: (b) => {
      h.sortable && (b.key === "Enter" || b.key === " ") && (b.preventDefault(), m(h.field));
    }, role: h.sortable ? "button" : void 0, "aria-sort": x(h.field) ? y(h.field) === "↑" ? "ascending" : "descending" : void 0, children: [
      h.label,
      x(h.field) && /* @__PURE__ */ n.jsx(zi, { $direction: y(h.field) === "↑" ? "asc" : "desc" })
    ] }, String(h.field))) }) }),
    /* @__PURE__ */ n.jsx(Fi, { children: u.map((h, b) => /* @__PURE__ */ n.jsx(lr, { $striped: d, $hoverable: f, $clickable: !!s, onClick: () => s == null ? void 0 : s(h, b), tabIndex: s ? 0 : -1, onKeyDown: (E) => {
      s && (E.key === "Enter" || E.key === " ") && (E.preventDefault(), s(h, b));
    }, role: s ? "button" : void 0, children: t.map((E) => {
      const S = h[E.field];
      return /* @__PURE__ */ n.jsx(Ai, { $size: l, children: a ? a(S, h, E) : String(S) }, String(E.field));
    }) }, b)) })
  ] }) });
}, ml = Gi, Bi = /* @__PURE__ */ c.div.withConfig({
  displayName: "FieldContainer",
  componentId: "sc-i922jg-0"
})(["display:flex;flex-direction:column;margin-bottom:", ";"], ({
  theme: e
}) => e.spacing.md), qi = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-i922jg-1"
})(["font-size:", ";font-weight:500;margin-bottom:", ";color:", ";.required-indicator{color:", ";margin-left:", ";}"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e,
  hasError: t
}) => t ? e.colors.error : e.colors.textPrimary, ({
  theme: e
}) => e.colors.error, ({
  theme: e
}) => e.spacing.xxs), Hi = /* @__PURE__ */ c.div.withConfig({
  displayName: "HelperText",
  componentId: "sc-i922jg-2"
})(["font-size:", ";color:", ";margin-top:", ";"], ({
  theme: e
}) => e.fontSizes.xs, ({
  theme: e,
  hasError: t
}) => t ? e.colors.error : e.colors.textSecondary, ({
  theme: e
}) => e.spacing.xxs), hl = ({
  children: e,
  label: t,
  helperText: r,
  required: o = !1,
  error: i,
  className: a,
  id: s,
  ...l
}) => {
  const d = s || `field-${Math.random().toString(36).substr(2, 9)}`, f = Ce.Children.map(e, (u) => Ce.isValidElement(u) ? Ce.cloneElement(u, {
    id: d,
    required: o,
    error: i
  }) : u);
  return /* @__PURE__ */ n.jsxs(Bi, { className: a, ...l, children: [
    /* @__PURE__ */ n.jsxs(qi, { htmlFor: d, hasError: !!i, children: [
      t,
      o && /* @__PURE__ */ n.jsx("span", { className: "required-indicator", children: "*" })
    ] }),
    f,
    (r || i) && /* @__PURE__ */ n.jsx(Hi, { hasError: !!i, children: i || r })
  ] });
}, Ui = /* @__PURE__ */ De(["from{opacity:0;}to{opacity:1;}"]), Yi = /* @__PURE__ */ De(["from{transform:translateY(-20px);opacity:0;}to{transform:translateY(0);opacity:1;}"]), Wi = /* @__PURE__ */ c.div.withConfig({
  displayName: "Backdrop",
  componentId: "sc-1cuqxtr-0"
})(["position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:", ";animation:", " 0.2s ease-out;"], ({
  zIndex: e
}) => e || 1e3, Ui), Ki = /* @__PURE__ */ c.div.withConfig({
  displayName: "ModalContainer",
  componentId: "sc-1cuqxtr-1"
})(["background-color:", ";border-radius:", ";box-shadow:", ";display:flex;flex-direction:column;max-height:", ";width:", ";max-width:95vw;animation:", " 0.2s ease-out;position:relative;", " ", ""], ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.shadows.lg, ({
  size: e
}) => e === "fullscreen" ? "100vh" : "90vh", ({
  size: e
}) => {
  switch (e) {
    case "small":
      return "400px";
    case "medium":
      return "600px";
    case "large":
      return "800px";
    case "fullscreen":
      return "100vw";
    default:
      return "600px";
  }
}, Yi, ({
  size: e
}) => e === "fullscreen" && g(["height:100vh;border-radius:0;"]), ({
  centered: e
}) => e && g(["margin:auto;"])), Qi = /* @__PURE__ */ c.div.withConfig({
  displayName: "ModalHeader",
  componentId: "sc-1cuqxtr-2"
})(["display:flex;justify-content:space-between;align-items:center;padding:", ";border-bottom:1px solid ", ";"], ({
  theme: e
}) => `${e.spacing.md} ${e.spacing.lg}`, ({
  theme: e
}) => e.colors.border), Xi = /* @__PURE__ */ c.h3.withConfig({
  displayName: "ModalTitle",
  componentId: "sc-1cuqxtr-3"
})(["margin:0;font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.lg, ({
  theme: e
}) => e.fontWeights.semibold, ({
  theme: e
}) => e.colors.textPrimary), Ji = /* @__PURE__ */ c.button.withConfig({
  displayName: "CloseButton",
  componentId: "sc-1cuqxtr-4"
})(["background:none;border:none;cursor:pointer;font-size:", ";color:", ";padding:0;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:", ";&:hover{background-color:", ";}&:focus{outline:none;box-shadow:0 0 0 2px ", "33;}"], ({
  theme: e
}) => e.fontSizes.xl, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary), Zi = /* @__PURE__ */ c.div.withConfig({
  displayName: "ModalContent",
  componentId: "sc-1cuqxtr-5"
})(["padding:", ";", ""], ({
  theme: e
}) => e.spacing.lg, ({
  scrollable: e
}) => e && g(["overflow-y:auto;flex:1;"])), es = /* @__PURE__ */ c.div.withConfig({
  displayName: "ModalFooter",
  componentId: "sc-1cuqxtr-6"
})(["display:flex;justify-content:flex-end;gap:", ";padding:", ";border-top:1px solid ", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => `${e.spacing.md} ${e.spacing.lg}`, ({
  theme: e
}) => e.colors.border), xl = ({
  isOpen: e,
  title: t = "",
  children: r,
  onClose: o,
  size: i = "medium",
  closeOnOutsideClick: a = !0,
  showCloseButton: s = !0,
  footer: l,
  hasFooter: d = !0,
  primaryActionText: f = "",
  onPrimaryAction: u,
  primaryActionDisabled: m = !1,
  primaryActionLoading: y = !1,
  secondaryActionText: x = "",
  onSecondaryAction: h,
  secondaryActionDisabled: b = !1,
  className: E = "",
  zIndex: S = 1e3,
  centered: C = !0,
  // hasBackdrop = true, // Unused prop
  scrollable: k = !0
}) => {
  const $ = Ir(null);
  ae(() => {
    const R = (D) => {
      D.key === "Escape" && e && a && o();
    };
    return document.addEventListener("keydown", R), () => {
      document.removeEventListener("keydown", R);
    };
  }, [e, o, a]);
  const j = (R) => {
    $.current && !$.current.contains(R.target) && a && o();
  };
  ae(() => (e ? document.body.style.overflow = "hidden" : document.body.style.overflow = "", () => {
    document.body.style.overflow = "";
  }), [e]);
  const N = /* @__PURE__ */ n.jsxs(n.Fragment, { children: [
    x && /* @__PURE__ */ n.jsx(le, { variant: "outline", onClick: h, disabled: b, children: x }),
    f && /* @__PURE__ */ n.jsx(le, { onClick: u, disabled: m, loading: y, children: f })
  ] });
  return e ? ho(/* @__PURE__ */ n.jsx(Wi, { onClick: j, zIndex: S, children: /* @__PURE__ */ n.jsxs(Ki, { ref: $, size: i, className: E, centered: C, scrollable: k, onClick: (R) => R.stopPropagation(), children: [
    (t || s) && /* @__PURE__ */ n.jsxs(Qi, { children: [
      t && /* @__PURE__ */ n.jsx(Xi, { children: t }),
      s && /* @__PURE__ */ n.jsx(Ji, { onClick: o, "aria-label": "Close", children: "×" })
    ] }),
    /* @__PURE__ */ n.jsx(Zi, { scrollable: k, children: r }),
    d && (l || f || x) && /* @__PURE__ */ n.jsx(es, { children: l || N })
  ] }) }), document.body) : null;
}, ts = /* @__PURE__ */ c.div.withConfig({
  displayName: "TableContainer",
  componentId: "sc-4as3uq-0"
})(["width:100%;overflow:auto;", " ", ""], ({
  height: e
}) => e && `height: ${e};`, ({
  scrollable: e
}) => e && "overflow-x: auto;"), rs = /* @__PURE__ */ c.table.withConfig({
  displayName: "StyledTable",
  componentId: "sc-4as3uq-1"
})(["width:100%;border-collapse:separate;border-spacing:0;font-size:", ";", " ", ""], ({
  theme: e
}) => e.fontSizes.sm, ({
  bordered: e,
  theme: t
}) => e && g(["border:1px solid ", ";border-radius:", ";"], t.colors.border, t.borderRadius.sm), ({
  compact: e,
  theme: t
}) => e ? g(["th,td{padding:", " ", ";}"], t.spacing.xs, t.spacing.sm) : g(["th,td{padding:", " ", ";}"], t.spacing.sm, t.spacing.md)), os = /* @__PURE__ */ c.thead.withConfig({
  displayName: "TableHeader",
  componentId: "sc-4as3uq-2"
})(["", ""], ({
  stickyHeader: e
}) => e && g(["position:sticky;top:0;z-index:1;"])), ns = /* @__PURE__ */ c.tr.withConfig({
  displayName: "TableHeaderRow",
  componentId: "sc-4as3uq-3"
})(["background-color:", ";"], ({
  theme: e
}) => e.colors.background), is = /* @__PURE__ */ c.th.withConfig({
  displayName: "TableHeaderCell",
  componentId: "sc-4as3uq-4"
})(["text-align:", ";font-weight:", ";color:", ";border-bottom:1px solid ", ";white-space:nowrap;", " ", " ", ""], ({
  align: e
}) => e || "left", ({
  theme: e
}) => e.fontWeights.semibold, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.colors.border, ({
  width: e
}) => e && `width: ${e};`, ({
  sortable: e
}) => e && g(["cursor:pointer;user-select:none;&:hover{background-color:", "aa;}"], ({
  theme: t
}) => t.colors.background), ({
  isSorted: e,
  theme: t
}) => e && g(["color:", ";"], t.colors.primary)), ss = /* @__PURE__ */ c.span.withConfig({
  displayName: "SortIcon",
  componentId: "sc-4as3uq-5"
})(["display:inline-block;margin-left:", ";&::after{content:'", "';}"], ({
  theme: e
}) => e.spacing.xs, ({
  direction: e
}) => e === "asc" ? "↑" : e === "desc" ? "↓" : "↕"), as = /* @__PURE__ */ c.tbody.withConfig({
  displayName: "TableBody",
  componentId: "sc-4as3uq-6"
})([""]), cs = /* @__PURE__ */ c.tr.withConfig({
  displayName: "TableRow",
  componentId: "sc-4as3uq-7"
})(["", " ", " ", " ", ""], ({
  striped: e,
  theme: t,
  isSelected: r
}) => e && !r && g(["&:nth-child(even){background-color:", "50;}"], t.colors.background), ({
  hoverable: e,
  theme: t,
  isSelected: r
}) => e && !r && g(["&:hover{background-color:", "aa;}"], t.colors.background), ({
  isSelected: e,
  theme: t
}) => e && g(["background-color:", "15;"], t.colors.primary), ({
  isClickable: e
}) => e && g(["cursor:pointer;"])), ls = /* @__PURE__ */ c.td.withConfig({
  displayName: "TableCell",
  componentId: "sc-4as3uq-8"
})(["text-align:", ";border-bottom:1px solid ", ";color:", ";"], ({
  align: e
}) => e || "left", ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.colors.textPrimary), ds = /* @__PURE__ */ c.div.withConfig({
  displayName: "EmptyState",
  componentId: "sc-4as3uq-9"
})(["padding:", ";text-align:center;color:", ";"], ({
  theme: e
}) => e.spacing.xl, ({
  theme: e
}) => e.colors.textSecondary), us = /* @__PURE__ */ c.div.withConfig({
  displayName: "PaginationContainer",
  componentId: "sc-4as3uq-10"
})(["display:flex;justify-content:space-between;align-items:center;padding:", " 0;font-size:", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.fontSizes.sm), ps = /* @__PURE__ */ c.div.withConfig({
  displayName: "PageInfo",
  componentId: "sc-4as3uq-11"
})(["color:", ";"], ({
  theme: e
}) => e.colors.textSecondary), fs = /* @__PURE__ */ c.div.withConfig({
  displayName: "PaginationControls",
  componentId: "sc-4as3uq-12"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.xs), gs = /* @__PURE__ */ c.div.withConfig({
  displayName: "PageSizeSelector",
  componentId: "sc-4as3uq-13"
})(["display:flex;align-items:center;gap:", ";margin-right:", ";"], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.spacing.md), ms = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-4as3uq-14"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:", ";display:flex;align-items:center;justify-content:center;z-index:1;"], ({
  theme: e
}) => `${e.colors.background}80`), hs = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-4as3uq-15"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"], ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary);
function yl({
  columns: e,
  data: t,
  isLoading: r = !1,
  bordered: o = !0,
  striped: i = !0,
  hoverable: a = !0,
  compact: s = !1,
  stickyHeader: l = !1,
  height: d,
  onRowClick: f,
  isRowSelected: u,
  onSort: m,
  sortColumn: y,
  sortDirection: x,
  pagination: h = !1,
  currentPage: b = 1,
  pageSize: E = 10,
  totalRows: S = 0,
  onPageChange: C,
  onPageSizeChange: k,
  className: $,
  emptyMessage: j = "No data available",
  scrollable: N = !0
}) {
  const _ = q(() => e.filter((L) => !L.hidden), [e]), R = q(() => Math.ceil(S / E), [S, E]), D = q(() => {
    if (!h)
      return t;
    const L = (b - 1) * E, B = L + E;
    return S > 0 && t.length <= E ? t : t.slice(L, B);
  }, [t, h, b, E, S]), K = (L) => {
    if (!m)
      return;
    m(L, y === L && x === "asc" ? "desc" : "asc");
  }, W = (L) => {
    L < 1 || L > R || !C || C(L);
  };
  return /* @__PURE__ */ n.jsxs("div", { style: {
    position: "relative"
  }, children: [
    r && /* @__PURE__ */ n.jsx(ms, { children: /* @__PURE__ */ n.jsx(hs, {}) }),
    /* @__PURE__ */ n.jsx(ts, { height: d, scrollable: N, children: /* @__PURE__ */ n.jsxs(rs, { bordered: o, striped: i, compact: s, className: $, children: [
      /* @__PURE__ */ n.jsx(os, { stickyHeader: l, children: /* @__PURE__ */ n.jsx(ns, { children: _.map((L) => /* @__PURE__ */ n.jsxs(is, { sortable: L.sortable, isSorted: y === L.id, align: L.align, width: L.width, onClick: () => L.sortable && K(L.id), children: [
        L.header,
        L.sortable && /* @__PURE__ */ n.jsx(ss, { direction: y === L.id ? x : void 0 })
      ] }, L.id)) }) }),
      /* @__PURE__ */ n.jsx(as, { children: D.length > 0 ? D.map((L, B) => /* @__PURE__ */ n.jsx(cs, { hoverable: a, striped: i, isSelected: u ? u(L, B) : !1, isClickable: !!f, onClick: () => f && f(L, B), children: _.map((te) => /* @__PURE__ */ n.jsx(ls, { align: te.align, children: te.cell(L, B) }, te.id)) }, B)) : /* @__PURE__ */ n.jsx("tr", { children: /* @__PURE__ */ n.jsx("td", { colSpan: _.length, children: /* @__PURE__ */ n.jsx(ds, { children: j }) }) }) })
    ] }) }),
    h && R > 0 && /* @__PURE__ */ n.jsxs(us, { children: [
      /* @__PURE__ */ n.jsxs(ps, { children: [
        "Showing ",
        Math.min((b - 1) * E + 1, S),
        " to",
        " ",
        Math.min(b * E, S),
        " of ",
        S,
        " entries"
      ] }),
      /* @__PURE__ */ n.jsxs("div", { style: {
        display: "flex",
        alignItems: "center"
      }, children: [
        k && /* @__PURE__ */ n.jsxs(gs, { children: [
          /* @__PURE__ */ n.jsx("span", { children: "Show" }),
          /* @__PURE__ */ n.jsx("select", { value: E, onChange: (L) => k(Number(L.target.value)), style: {
            padding: "4px 8px",
            borderRadius: "4px",
            border: "1px solid #ccc"
          }, children: [10, 25, 50, 100].map((L) => /* @__PURE__ */ n.jsx("option", { value: L, children: L }, L)) }),
          /* @__PURE__ */ n.jsx("span", { children: "entries" })
        ] }),
        /* @__PURE__ */ n.jsxs(fs, { children: [
          /* @__PURE__ */ n.jsx(le, { size: "small", variant: "outline", onClick: () => W(1), disabled: b === 1, children: "First" }),
          /* @__PURE__ */ n.jsx(le, { size: "small", variant: "outline", onClick: () => W(b - 1), disabled: b === 1, children: "Prev" }),
          /* @__PURE__ */ n.jsx(le, { size: "small", variant: "outline", onClick: () => W(b + 1), disabled: b === R, children: "Next" }),
          /* @__PURE__ */ n.jsx(le, { size: "small", variant: "outline", onClick: () => W(R), disabled: b === R, children: "Last" })
        ] })
      ] })
    ] })
  ] });
}
const ue = {
  [P.MORNING_BREAKOUT]: {
    type: P.MORNING_BREAKOUT,
    name: "9:50-10:10 Macro",
    timeRange: {
      start: "09:50:00",
      end: "10:10:00"
    },
    description: "Morning breakout period - high volatility after market open",
    characteristics: ["High Volume", "Breakout Setups", "Gap Fills", "Opening Range"],
    volatilityLevel: 5,
    volumeLevel: 5,
    isHighProbability: !0
  },
  [P.MID_MORNING_REVERSION]: {
    type: P.MID_MORNING_REVERSION,
    name: "10:50-11:10 Macro",
    timeRange: {
      start: "10:50:00",
      end: "11:10:00"
    },
    description: "Mid-morning reversion period - mean reversion opportunities",
    characteristics: ["Mean Reversion", "Pullback Setups", "Support/Resistance Tests"],
    volatilityLevel: 3,
    volumeLevel: 3,
    isHighProbability: !0
  },
  [P.PRE_LUNCH]: {
    type: P.PRE_LUNCH,
    name: "11:50-12:10 Macro",
    timeRange: {
      start: "11:50:00",
      end: "12:10:00"
    },
    description: "Pre-lunch macro window - specific high-activity period within lunch session",
    characteristics: ["Consolidation", "Range Trading", "Pre-Lunch Activity"],
    volatilityLevel: 2,
    volumeLevel: 2,
    isHighProbability: !1,
    parentMacro: P.LUNCH_MACRO_EXTENDED
  },
  [P.LUNCH_MACRO_EXTENDED]: {
    type: P.LUNCH_MACRO_EXTENDED,
    name: "Lunch Macro (11:30-13:30)",
    timeRange: {
      start: "11:30:00",
      end: "13:30:00"
    },
    description: "Extended lunch period spanning late morning through early afternoon",
    characteristics: ["Multi-Session", "Lunch Trading", "Lower Volume", "Transition Period"],
    volatilityLevel: 2,
    volumeLevel: 2,
    isHighProbability: !1,
    isMultiSession: !0,
    spansSessions: [Q.NEW_YORK_AM, Q.NEW_YORK_PM],
    subPeriods: []
    // Will be populated with PRE_LUNCH macro
  },
  [P.LUNCH_MACRO]: {
    type: P.LUNCH_MACRO,
    name: "Lunch Macro (12:00-13:30)",
    timeRange: {
      start: "12:00:00",
      end: "13:30:00"
    },
    description: "Traditional lunch time trading - typically lower volume",
    characteristics: ["Low Volume", "Range Bound", "Choppy Price Action"],
    volatilityLevel: 2,
    volumeLevel: 1,
    isHighProbability: !1
  },
  [P.POST_LUNCH]: {
    type: P.POST_LUNCH,
    name: "13:50-14:10 Macro",
    timeRange: {
      start: "13:50:00",
      end: "14:10:00"
    },
    description: "Post-lunch macro window",
    characteristics: ["Volume Pickup", "Trend Resumption"],
    volatilityLevel: 3,
    volumeLevel: 3,
    isHighProbability: !1
  },
  [P.PRE_CLOSE]: {
    type: P.PRE_CLOSE,
    name: "14:50-15:10 Macro",
    timeRange: {
      start: "14:50:00",
      end: "15:10:00"
    },
    description: "Pre-close macro window",
    characteristics: ["Institutional Activity", "Position Adjustments"],
    volatilityLevel: 3,
    volumeLevel: 4,
    isHighProbability: !1
  },
  [P.POWER_HOUR]: {
    type: P.POWER_HOUR,
    name: "15:15-15:45 Macro (Power Hour)",
    timeRange: {
      start: "15:15:00",
      end: "15:45:00"
    },
    description: "Last hour macro - high activity before close",
    characteristics: ["High Volume", "Institutional Flows", "EOD Positioning"],
    volatilityLevel: 4,
    volumeLevel: 5,
    isHighProbability: !0
  },
  [P.MOC]: {
    type: P.MOC,
    name: "MOC (Market on Close)",
    timeRange: {
      start: "15:45:00",
      end: "16:00:00"
    },
    description: "Market on close period",
    characteristics: ["MOC Orders", "Final Positioning", "High Volume"],
    volatilityLevel: 4,
    volumeLevel: 5,
    isHighProbability: !1
  },
  [P.LONDON_OPEN]: {
    type: P.LONDON_OPEN,
    name: "London Open",
    timeRange: {
      start: "08:00:00",
      end: "09:00:00"
    },
    description: "London market opening hour",
    characteristics: ["European Activity", "Currency Moves", "News Reactions"],
    volatilityLevel: 4,
    volumeLevel: 4,
    isHighProbability: !0
  },
  [P.LONDON_NY_OVERLAP]: {
    type: P.LONDON_NY_OVERLAP,
    name: "London/NY Overlap",
    timeRange: {
      start: "14:00:00",
      end: "16:00:00"
    },
    description: "London and New York session overlap",
    characteristics: ["Highest Volume", "Major Moves", "Cross-Market Activity"],
    volatilityLevel: 5,
    volumeLevel: 5,
    isHighProbability: !0
  },
  [P.CUSTOM]: {
    type: P.CUSTOM,
    name: "Custom Period",
    timeRange: {
      start: "00:00:00",
      end: "23:59:59"
    },
    description: "User-defined custom time period",
    characteristics: ["Custom"],
    volatilityLevel: 3,
    volumeLevel: 3,
    isHighProbability: !1
  }
}, xs = () => {
  const e = Object.values(ys).map((i) => ({
    id: i.type,
    ...i
  })), r = [{
    ...ue[P.LUNCH_MACRO_EXTENDED],
    id: "lunch-macro-extended",
    subPeriods: [{
      ...ue[P.PRE_LUNCH],
      id: "pre-lunch-sub"
    }]
  }], o = {};
  return e.forEach((i) => {
    i.macroPeriods.forEach((a) => {
      o[a.type] = {
        ...a,
        parentSession: i.type
      };
    });
  }), r.forEach((i) => {
    o[i.type] = {
      ...i,
      spansSessions: i.spansSessions
    };
  }), {
    sessions: e,
    sessionsByType: e.reduce((i, a) => (i[a.type] = a, i), {}),
    macrosByType: o,
    multiSessionMacros: r
  };
}, ys = {
  [Q.NEW_YORK_AM]: {
    type: Q.NEW_YORK_AM,
    name: "New York AM Session",
    timeRange: {
      start: "09:30:00",
      end: "12:00:00"
    },
    description: "New York morning session - high activity and volatility",
    timezone: "America/New_York",
    characteristics: ["High Volume", "Trend Development", "Breakout Opportunities"],
    color: "#dc2626",
    // F1 Red
    macroPeriods: [{
      ...ue[P.MORNING_BREAKOUT],
      id: "morning-breakout"
    }, {
      ...ue[P.MID_MORNING_REVERSION],
      id: "mid-morning-reversion"
    }, {
      ...ue[P.PRE_LUNCH],
      id: "pre-lunch"
    }]
  },
  [Q.NEW_YORK_PM]: {
    type: Q.NEW_YORK_PM,
    name: "New York PM Session",
    timeRange: {
      start: "12:00:00",
      end: "16:00:00"
    },
    description: "New York afternoon session - institutional activity increases toward close",
    timezone: "America/New_York",
    characteristics: ["Institutional Flows", "EOD Positioning", "Power Hour Activity"],
    color: "#dc2626",
    // F1 Red
    macroPeriods: [{
      ...ue[P.LUNCH_MACRO],
      id: "lunch-macro"
    }, {
      ...ue[P.POST_LUNCH],
      id: "post-lunch"
    }, {
      ...ue[P.PRE_CLOSE],
      id: "pre-close"
    }, {
      ...ue[P.POWER_HOUR],
      id: "power-hour"
    }, {
      ...ue[P.MOC],
      id: "moc"
    }]
  },
  [Q.LONDON]: {
    type: Q.LONDON,
    name: "London Session",
    timeRange: {
      start: "08:00:00",
      end: "16:00:00"
    },
    description: "London trading session - European market activity",
    timezone: "Europe/London",
    characteristics: ["European Activity", "Currency Focus", "News-Driven"],
    color: "#1f2937",
    // Dark Gray
    macroPeriods: [{
      ...ue[P.LONDON_OPEN],
      id: "london-open"
    }, {
      ...ue[P.LONDON_NY_OVERLAP],
      id: "london-ny-overlap"
    }]
  },
  [Q.ASIA]: {
    type: Q.ASIA,
    name: "Asia Session",
    timeRange: {
      start: "18:00:00",
      end: "03:00:00"
    },
    description: "Asian trading session - typically lower volatility",
    timezone: "Asia/Tokyo",
    characteristics: ["Lower Volume", "Range Trading", "News Reactions"],
    color: "#4b5563",
    // Gray
    macroPeriods: []
  },
  [Q.PRE_MARKET]: {
    type: Q.PRE_MARKET,
    name: "Pre-Market",
    timeRange: {
      start: "04:00:00",
      end: "09:30:00"
    },
    description: "Pre-market trading hours",
    timezone: "America/New_York",
    characteristics: ["Low Volume", "News Reactions", "Gap Setups"],
    color: "#6b7280",
    // Light Gray
    macroPeriods: []
  },
  [Q.AFTER_HOURS]: {
    type: Q.AFTER_HOURS,
    name: "After Hours",
    timeRange: {
      start: "16:00:00",
      end: "20:00:00"
    },
    description: "After-hours trading",
    timezone: "America/New_York",
    characteristics: ["Low Volume", "Earnings Reactions", "News-Driven"],
    color: "#6b7280",
    // Light Gray
    macroPeriods: []
  },
  [Q.OVERNIGHT]: {
    type: Q.OVERNIGHT,
    name: "Overnight",
    timeRange: {
      start: "20:00:00",
      end: "04:00:00"
    },
    description: "Overnight session",
    timezone: "America/New_York",
    characteristics: ["Very Low Volume", "Futures Activity"],
    color: "#374151",
    // Dark Gray
    macroPeriods: []
  }
};
class se {
  /**
   * Initialize and get the session hierarchy
   */
  static getSessionHierarchy() {
    return this.hierarchy || (this.hierarchy = this.buildHierarchy()), this.hierarchy;
  }
  /**
   * Build the complete session hierarchy with overlapping macro support
   */
  static buildHierarchy() {
    return xs();
  }
  /**
   * Parse time string to minutes since midnight
   */
  static timeToMinutes(t) {
    const [r, o, i = 0] = t.split(":").map(Number);
    return r * 60 + o + i / 60;
  }
  /**
   * Convert minutes since midnight to time string
   */
  static minutesToTime(t) {
    const r = Math.floor(t / 60), o = Math.floor(t % 60), i = Math.floor(t % 1 * 60);
    return `${r.toString().padStart(2, "0")}:${o.toString().padStart(2, "0")}:${i.toString().padStart(2, "0")}`;
  }
  /**
   * Check if a time falls within a time range
   */
  static isTimeInRange(t, r) {
    const o = this.timeToMinutes(t), i = this.timeToMinutes(r.start), a = this.timeToMinutes(r.end);
    return a < i ? o >= i || o <= a : o >= i && o <= a;
  }
  /**
   * Validate a time and suggest appropriate session/macro with overlapping support
   */
  static validateTime(t) {
    var a;
    if (!/^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/.test(t))
      return {
        isValid: !1,
        error: "Invalid time format. Use HH:MM or HH:MM:SS format."
      };
    const o = this.getSessionHierarchy(), i = [];
    for (const [s, l] of Object.entries(o.macrosByType))
      this.isTimeInRange(t, l.timeRange) && i.push({
        type: s,
        macro: l,
        isSubPeriod: !!l.parentMacro
      });
    if (i.length > 0) {
      const l = i.sort((f, u) => {
        if (f.isSubPeriod && !u.isSubPeriod)
          return -1;
        if (!f.isSubPeriod && u.isSubPeriod)
          return 1;
        const m = this.timeToMinutes(f.macro.timeRange.end) - this.timeToMinutes(f.macro.timeRange.start), y = this.timeToMinutes(u.macro.timeRange.end) - this.timeToMinutes(u.macro.timeRange.start);
        return m - y;
      })[0], d = i.length > 1;
      return {
        isValid: !0,
        suggestedMacro: l.type,
        suggestedSession: l.macro.parentSession || ((a = l.macro.spansSessions) == null ? void 0 : a[0]),
        warning: d ? `Time falls within ${i.length} overlapping macro periods. Suggesting most specific: ${l.macro.name}` : void 0
      };
    }
    for (const s of o.sessions)
      if (this.isTimeInRange(t, s.timeRange))
        return {
          isValid: !0,
          suggestedSession: s.type,
          warning: "Time falls within session but not in a specific macro period."
        };
    return {
      isValid: !0,
      warning: "Time does not fall within any defined session or macro period."
    };
  }
  /**
   * Get session by type
   */
  static getSession(t) {
    return this.getSessionHierarchy().sessionsByType[t] || null;
  }
  /**
   * Get macro period by type
   */
  static getMacroPeriod(t) {
    return this.getSessionHierarchy().macrosByType[t] || null;
  }
  /**
   * Get all macro periods for a session
   */
  static getMacroPeriodsForSession(t) {
    const r = this.getSession(t);
    return (r == null ? void 0 : r.macroPeriods) || [];
  }
  /**
   * Create a session selection
   */
  static createSessionSelection(t, r, o) {
    if (r) {
      const i = this.getMacroPeriod(r);
      return {
        session: i == null ? void 0 : i.parentSession,
        macroPeriod: r,
        displayLabel: (i == null ? void 0 : i.name) || "Unknown Macro",
        selectionType: "macro"
      };
    }
    if (t) {
      const i = this.getSession(t);
      return {
        session: t,
        displayLabel: (i == null ? void 0 : i.name) || "Unknown Session",
        selectionType: "session"
      };
    }
    return o ? {
      customTimeRange: o,
      displayLabel: `${o.start} - ${o.end}`,
      selectionType: "custom"
    } : {
      displayLabel: "No Selection",
      selectionType: "custom"
    };
  }
  /**
   * Filter sessions and macros based on criteria
   */
  static filterSessions(t = {}) {
    var a, s;
    const r = this.getSessionHierarchy();
    let o = [...r.sessions], i = Object.values(r.macrosByType);
    return t.activeOnly && (o = o.filter((l) => l.isActive)), (a = t.sessionTypes) != null && a.length && (o = o.filter((l) => t.sessionTypes.includes(l.type))), (s = t.macroTypes) != null && s.length && (i = i.filter((l) => t.macroTypes.includes(l.type))), t.highProbabilityOnly && (i = i.filter((l) => l.isHighProbability)), t.minVolatility !== void 0 && (i = i.filter((l) => l.volatilityLevel >= t.minVolatility)), t.maxVolatility !== void 0 && (i = i.filter((l) => l.volatilityLevel <= t.maxVolatility)), {
      sessions: o,
      macros: i
    };
  }
  /**
   * Get current active session based on current time
   */
  static getCurrentSession() {
    const t = /* @__PURE__ */ new Date(), r = `${t.getHours().toString().padStart(2, "0")}:${t.getMinutes().toString().padStart(2, "0")}:00`, o = this.validateTime(r);
    return o.suggestedMacro ? this.createSessionSelection(o.suggestedSession, o.suggestedMacro) : o.suggestedSession ? this.createSessionSelection(o.suggestedSession) : null;
  }
  /**
   * Check if two time ranges overlap
   */
  static timeRangesOverlap(t, r) {
    const o = this.timeToMinutes(t.start), i = this.timeToMinutes(t.end), a = this.timeToMinutes(r.start), s = this.timeToMinutes(r.end);
    return Math.max(o, a) < Math.min(i, s);
  }
  /**
   * Get display options for UI dropdowns
   */
  static getDisplayOptions() {
    const t = this.getSessionHierarchy(), r = t.sessions.map((i) => ({
      value: i.type,
      label: i.name,
      group: "Sessions"
    })), o = Object.values(t.macrosByType).filter((i) => i.parentSession).map((i) => {
      var a;
      return {
        value: i.type,
        label: i.name,
        group: ((a = t.sessionsByType[i.parentSession]) == null ? void 0 : a.name) || "Other",
        parentSession: i.parentSession
      };
    });
    return {
      sessionOptions: r,
      macroOptions: o
    };
  }
  /**
   * Get all overlapping macro periods for a given time
   */
  static getOverlappingMacros(t) {
    const r = this.getSessionHierarchy(), o = [];
    for (const [i, a] of Object.entries(r.macrosByType))
      this.isTimeInRange(t, a.timeRange) && o.push({
        type: i,
        macro: a,
        isSubPeriod: !!a.parentMacro,
        isMultiSession: !!a.spansSessions
      });
    return o.sort((i, a) => {
      if (i.isSubPeriod && !a.isSubPeriod)
        return -1;
      if (!i.isSubPeriod && a.isSubPeriod)
        return 1;
      const s = this.timeToMinutes(i.macro.timeRange.end) - this.timeToMinutes(i.macro.timeRange.start), l = this.timeToMinutes(a.macro.timeRange.end) - this.timeToMinutes(a.macro.timeRange.start);
      return s - l;
    });
  }
  /**
   * Get multi-session macros
   */
  static getMultiSessionMacros() {
    return this.getSessionHierarchy().multiSessionMacros || [];
  }
  /**
   * Check if a macro period has sub-periods
   */
  static hasSubPeriods(t) {
    const r = this.getMacroPeriod(t);
    return !!(r != null && r.subPeriods && r.subPeriods.length > 0);
  }
  /**
   * Get sub-periods for a macro
   */
  static getSubPeriods(t) {
    const r = this.getMacroPeriod(t);
    return (r == null ? void 0 : r.subPeriods) || [];
  }
  /**
   * Convert legacy session string to new session selection
   */
  static convertLegacySession(t) {
    const o = {
      "NY Open": {
        session: Q.NEW_YORK_AM
      },
      "London Open": {
        session: Q.LONDON
      },
      "Lunch Macro": {
        macro: P.LUNCH_MACRO_EXTENDED
      },
      // Updated to use extended lunch macro
      "Lunch Macro (11:30-13:30)": {
        macro: P.LUNCH_MACRO_EXTENDED
      },
      "Lunch Macro (12:00-13:30)": {
        macro: P.LUNCH_MACRO
      },
      MOC: {
        macro: P.MOC
      },
      Overnight: {
        session: Q.OVERNIGHT
      },
      "Pre-Market": {
        session: Q.PRE_MARKET
      },
      "After Hours": {
        session: Q.AFTER_HOURS
      },
      "Power Hour": {
        macro: P.POWER_HOUR
      },
      "10:50-11:10": {
        macro: P.MID_MORNING_REVERSION
      },
      "11:50-12:10": {
        macro: P.PRE_LUNCH
      },
      "15:15-15:45": {
        macro: P.POWER_HOUR
      }
    }[t];
    return o ? this.createSessionSelection(o.session, o.macro) : null;
  }
}
Se(se, "hierarchy", null);
const bs = (e = {}) => {
  const {
    initialSelection: t,
    autoDetectCurrent: r = !1,
    filterOptions: o = {},
    onSelectionChange: i,
    validateTimes: a = !0
  } = e, [s, l] = H(t || {
    displayLabel: "No Selection",
    selectionType: "custom"
  }), d = q(() => se.getCurrentSession(), []), f = q(() => d !== null, [d]), {
    availableSessions: u,
    availableMacros: m
  } = q(() => {
    const {
      sessions: N,
      macros: _
    } = se.filterSessions(o), {
      sessionOptions: R,
      macroOptions: D
    } = se.getDisplayOptions(), K = R.filter((L) => N.some((B) => B.type === L.value)), W = D.filter((L) => _.some((B) => B.type === L.value));
    return {
      availableSessions: K,
      availableMacros: W
    };
  }, [o]), y = q(() => u.map((N) => {
    const _ = m.filter((R) => R.parentSession === N.value).map((R) => ({
      value: R.value,
      label: R.label
    }));
    return {
      session: N.value,
      sessionLabel: N.label,
      macros: _
    };
  }), [u, m]);
  ae(() => {
    r && d && !t && l(d);
  }, [r, d, t]), ae(() => {
    i == null || i(s);
  }, [s, i]);
  const x = z((N) => {
    const _ = se.createSessionSelection(N);
    l(_);
  }, []), h = z((N) => {
    const _ = se.createSessionSelection(void 0, N);
    l(_);
  }, []), b = z((N) => {
    const _ = se.createSessionSelection(void 0, void 0, N);
    l(_);
  }, []), E = z(() => {
    l({
      displayLabel: "No Selection",
      selectionType: "custom"
    });
  }, []), S = z((N) => a ? se.validateTime(N) : {
    isValid: !0
  }, [a]), C = q(() => {
    if (s.selectionType === "session" && s.session)
      return se.getSession(s.session) !== null;
    if (s.selectionType === "macro" && s.macroPeriod)
      return se.getMacroPeriod(s.macroPeriod) !== null;
    if (s.selectionType === "custom" && s.customTimeRange) {
      const N = S(s.customTimeRange.start), _ = S(s.customTimeRange.end);
      return N.isValid && _.isValid;
    }
    return s.selectionType === "custom" && !s.customTimeRange;
  }, [s, S]), k = z((N) => se.getSession(N), []), $ = z((N) => se.getMacroPeriod(N), []), j = z((N) => se.convertLegacySession(N), []);
  return {
    // State
    selection: s,
    // Selection methods
    selectSession: x,
    selectMacro: h,
    selectCustomRange: b,
    clearSelection: E,
    // Validation
    validateTime: S,
    isValidSelection: C,
    // Options
    availableSessions: u,
    availableMacros: m,
    hierarchicalOptions: y,
    // Current session
    currentSession: d,
    isCurrentSessionActive: f,
    // Utilities
    getSessionDetails: k,
    getMacroDetails: $,
    convertLegacySession: j
  };
}, vs = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-1reqqnl-0"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.sm) || "8px";
}), ws = /* @__PURE__ */ c.div.withConfig({
  displayName: "SelectorContainer",
  componentId: "sc-1reqqnl-1"
})(["position:relative;border:1px solid ", ";border-radius:", ";background:", ";transition:all 0.2s ease;opacity:", ";pointer-events:", ";&:hover{border-color:", "40;}&:focus-within{border-color:", ";box-shadow:0 0 0 3px ", "20;}"], ({
  theme: e,
  hasError: t
}) => {
  var r, o;
  return t ? ((r = e.colors) == null ? void 0 : r.error) || "#ef4444" : ((o = e.colors) == null ? void 0 : o.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.borderRadius) == null ? void 0 : t.md) || "6px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.surface) || "#1f2937";
}, ({
  disabled: e
}) => e ? 0.6 : 1, ({
  disabled: e
}) => e ? "none" : "auto", ({
  theme: e,
  hasError: t
}) => {
  var r, o;
  return t ? ((r = e.colors) == null ? void 0 : r.error) || "#ef4444" : ((o = e.colors) == null ? void 0 : o.primary) || "#dc2626";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626";
}), Ss = /* @__PURE__ */ c.div.withConfig({
  displayName: "SelectedValue",
  componentId: "sc-1reqqnl-2"
})(["padding:", ";color:", ";font-size:", ";cursor:pointer;display:flex;align-items:center;justify-content:space-between;"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.md) || "12px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.md) || "1rem";
}), Cs = /* @__PURE__ */ c.div.withConfig({
  displayName: "DropdownIcon",
  componentId: "sc-1reqqnl-3"
})(["transition:transform 0.2s ease;transform:", ";color:", ";"], ({
  isOpen: e
}) => e ? "rotate(180deg)" : "rotate(0deg)", ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textSecondary) || "#9ca3af";
}), Es = /* @__PURE__ */ c.div.withConfig({
  displayName: "DropdownMenu",
  componentId: "sc-1reqqnl-4"
})(["position:absolute;top:100%;left:0;right:0;z-index:1000;background:", ";border:1px solid ", ";border-radius:", ";box-shadow:0 10px 25px -5px rgba(0,0,0,0.3);max-height:400px;overflow-y:auto;display:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.surface) || "#1f2937";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.borderRadius) == null ? void 0 : t.md) || "6px";
}, ({
  isOpen: e
}) => e ? "block" : "none"), Ts = /* @__PURE__ */ c.div.withConfig({
  displayName: "MultiSessionGroup",
  componentId: "sc-1reqqnl-5"
})(["border-bottom:1px solid ", ";background:", ";&:last-child{border-bottom:none;}"], ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.background) || "#111827";
}), Is = /* @__PURE__ */ c.div.withConfig({
  displayName: "MultiSessionHeader",
  componentId: "sc-1reqqnl-6"
})(["padding:", ";background:", ";color:", ";font-weight:600;cursor:pointer;display:flex;align-items:center;justify-content:space-between;transition:background-color 0.2s ease;border-left:3px solid ", ";&:hover{background:", "40;}"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.md) || "12px";
}, ({
  theme: e,
  isSelected: t
}) => {
  var r, o;
  return t ? ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626" : ((o = e.colors) == null ? void 0 : o.surface) || "#1f2937";
}, ({
  theme: e,
  isSelected: t
}) => {
  var r;
  return t ? "#ffffff" : ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.warning) || "#f59e0b";
}, ({
  theme: e,
  isSelected: t
}) => {
  var r, o;
  return t ? ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626" : ((o = e.colors) == null ? void 0 : o.border) || "#4b5563";
}), Rs = /* @__PURE__ */ c.div.withConfig({
  displayName: "MultiSessionIndicator",
  componentId: "sc-1reqqnl-7"
})(["display:inline-flex;align-items:center;gap:", ";font-size:", ";color:", ";font-weight:500;"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xs) || "4px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.warning) || "#f59e0b";
}), js = /* @__PURE__ */ c.div.withConfig({
  displayName: "SessionGroup",
  componentId: "sc-1reqqnl-8"
})(["border-bottom:1px solid ", ";&:last-child{border-bottom:none;}"], ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.border) || "#4b5563";
}), Ns = /* @__PURE__ */ c.div.withConfig({
  displayName: "SessionHeader",
  componentId: "sc-1reqqnl-9"
})(["padding:", ";background:", ";color:", ";font-weight:600;cursor:pointer;display:flex;align-items:center;justify-content:space-between;transition:background-color 0.2s ease;&:hover{background:", "40;}"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.md) || "12px";
}, ({
  theme: e,
  isSelected: t
}) => {
  var r;
  return t ? ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626" : "transparent";
}, ({
  theme: e,
  isSelected: t
}) => {
  var r;
  return t ? "#ffffff" : ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  theme: e,
  isSelected: t
}) => {
  var r, o;
  return t ? ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626" : ((o = e.colors) == null ? void 0 : o.border) || "#4b5563";
}), Ds = /* @__PURE__ */ c.div.withConfig({
  displayName: "MacroList",
  componentId: "sc-1reqqnl-10"
})(["background:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.background) || "#111827";
}), Ls = /* @__PURE__ */ c.div.withConfig({
  displayName: "MacroItem",
  componentId: "sc-1reqqnl-11"
})(["padding:", " ", ";color:", ";cursor:pointer;font-size:", ";transition:all 0.2s ease;border-left:3px solid ", ";&:hover{background:", "20;color:", ";}"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.sm) || "8px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.lg) || "24px";
}, ({
  theme: e,
  isSelected: t
}) => {
  var r, o;
  return t ? ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626" : ((o = e.colors) == null ? void 0 : o.textSecondary) || "#9ca3af";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.sm) || "0.875rem";
}, ({
  theme: e,
  isSelected: t
}) => {
  var r;
  return t ? ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626" : "transparent";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textPrimary) || "#ffffff";
}), ks = /* @__PURE__ */ c.div.withConfig({
  displayName: "CurrentSessionIndicator",
  componentId: "sc-1reqqnl-12"
})(["display:inline-flex;align-items:center;gap:", ";font-size:", ";color:", ";font-weight:500;"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xs) || "4px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.success) || "#10b981";
}), _s = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-1reqqnl-13"
})(["color:", ";font-size:", ";margin-top:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.error) || "#ef4444";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xs) || "4px";
}), bl = ({
  value: e,
  onChange: t,
  showMacroPeriods: r = !0,
  showCurrentSession: o = !0,
  placeholder: i = "Select session or macro period",
  disabled: a = !1,
  error: s,
  className: l
}) => {
  const [d, f] = H(!1), {
    hierarchicalOptions: u,
    currentSession: m,
    selectSession: y,
    selectMacro: x
  } = bs({
    onSelectionChange: t
  }), h = q(() => se.getMultiSessionMacros(), []), b = q(() => e != null && e.displayLabel ? e.displayLabel : i, [e, i]), E = (j) => {
    y(j), f(!1);
  }, S = (j) => {
    x(j), f(!1);
  }, C = (j) => (e == null ? void 0 : e.session) === j && (e == null ? void 0 : e.selectionType) === "session", k = (j) => (e == null ? void 0 : e.macroPeriod) === j && (e == null ? void 0 : e.selectionType) === "macro", $ = (j) => (m == null ? void 0 : m.session) === j;
  return /* @__PURE__ */ n.jsxs(vs, { className: l, hasError: !!s, children: [
    /* @__PURE__ */ n.jsxs(ws, { hasError: !!s, disabled: a, onClick: () => !a && f(!d), children: [
      /* @__PURE__ */ n.jsxs(Ss, { children: [
        /* @__PURE__ */ n.jsx("span", { children: b }),
        /* @__PURE__ */ n.jsx(Cs, { isOpen: d, children: "▼" })
      ] }),
      /* @__PURE__ */ n.jsxs(Es, { isOpen: d, children: [
        r && h.length > 0 && /* @__PURE__ */ n.jsx(Ts, { children: h.map((j) => /* @__PURE__ */ n.jsxs(Is, { isSelected: k(j.type), onClick: (N) => {
          N.stopPropagation(), S(j.type);
        }, children: [
          /* @__PURE__ */ n.jsx("span", { children: j.name }),
          /* @__PURE__ */ n.jsx(Rs, { children: "🌐 MULTI-SESSION" })
        ] }, j.type)) }),
        u.map(({
          session: j,
          sessionLabel: N,
          macros: _
        }) => /* @__PURE__ */ n.jsxs(js, { children: [
          /* @__PURE__ */ n.jsxs(Ns, { isSelected: C(j), onClick: (R) => {
            R.stopPropagation(), E(j);
          }, children: [
            /* @__PURE__ */ n.jsx("span", { children: N }),
            o && $(j) && /* @__PURE__ */ n.jsx(ks, { children: "🔴 LIVE" })
          ] }),
          r && _.length > 0 && /* @__PURE__ */ n.jsx(Ds, { children: _.map(({
            value: R,
            label: D
          }) => /* @__PURE__ */ n.jsxs(Ls, { isSelected: k(R), onClick: (K) => {
            K.stopPropagation(), S(R);
          }, children: [
            D,
            se.hasSubPeriods(R) && /* @__PURE__ */ n.jsx("span", { style: {
              marginLeft: "8px",
              fontSize: "0.75rem",
              opacity: 0.7
            }, children: "📋 Has sub-periods" })
          ] }, R)) })
        ] }, j))
      ] })
    ] }),
    s && /* @__PURE__ */ n.jsx(_s, { children: s })
  ] });
}, I = {
  DATE: "date",
  SYMBOL: "symbol",
  DIRECTION: "direction",
  MODEL_TYPE: "model_type",
  SESSION: "session",
  ENTRY_PRICE: "entry_price",
  EXIT_PRICE: "exit_price",
  R_MULTIPLE: "r_multiple",
  ACHIEVED_PL: "achieved_pl",
  WIN_LOSS: "win_loss",
  PATTERN_QUALITY: "pattern_quality_rating",
  ENTRY_TIME: "entry_time",
  EXIT_TIME: "exit_time"
}, Rt = /* @__PURE__ */ c.span.withConfig({
  displayName: "ProfitLossCell",
  componentId: "sc-14bks31-0"
})(["color:", ";font-weight:", ";"], ({
  isProfit: e,
  theme: t
}) => e ? t.colors.success || "#10b981" : t.colors.error || "#ef4444", ({
  theme: e
}) => {
  var t;
  return ((t = e.fontWeights) == null ? void 0 : t.semibold) || 600;
}), kr = /* @__PURE__ */ c(Ze).withConfig({
  displayName: "DirectionBadge",
  componentId: "sc-14bks31-1"
})(["background-color:", ";color:white;"], ({
  direction: e,
  theme: t
}) => e === "Long" ? t.colors.success || "#10b981" : t.colors.error || "#ef4444"), _r = /* @__PURE__ */ c.span.withConfig({
  displayName: "QualityRating",
  componentId: "sc-14bks31-2"
})(["color:", ";font-weight:", ";"], ({
  rating: e,
  theme: t
}) => e >= 4 ? t.colors.success || "#10b981" : e >= 3 ? t.colors.warning || "#f59e0b" : t.colors.error || "#ef4444", ({
  theme: e
}) => {
  var t;
  return ((t = e.fontWeights) == null ? void 0 : t.semibold) || 600;
}), jt = /* @__PURE__ */ c.span.withConfig({
  displayName: "RMultipleCell",
  componentId: "sc-14bks31-3"
})(["color:", ";font-weight:", ";"], ({
  rMultiple: e,
  theme: t
}) => e > 0 ? t.colors.success || "#10b981" : t.colors.error || "#ef4444", ({
  theme: e
}) => {
  var t;
  return ((t = e.fontWeights) == null ? void 0 : t.semibold) || 600;
}), Ae = (e) => e == null ? "-" : new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  minimumFractionDigits: 2
}).format(e), Nt = (e) => {
  try {
    return new Date(e).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  } catch {
    return e;
  }
}, dr = (e) => e || "-", Ms = () => [{
  id: I.DATE,
  header: "Date",
  sortable: !0,
  width: "100px",
  cell: (e) => Nt(e.trade[I.DATE])
}, {
  id: I.SYMBOL,
  header: "Symbol",
  sortable: !0,
  width: "80px",
  cell: (e) => e.trade.market || "MNQ"
}, {
  id: I.DIRECTION,
  header: "Direction",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ n.jsx(kr, { direction: e.trade[I.DIRECTION], size: "small", children: e.trade[I.DIRECTION] })
}, {
  id: I.MODEL_TYPE,
  header: "Model",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[I.MODEL_TYPE] || "-"
}, {
  id: I.SESSION,
  header: "Session",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[I.SESSION] || "-"
}, {
  id: I.ENTRY_PRICE,
  header: "Entry",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => Ae(e.trade[I.ENTRY_PRICE])
}, {
  id: I.EXIT_PRICE,
  header: "Exit",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => Ae(e.trade[I.EXIT_PRICE])
}, {
  id: I.R_MULTIPLE,
  header: "R Multiple",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => {
    var t;
    return /* @__PURE__ */ n.jsx(jt, { rMultiple: e.trade[I.R_MULTIPLE] || 0, children: e.trade[I.R_MULTIPLE] ? `${(t = e.trade[I.R_MULTIPLE]) == null ? void 0 : t.toFixed(2)}R` : "-" });
  }
}, {
  id: I.ACHIEVED_PL,
  header: "P&L",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => /* @__PURE__ */ n.jsx(Rt, { isProfit: (e.trade[I.ACHIEVED_PL] || 0) > 0, children: Ae(e.trade[I.ACHIEVED_PL]) })
}, {
  id: I.WIN_LOSS,
  header: "Result",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ n.jsx(Ze, { variant: e.trade[I.WIN_LOSS] === "Win" ? "success" : "error", size: "small", children: e.trade[I.WIN_LOSS] || "-" })
}, {
  id: I.PATTERN_QUALITY,
  header: "Quality",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ n.jsx(_r, { rating: e.trade[I.PATTERN_QUALITY] || 0, children: e.trade[I.PATTERN_QUALITY] ? `${e.trade[I.PATTERN_QUALITY]}/5` : "-" })
}, {
  id: I.ENTRY_TIME,
  header: "Entry Time",
  sortable: !0,
  width: "100px",
  align: "center",
  cell: (e) => dr(e.trade[I.ENTRY_TIME])
}, {
  id: I.EXIT_TIME,
  header: "Exit Time",
  sortable: !0,
  width: "100px",
  align: "center",
  cell: (e) => dr(e.trade[I.EXIT_TIME])
}], Ps = () => [{
  id: I.DATE,
  header: "Date",
  sortable: !0,
  width: "90px",
  cell: (e) => Nt(e.trade[I.DATE])
}, {
  id: I.SYMBOL,
  header: "Symbol",
  sortable: !0,
  width: "60px",
  cell: (e) => e.trade.market || "MNQ"
}, {
  id: I.DIRECTION,
  header: "Dir",
  sortable: !0,
  width: "50px",
  align: "center",
  cell: (e) => /* @__PURE__ */ n.jsx(kr, { direction: e.trade[I.DIRECTION], size: "small", children: e.trade[I.DIRECTION].charAt(0) })
}, {
  id: I.R_MULTIPLE,
  header: "R",
  sortable: !0,
  width: "60px",
  align: "right",
  cell: (e) => {
    var t;
    return /* @__PURE__ */ n.jsx(jt, { rMultiple: e.trade[I.R_MULTIPLE] || 0, children: e.trade[I.R_MULTIPLE] ? `${(t = e.trade[I.R_MULTIPLE]) == null ? void 0 : t.toFixed(1)}R` : "-" });
  }
}, {
  id: I.ACHIEVED_PL,
  header: "P&L",
  sortable: !0,
  width: "80px",
  align: "right",
  cell: (e) => /* @__PURE__ */ n.jsx(Rt, { isProfit: (e.trade[I.ACHIEVED_PL] || 0) > 0, children: Ae(e.trade[I.ACHIEVED_PL]) })
}, {
  id: I.WIN_LOSS,
  header: "Result",
  sortable: !0,
  width: "60px",
  align: "center",
  cell: (e) => /* @__PURE__ */ n.jsx(Ze, { variant: e.trade[I.WIN_LOSS] === "Win" ? "success" : "error", size: "small", children: e.trade[I.WIN_LOSS] === "Win" ? "W" : e.trade[I.WIN_LOSS] === "Loss" ? "L" : "-" })
}], Os = () => [{
  id: I.DATE,
  header: "Date",
  sortable: !0,
  width: "100px",
  cell: (e) => Nt(e.trade[I.DATE])
}, {
  id: I.MODEL_TYPE,
  header: "Model",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[I.MODEL_TYPE] || "-"
}, {
  id: I.SESSION,
  header: "Session",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[I.SESSION] || "-"
}, {
  id: I.R_MULTIPLE,
  header: "R Multiple",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => {
    var t;
    return /* @__PURE__ */ n.jsx(jt, { rMultiple: e.trade[I.R_MULTIPLE] || 0, children: e.trade[I.R_MULTIPLE] ? `${(t = e.trade[I.R_MULTIPLE]) == null ? void 0 : t.toFixed(2)}R` : "-" });
  }
}, {
  id: I.ACHIEVED_PL,
  header: "P&L",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => /* @__PURE__ */ n.jsx(Rt, { isProfit: (e.trade[I.ACHIEVED_PL] || 0) > 0, children: Ae(e.trade[I.ACHIEVED_PL]) })
}, {
  id: I.PATTERN_QUALITY,
  header: "Quality",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ n.jsx(_r, { rating: e.trade[I.PATTERN_QUALITY] || 0, children: e.trade[I.PATTERN_QUALITY] ? `${e.trade[I.PATTERN_QUALITY]}/5` : "-" })
}, {
  id: I.WIN_LOSS,
  header: "Result",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ n.jsx(Ze, { variant: e.trade[I.WIN_LOSS] === "Win" ? "success" : "error", size: "small", children: e.trade[I.WIN_LOSS] || "-" })
}], Fs = /* @__PURE__ */ c.tr.withConfig({
  displayName: "TableRow",
  componentId: "sc-uyrnn-0"
})(["", " ", " ", " ", " ", ""], ({
  striped: e,
  theme: t,
  isSelected: r
}) => {
  var o;
  return e && !r && g(["&:nth-child(even){background-color:", "50;}"], ((o = t.colors) == null ? void 0 : o.background) || "#f8f9fa");
}, ({
  hoverable: e,
  theme: t,
  isSelected: r
}) => {
  var o;
  return e && !r && g(["&:hover{background-color:", "aa;}"], ((o = t.colors) == null ? void 0 : o.background) || "#f8f9fa");
}, ({
  isSelected: e,
  theme: t
}) => {
  var r;
  return e && g(["background-color:", "15;"], ((r = t.colors) == null ? void 0 : r.primary) || "#3b82f6");
}, ({
  isClickable: e
}) => e && g(["cursor:pointer;"]), ({
  isExpanded: e,
  theme: t
}) => {
  var r;
  return e && g(["border-bottom:2px solid ", ";"], ((r = t.colors) == null ? void 0 : r.primary) || "#3b82f6");
}), ur = /* @__PURE__ */ c.td.withConfig({
  displayName: "TableCell",
  componentId: "sc-uyrnn-1"
})(["text-align:", ";border-bottom:1px solid ", ";color:", ";padding:", " ", ";vertical-align:middle;"], ({
  align: e
}) => e || "left", ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.border) || "#e5e7eb";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textPrimary) || "#111827";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.sm) || "12px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.md) || "16px";
}), $s = /* @__PURE__ */ c.tr.withConfig({
  displayName: "ExpandedRow",
  componentId: "sc-uyrnn-2"
})(["display:", ";"], ({
  isVisible: e
}) => e ? "table-row" : "none"), As = /* @__PURE__ */ c.td.withConfig({
  displayName: "ExpandedCell",
  componentId: "sc-uyrnn-3"
})(["padding:0;border-bottom:1px solid ", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.border) || "#e5e7eb";
}), zs = /* @__PURE__ */ c.div.withConfig({
  displayName: "ExpandedContent",
  componentId: "sc-uyrnn-4"
})(["padding:", ";background-color:", "30;border-left:3px solid ", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.md) || "16px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.primary) || "#3b82f6";
}), Vs = /* @__PURE__ */ c.button.withConfig({
  displayName: "ExpandButton",
  componentId: "sc-uyrnn-5"
})(["background:none;border:none;cursor:pointer;padding:", ";color:", ";font-size:", ";display:flex;align-items:center;justify-content:center;border-radius:", ";transition:all 0.2s ease;&:hover{background-color:", ";color:", ";}&:focus{outline:2px solid ", ";outline-offset:2px;}"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xs) || "8px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textSecondary) || "#6b7280";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.sm) || "14px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.borderRadius) == null ? void 0 : t.sm) || "4px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.primary) || "#3b82f6";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.primary) || "#3b82f6";
}), Gs = /* @__PURE__ */ c.span.withConfig({
  displayName: "ExpandIcon",
  componentId: "sc-uyrnn-6"
})(["display:inline-block;transition:transform 0.2s ease;transform:", ";&::after{content:'▶';}"], ({
  isExpanded: e
}) => e ? "rotate(90deg)" : "rotate(0deg)"), Bs = /* @__PURE__ */ c.div.withConfig({
  displayName: "TradeDetails",
  componentId: "sc-uyrnn-7"
})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.md) || "16px";
}), Oe = /* @__PURE__ */ c.div.withConfig({
  displayName: "DetailGroup",
  componentId: "sc-uyrnn-8"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xs) || "8px";
}), Fe = /* @__PURE__ */ c.span.withConfig({
  displayName: "DetailLabel",
  componentId: "sc-uyrnn-9"
})(["font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.sm) || "14px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.fontWeights) == null ? void 0 : t.medium) || 500;
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textSecondary) || "#6b7280";
}), ce = /* @__PURE__ */ c.span.withConfig({
  displayName: "DetailValue",
  componentId: "sc-uyrnn-10"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.sm) || "14px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textPrimary) || "#111827";
}), qs = ({
  trade: e
}) => /* @__PURE__ */ n.jsxs(Bs, { children: [
  e.fvg_details && /* @__PURE__ */ n.jsxs(Oe, { children: [
    /* @__PURE__ */ n.jsx(Fe, { children: "FVG Details" }),
    /* @__PURE__ */ n.jsxs(ce, { children: [
      "Type: ",
      e.fvg_details.rd_type || "-"
    ] }),
    /* @__PURE__ */ n.jsxs(ce, { children: [
      "Entry Version: ",
      e.fvg_details.entry_version || "-"
    ] }),
    /* @__PURE__ */ n.jsxs(ce, { children: [
      "Draw on Liquidity: ",
      e.fvg_details.draw_on_liquidity || "-"
    ] })
  ] }),
  e.setup && /* @__PURE__ */ n.jsxs(Oe, { children: [
    /* @__PURE__ */ n.jsx(Fe, { children: "Setup Classification" }),
    /* @__PURE__ */ n.jsxs(ce, { children: [
      "Primary: ",
      e.setup.primary_setup || "-"
    ] }),
    /* @__PURE__ */ n.jsxs(ce, { children: [
      "Secondary: ",
      e.setup.secondary_setup || "-"
    ] }),
    /* @__PURE__ */ n.jsxs(ce, { children: [
      "Liquidity: ",
      e.setup.liquidity_taken || "-"
    ] })
  ] }),
  e.analysis && /* @__PURE__ */ n.jsxs(Oe, { children: [
    /* @__PURE__ */ n.jsx(Fe, { children: "Analysis" }),
    /* @__PURE__ */ n.jsxs(ce, { children: [
      "DOL Target: ",
      e.analysis.dol_target_type || "-"
    ] }),
    /* @__PURE__ */ n.jsxs(ce, { children: [
      "Path Quality: ",
      e.analysis.path_quality || "-"
    ] }),
    /* @__PURE__ */ n.jsxs(ce, { children: [
      "Clustering: ",
      e.analysis.clustering || "-"
    ] })
  ] }),
  /* @__PURE__ */ n.jsxs(Oe, { children: [
    /* @__PURE__ */ n.jsx(Fe, { children: "Timing" }),
    /* @__PURE__ */ n.jsxs(ce, { children: [
      "Entry: ",
      e.trade.entry_time || "-"
    ] }),
    /* @__PURE__ */ n.jsxs(ce, { children: [
      "Exit: ",
      e.trade.exit_time || "-"
    ] }),
    /* @__PURE__ */ n.jsxs(ce, { children: [
      "FVG: ",
      e.trade.fvg_time || "-"
    ] }),
    /* @__PURE__ */ n.jsxs(ce, { children: [
      "RD: ",
      e.trade.rd_time || "-"
    ] })
  ] }),
  e.trade.notes && /* @__PURE__ */ n.jsxs(Oe, { style: {
    gridColumn: "1 / -1"
  }, children: [
    /* @__PURE__ */ n.jsx(Fe, { children: "Notes" }),
    /* @__PURE__ */ n.jsx(ce, { children: e.trade.notes })
  ] })
] }), Hs = ({
  trade: e,
  index: t,
  columns: r,
  isSelected: o = !1,
  hoverable: i = !0,
  striped: a = !0,
  expandable: s = !1,
  isExpanded: l = !1,
  onRowClick: d,
  onToggleExpand: f,
  expandedContent: u
}) => {
  const [m, y] = H(!1), x = l !== void 0 ? l : m, h = (S) => {
    S.target.closest("button") || d == null || d(e, t);
  }, b = (S) => {
    S.stopPropagation(), f ? f(e, t) : y(!m);
  }, E = r.filter((S) => !S.hidden);
  return /* @__PURE__ */ n.jsxs(n.Fragment, { children: [
    /* @__PURE__ */ n.jsxs(Fs, { hoverable: i, striped: a, isSelected: o, isClickable: !!d, isExpanded: x, onClick: h, children: [
      s && /* @__PURE__ */ n.jsx(ur, { align: "center", style: {
        width: "40px",
        padding: "8px"
      }, children: /* @__PURE__ */ n.jsx(Vs, { onClick: b, children: /* @__PURE__ */ n.jsx(Gs, { isExpanded: x }) }) }),
      E.map((S) => /* @__PURE__ */ n.jsx(ur, { align: S.align, children: S.cell(e, t) }, S.id))
    ] }),
    s && /* @__PURE__ */ n.jsx($s, { isVisible: x, children: /* @__PURE__ */ n.jsx(As, { colSpan: E.length + 1, children: /* @__PURE__ */ n.jsx(zs, { children: u || /* @__PURE__ */ n.jsx(qs, { trade: e }) }) }) })
  ] });
}, ge = {
  MODEL_TYPE: "model_type",
  WIN_LOSS: "win_loss",
  DATE_FROM: "dateFrom",
  DATE_TO: "dateTo",
  SESSION: "session",
  DIRECTION: "direction",
  MARKET: "market",
  MIN_R_MULTIPLE: "min_r_multiple",
  MAX_R_MULTIPLE: "max_r_multiple",
  MIN_PATTERN_QUALITY: "min_pattern_quality",
  MAX_PATTERN_QUALITY: "max_pattern_quality"
}, Us = /* @__PURE__ */ c.div.withConfig({
  displayName: "FiltersContainer",
  componentId: "sc-32k3gq-0"
})(["display:flex;flex-direction:column;gap:", ";padding:", ";background-color:", ";border-radius:", ";border:1px solid ", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.md) || "16px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.md) || "16px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.borderRadius) == null ? void 0 : t.md) || "8px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.border) || "#e5e7eb";
}), pr = /* @__PURE__ */ c.div.withConfig({
  displayName: "FilterRow",
  componentId: "sc-32k3gq-1"
})(["display:flex;gap:", ";align-items:end;flex-wrap:wrap;@media (max-width:768px){flex-direction:column;align-items:stretch;}"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.sm) || "12px";
}), xe = /* @__PURE__ */ c.div.withConfig({
  displayName: "FilterGroup",
  componentId: "sc-32k3gq-2"
})(["display:flex;flex-direction:column;gap:", ";min-width:120px;"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xs) || "8px";
}), ye = /* @__PURE__ */ c.label.withConfig({
  displayName: "FilterLabel",
  componentId: "sc-32k3gq-3"
})(["font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.sm) || "14px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.fontWeights) == null ? void 0 : t.medium) || 500;
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textSecondary) || "#6b7280";
}), Ys = /* @__PURE__ */ c.div.withConfig({
  displayName: "FilterActions",
  componentId: "sc-32k3gq-4"
})(["display:flex;gap:", ";align-items:center;margin-left:auto;@media (max-width:768px){margin-left:0;justify-content:flex-end;}"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.sm) || "12px";
}), Ws = /* @__PURE__ */ c.div.withConfig({
  displayName: "AdvancedFilters",
  componentId: "sc-32k3gq-5"
})(["display:", ";flex-direction:column;gap:", ";padding-top:", ";border-top:1px solid ", ";"], ({
  isVisible: e
}) => e ? "flex" : "none", ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.md) || "16px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.md) || "16px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.border) || "#e5e7eb";
}), fr = /* @__PURE__ */ c.div.withConfig({
  displayName: "RangeInputGroup",
  componentId: "sc-32k3gq-6"
})(["display:flex;gap:", ";align-items:center;"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xs) || "8px";
}), gr = /* @__PURE__ */ c.span.withConfig({
  displayName: "RangeLabel",
  componentId: "sc-32k3gq-7"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.sm) || "14px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textSecondary) || "#6b7280";
}), Ks = ({
  filters: e,
  onFiltersChange: t,
  onReset: r,
  isLoading: o = !1,
  showAdvanced: i = !1,
  onToggleAdvanced: a
}) => {
  var f, u, m, y;
  const s = (x, h) => {
    t({
      ...e,
      [x]: h
    });
  }, l = () => {
    t({}), r == null || r();
  }, d = Object.values(e).some((x) => x !== void 0 && x !== "" && x !== null);
  return /* @__PURE__ */ n.jsxs(Us, { children: [
    /* @__PURE__ */ n.jsxs(pr, { children: [
      /* @__PURE__ */ n.jsxs(xe, { children: [
        /* @__PURE__ */ n.jsx(ye, { children: "Date From" }),
        /* @__PURE__ */ n.jsx(Ie, { type: "date", value: e.dateFrom || "", onChange: (x) => s(ge.DATE_FROM, x), disabled: o })
      ] }),
      /* @__PURE__ */ n.jsxs(xe, { children: [
        /* @__PURE__ */ n.jsx(ye, { children: "Date To" }),
        /* @__PURE__ */ n.jsx(Ie, { type: "date", value: e.dateTo || "", onChange: (x) => s(ge.DATE_TO, x), disabled: o })
      ] }),
      /* @__PURE__ */ n.jsxs(xe, { children: [
        /* @__PURE__ */ n.jsx(ye, { children: "Model Type" }),
        /* @__PURE__ */ n.jsx(Pe, { options: [{
          value: "",
          label: "All Models"
        }, {
          value: "RD-Cont",
          label: "RD-Cont"
        }, {
          value: "FVG-RD",
          label: "FVG-RD"
        }, {
          value: "True-RD",
          label: "True-RD"
        }, {
          value: "IMM-RD",
          label: "IMM-RD"
        }, {
          value: "Dispersed-RD",
          label: "Dispersed-RD"
        }, {
          value: "Wide-Gap-RD",
          label: "Wide-Gap-RD"
        }], value: e.model_type || "", onChange: (x) => s(ge.MODEL_TYPE, x), disabled: o })
      ] }),
      /* @__PURE__ */ n.jsxs(xe, { children: [
        /* @__PURE__ */ n.jsx(ye, { children: "Session" }),
        /* @__PURE__ */ n.jsx(Pe, { options: [{
          value: "",
          label: "All Sessions"
        }, {
          value: "Pre-Market",
          label: "Pre-Market"
        }, {
          value: "NY Open",
          label: "NY Open"
        }, {
          value: "10:50-11:10",
          label: "10:50-11:10"
        }, {
          value: "11:50-12:10",
          label: "11:50-12:10"
        }, {
          value: "Lunch Macro",
          label: "Lunch Macro"
        }, {
          value: "13:50-14:10",
          label: "13:50-14:10"
        }, {
          value: "14:50-15:10",
          label: "14:50-15:10"
        }, {
          value: "15:15-15:45",
          label: "15:15-15:45"
        }, {
          value: "MOC",
          label: "MOC"
        }, {
          value: "Post MOC",
          label: "Post MOC"
        }], value: e.session || "", onChange: (x) => s(ge.SESSION, x), disabled: o })
      ] }),
      /* @__PURE__ */ n.jsxs(xe, { children: [
        /* @__PURE__ */ n.jsx(ye, { children: "Direction" }),
        /* @__PURE__ */ n.jsx(Pe, { options: [{
          value: "",
          label: "All Directions"
        }, {
          value: "Long",
          label: "Long"
        }, {
          value: "Short",
          label: "Short"
        }], value: e.direction || "", onChange: (x) => s(ge.DIRECTION, x), disabled: o })
      ] }),
      /* @__PURE__ */ n.jsxs(xe, { children: [
        /* @__PURE__ */ n.jsx(ye, { children: "Result" }),
        /* @__PURE__ */ n.jsx(Pe, { options: [{
          value: "",
          label: "All Results"
        }, {
          value: "Win",
          label: "Win"
        }, {
          value: "Loss",
          label: "Loss"
        }], value: e.win_loss || "", onChange: (x) => s(ge.WIN_LOSS, x), disabled: o })
      ] }),
      /* @__PURE__ */ n.jsxs(Ys, { children: [
        a && /* @__PURE__ */ n.jsxs(le, { variant: "outline", size: "small", onClick: a, disabled: o, children: [
          i ? "Hide" : "Show",
          " Advanced"
        ] }),
        /* @__PURE__ */ n.jsx(le, { variant: "outline", size: "small", onClick: l, disabled: o || !d, children: "Reset" })
      ] })
    ] }),
    /* @__PURE__ */ n.jsx(Ws, { isVisible: i, children: /* @__PURE__ */ n.jsxs(pr, { children: [
      /* @__PURE__ */ n.jsxs(xe, { children: [
        /* @__PURE__ */ n.jsx(ye, { children: "Market" }),
        /* @__PURE__ */ n.jsx(Pe, { options: [{
          value: "",
          label: "All Markets"
        }, {
          value: "MNQ",
          label: "MNQ"
        }, {
          value: "NQ",
          label: "NQ"
        }, {
          value: "ES",
          label: "ES"
        }, {
          value: "MES",
          label: "MES"
        }, {
          value: "YM",
          label: "YM"
        }, {
          value: "MYM",
          label: "MYM"
        }], value: e.market || "", onChange: (x) => s(ge.MARKET, x), disabled: o })
      ] }),
      /* @__PURE__ */ n.jsxs(xe, { children: [
        /* @__PURE__ */ n.jsx(ye, { children: "R Multiple Range" }),
        /* @__PURE__ */ n.jsxs(fr, { children: [
          /* @__PURE__ */ n.jsx(Ie, { type: "number", placeholder: "Min", step: "0.1", value: ((f = e.min_r_multiple) == null ? void 0 : f.toString()) || "", onChange: (x) => s(ge.MIN_R_MULTIPLE, x ? Number(x) : void 0), disabled: o, style: {
            width: "80px"
          } }),
          /* @__PURE__ */ n.jsx(gr, { children: "to" }),
          /* @__PURE__ */ n.jsx(Ie, { type: "number", placeholder: "Max", step: "0.1", value: ((u = e.max_r_multiple) == null ? void 0 : u.toString()) || "", onChange: (x) => s(ge.MAX_R_MULTIPLE, x ? Number(x) : void 0), disabled: o, style: {
            width: "80px"
          } })
        ] })
      ] }),
      /* @__PURE__ */ n.jsxs(xe, { children: [
        /* @__PURE__ */ n.jsx(ye, { children: "Pattern Quality Range" }),
        /* @__PURE__ */ n.jsxs(fr, { children: [
          /* @__PURE__ */ n.jsx(Ie, { type: "number", placeholder: "Min", min: "1", max: "5", step: "0.1", value: ((m = e.min_pattern_quality) == null ? void 0 : m.toString()) || "", onChange: (x) => s(ge.MIN_PATTERN_QUALITY, x ? Number(x) : void 0), disabled: o, style: {
            width: "80px"
          } }),
          /* @__PURE__ */ n.jsx(gr, { children: "to" }),
          /* @__PURE__ */ n.jsx(Ie, { type: "number", placeholder: "Max", min: "1", max: "5", step: "0.1", value: ((y = e.max_pattern_quality) == null ? void 0 : y.toString()) || "", onChange: (x) => s(ge.MAX_PATTERN_QUALITY, x ? Number(x) : void 0), disabled: o, style: {
            width: "80px"
          } })
        ] })
      ] })
    ] }) })
  ] });
}, Qs = /* @__PURE__ */ c.div.withConfig({
  displayName: "TableContainer",
  componentId: "sc-13oxwmo-0"
})(["width:100%;overflow:auto;", " ", ""], ({
  height: e
}) => e && `height: ${e};`, ({
  scrollable: e
}) => e && "overflow-x: auto;"), Xs = /* @__PURE__ */ c.table.withConfig({
  displayName: "StyledTable",
  componentId: "sc-13oxwmo-1"
})(["width:100%;border-collapse:separate;border-spacing:0;font-size:", ";", " ", ""], ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.sm) || "14px";
}, ({
  bordered: e,
  theme: t
}) => {
  var r, o;
  return e && g(["border:1px solid ", ";border-radius:", ";"], ((r = t.colors) == null ? void 0 : r.border) || "#e5e7eb", ((o = t.borderRadius) == null ? void 0 : o.sm) || "4px");
}, ({
  compact: e,
  theme: t
}) => {
  var r, o, i, a;
  return e ? g(["th,td{padding:", " ", ";}"], ((r = t.spacing) == null ? void 0 : r.xs) || "8px", ((o = t.spacing) == null ? void 0 : o.sm) || "12px") : g(["th,td{padding:", " ", ";}"], ((i = t.spacing) == null ? void 0 : i.sm) || "12px", ((a = t.spacing) == null ? void 0 : a.md) || "16px");
}), Js = /* @__PURE__ */ c.thead.withConfig({
  displayName: "TableHeader",
  componentId: "sc-13oxwmo-2"
})(["", ""], ({
  stickyHeader: e
}) => e && g(["position:sticky;top:0;z-index:1;"])), Zs = /* @__PURE__ */ c.tr.withConfig({
  displayName: "TableHeaderRow",
  componentId: "sc-13oxwmo-3"
})(["background-color:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.background) || "#f8f9fa";
}), mr = /* @__PURE__ */ c.th.withConfig({
  displayName: "TableHeaderCell",
  componentId: "sc-13oxwmo-4"
})(["text-align:", ";font-weight:", ";color:", ";border-bottom:1px solid ", ";white-space:nowrap;", " ", " ", ""], ({
  align: e
}) => e || "left", ({
  theme: e
}) => {
  var t;
  return ((t = e.fontWeights) == null ? void 0 : t.semibold) || 600;
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textSecondary) || "#6b7280";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.border) || "#e5e7eb";
}, ({
  width: e
}) => e && `width: ${e};`, ({
  sortable: e
}) => e && g(["cursor:pointer;user-select:none;&:hover{background-color:", "aa;}"], ({
  theme: t
}) => {
  var r;
  return ((r = t.colors) == null ? void 0 : r.background) || "#f8f9fa";
}), ({
  isSorted: e,
  theme: t
}) => {
  var r;
  return e && g(["color:", ";"], ((r = t.colors) == null ? void 0 : r.primary) || "#3b82f6");
}), ea = /* @__PURE__ */ c.span.withConfig({
  displayName: "SortIcon",
  componentId: "sc-13oxwmo-5"
})(["display:inline-block;margin-left:", ";&::after{content:'", "';}"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xs) || "8px";
}, ({
  direction: e
}) => e === "asc" ? "↑" : e === "desc" ? "↓" : "↕"), ta = /* @__PURE__ */ c.tbody.withConfig({
  displayName: "TableBody",
  componentId: "sc-13oxwmo-6"
})([""]), ra = /* @__PURE__ */ c.div.withConfig({
  displayName: "EmptyState",
  componentId: "sc-13oxwmo-7"
})(["padding:", ";text-align:center;color:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xl) || "32px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textSecondary) || "#6b7280";
}), oa = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-13oxwmo-8"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:", ";display:flex;align-items:center;justify-content:center;z-index:1;"], ({
  theme: e
}) => {
  var t;
  return `${((t = e.colors) == null ? void 0 : t.background) || "#ffffff"}80`;
}), na = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-13oxwmo-9"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"], ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.primary) || "#3b82f6";
}), ia = /* @__PURE__ */ c.div.withConfig({
  displayName: "PaginationContainer",
  componentId: "sc-13oxwmo-10"
})(["display:flex;justify-content:space-between;align-items:center;padding:", " 0;font-size:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.md) || "16px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.sm) || "14px";
}), sa = /* @__PURE__ */ c.div.withConfig({
  displayName: "PageInfo",
  componentId: "sc-13oxwmo-11"
})(["color:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textSecondary) || "#6b7280";
}), aa = /* @__PURE__ */ c.div.withConfig({
  displayName: "PaginationControls",
  componentId: "sc-13oxwmo-12"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xs) || "8px";
}), vl = ({
  data: e,
  isLoading: t = !1,
  bordered: r = !0,
  striped: o = !0,
  hoverable: i = !0,
  compact: a = !1,
  stickyHeader: s = !1,
  height: l = "",
  onRowClick: d,
  isRowSelected: f,
  onSort: u,
  sortColumn: m = "",
  sortDirection: y = "asc",
  pagination: x = !1,
  currentPage: h = 1,
  pageSize: b = 10,
  totalRows: E = 0,
  onPageChange: S,
  onPageSizeChange: C,
  className: k = "",
  emptyMessage: $ = "No trades available",
  scrollable: j = !0,
  showFilters: N = !1,
  filters: _ = {},
  onFiltersChange: R,
  columnPreset: D = "default",
  customColumns: K,
  expandableRows: W = !1,
  renderExpandedContent: L
}) => {
  const [B, te] = H(!1), re = q(() => {
    if (K)
      return K;
    switch (D) {
      case "compact":
        return Ps();
      case "performance":
        return Os();
      default:
        return Ms();
    }
  }, [K, D]), fe = q(() => re.filter((G) => !G.hidden), [re]), oe = q(() => Math.ceil(E / b), [E, b]), O = q(() => {
    if (!x)
      return e;
    const G = (h - 1) * b, me = G + b;
    return E > 0 && e.length <= b ? e : e.slice(G, me);
  }, [e, x, h, b, E]), J = (G) => {
    if (!u)
      return;
    u(G, m === G && y === "asc" ? "desc" : "asc");
  }, we = (G) => {
    G < 1 || G > oe || !S || S(G);
  };
  return /* @__PURE__ */ n.jsxs("div", { children: [
    N && R && /* @__PURE__ */ n.jsx(Ks, { filters: _, onFiltersChange: R, isLoading: t, showAdvanced: B, onToggleAdvanced: () => te(!B) }),
    /* @__PURE__ */ n.jsxs("div", { style: {
      position: "relative"
    }, children: [
      t && /* @__PURE__ */ n.jsx(oa, { children: /* @__PURE__ */ n.jsx(na, {}) }),
      /* @__PURE__ */ n.jsx(Qs, { height: l, scrollable: j, children: /* @__PURE__ */ n.jsxs(Xs, { bordered: r, striped: o, compact: a, className: k, children: [
        /* @__PURE__ */ n.jsx(Js, { stickyHeader: s, children: /* @__PURE__ */ n.jsxs(Zs, { children: [
          W && /* @__PURE__ */ n.jsx(mr, { width: "40px", align: "center" }),
          fe.map((G) => /* @__PURE__ */ n.jsxs(mr, { sortable: G.sortable, isSorted: m === G.id, align: G.align, width: G.width, onClick: () => G.sortable && J(G.id), children: [
            G.header,
            G.sortable && /* @__PURE__ */ n.jsx(ea, { direction: m === G.id ? y : void 0 })
          ] }, G.id))
        ] }) }),
        /* @__PURE__ */ n.jsx(ta, { children: O.length > 0 ? O.map((G, me) => /* @__PURE__ */ n.jsx(Hs, { trade: G, index: me, columns: fe, isSelected: f ? f(G, me) : !1, hoverable: i, striped: o, expandable: W, onRowClick: d, expandedContent: L == null ? void 0 : L(G) }, G.trade.id || me)) : /* @__PURE__ */ n.jsx("tr", { children: /* @__PURE__ */ n.jsx("td", { colSpan: fe.length + (W ? 1 : 0), children: /* @__PURE__ */ n.jsx(ra, { children: $ }) }) }) })
      ] }) }),
      x && oe > 0 && /* @__PURE__ */ n.jsxs(ia, { children: [
        /* @__PURE__ */ n.jsxs(sa, { children: [
          "Showing ",
          Math.min((h - 1) * b + 1, E),
          " to",
          " ",
          Math.min(h * b, E),
          " of ",
          E,
          " entries"
        ] }),
        /* @__PURE__ */ n.jsxs(aa, { children: [
          /* @__PURE__ */ n.jsx(le, { size: "small", variant: "outline", onClick: () => we(1), disabled: h === 1, children: "First" }),
          /* @__PURE__ */ n.jsx(le, { size: "small", variant: "outline", onClick: () => we(h - 1), disabled: h === 1, children: "Prev" }),
          /* @__PURE__ */ n.jsx(le, { size: "small", variant: "outline", onClick: () => we(h + 1), disabled: h === oe, children: "Next" }),
          /* @__PURE__ */ n.jsx(le, { size: "small", variant: "outline", onClick: () => we(oe), disabled: h === oe, children: "Last" })
        ] })
      ] })
    ] })
  ] });
}, ca = /* @__PURE__ */ c.div.withConfig({
  displayName: "HeaderActions",
  componentId: "sc-1l7c7gv-0"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.sm), wl = ({
  title: e,
  children: t,
  isLoading: r = !1,
  hasError: o = !1,
  errorMessage: i = "An error occurred while loading data",
  showRetry: a = !0,
  onRetry: s,
  isEmpty: l = !1,
  emptyMessage: d = "No data available",
  emptyActionText: f,
  onEmptyAction: u,
  actionButton: m,
  className: y,
  ...x
}) => {
  const h = /* @__PURE__ */ n.jsx(ca, { children: m });
  let b;
  return r ? b = /* @__PURE__ */ n.jsx(nn, { variant: "card", text: "Loading data..." }) : o ? b = /* @__PURE__ */ n.jsx(or, { title: "Error", description: i, variant: "compact", actionText: a ? "Retry" : void 0, onAction: a ? s : void 0 }) : l ? b = /* @__PURE__ */ n.jsx(or, { title: "No Data", description: d, variant: "compact", actionText: f, onAction: u }) : b = t, /* @__PURE__ */ n.jsx(ii, { title: e, actions: h, className: y, ...x, children: b });
}, la = /* @__PURE__ */ c.div.withConfig({
  displayName: "SectionContainer",
  componentId: "sc-14y246p-0"
})(["background:", ";border:1px solid ", ";border-radius:", ";padding:", ";margin-bottom:", ";"], ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.spacing.lg, ({
  theme: e
}) => e.spacing.lg), da = /* @__PURE__ */ c.div.withConfig({
  displayName: "SectionHeader",
  componentId: "sc-14y246p-1"
})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:", ";padding-bottom:", ";border-bottom:1px solid ", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.colors.border), ua = /* @__PURE__ */ c.h2.withConfig({
  displayName: "SectionTitle",
  componentId: "sc-14y246p-2"
})(["color:", ";font-size:", ";font-weight:600;margin:0;"], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.fontSizes.lg), pa = /* @__PURE__ */ c.div.withConfig({
  displayName: "SectionActions",
  componentId: "sc-14y246p-3"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.sm), fa = /* @__PURE__ */ c.div.withConfig({
  displayName: "SectionContent",
  componentId: "sc-14y246p-4"
})(["min-height:200px;"]), hr = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingState",
  componentId: "sc-14y246p-5"
})(["display:flex;align-items:center;justify-content:center;min-height:200px;color:", ";"], ({
  theme: e
}) => e.colors.textSecondary), ga = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorState",
  componentId: "sc-14y246p-6"
})(["display:flex;align-items:center;justify-content:center;min-height:200px;color:", ";text-align:center;"], ({
  theme: e
}) => e.colors.danger), ma = ({
  name: e,
  title: t,
  children: r,
  actions: o,
  isLoading: i = !1,
  error: a = null,
  className: s,
  collapsible: l = !1,
  defaultCollapsed: d = !1
}) => {
  const [f, u] = Ce.useState(d), m = () => {
    l && u(!f);
  }, y = t || e.charAt(0).toUpperCase() + e.slice(1), x = () => a ? /* @__PURE__ */ n.jsx(ga, { children: /* @__PURE__ */ n.jsxs("div", { children: [
    /* @__PURE__ */ n.jsxs("div", { children: [
      "Error loading ",
      e
    ] }),
    /* @__PURE__ */ n.jsx("div", { style: {
      fontSize: "0.9em",
      marginTop: "8px"
    }, children: a })
  ] }) }) : i ? /* @__PURE__ */ n.jsxs(hr, { children: [
    "Loading ",
    e,
    "..."
  ] }) : r || /* @__PURE__ */ n.jsxs(hr, { children: [
    "No ",
    e,
    " data available"
  ] });
  return /* @__PURE__ */ n.jsxs(la, { className: s, "data-section": e, children: [
    /* @__PURE__ */ n.jsxs(da, { children: [
      /* @__PURE__ */ n.jsxs(ua, { onClick: m, style: {
        cursor: l ? "pointer" : "default"
      }, children: [
        y,
        l && /* @__PURE__ */ n.jsx("span", { style: {
          marginLeft: "8px",
          fontSize: "0.8em"
        }, children: f ? "▶" : "▼" })
      ] }),
      o && /* @__PURE__ */ n.jsx(pa, { children: o })
    ] }),
    !f && /* @__PURE__ */ n.jsx(fa, { children: x() })
  ] });
}, Sl = ma, ha = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-djltr5-0"
})(["display:grid;grid-template-areas:'header header' 'sidebar content';grid-template-columns:", ";grid-template-rows:auto 1fr;height:100vh;width:100%;overflow:hidden;transition:grid-template-columns ", " ease;"], ({
  sidebarCollapsed: e
}) => e ? "auto 1fr" : "240px 1fr", ({
  theme: e
}) => e.transitions.normal), xa = /* @__PURE__ */ c.header.withConfig({
  displayName: "HeaderContainer",
  componentId: "sc-djltr5-1"
})(["grid-area:header;background-color:", ";border-bottom:1px solid ", ";padding:", ";z-index:", ";"], ({
  theme: e
}) => e.colors.headerBackground, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.zIndex.fixed), ya = /* @__PURE__ */ c.aside.withConfig({
  displayName: "SidebarContainer",
  componentId: "sc-djltr5-2"
})(["grid-area:sidebar;background-color:", ";border-right:1px solid ", ";overflow-y:auto;transition:width ", " ease;width:", ";"], ({
  theme: e
}) => e.colors.sidebarBackground, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.transitions.normal, ({
  collapsed: e
}) => e ? "60px" : "240px"), ba = /* @__PURE__ */ c.main.withConfig({
  displayName: "ContentContainer",
  componentId: "sc-djltr5-3"
})(["grid-area:content;overflow-y:auto;padding:", ";background-color:", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.colors.background), Cl = ({
  header: e,
  sidebar: t,
  children: r,
  sidebarCollapsed: o = !1,
  // toggleSidebar, // Unused prop
  className: i
}) => /* @__PURE__ */ n.jsxs(ha, { sidebarCollapsed: o, className: i, children: [
  /* @__PURE__ */ n.jsx(xa, { children: e }),
  /* @__PURE__ */ n.jsx(ya, { collapsed: o, children: t }),
  /* @__PURE__ */ n.jsx(ba, { children: r })
] }), va = /* @__PURE__ */ c.div.withConfig({
  displayName: "BuilderContainer",
  componentId: "sc-5duzr2-0"
})(["background:#1a1a1a;border:1px solid #4b5563;border-radius:8px;padding:24px;margin-bottom:16px;"]), wa = /* @__PURE__ */ c.h3.withConfig({
  displayName: "SectionTitle",
  componentId: "sc-5duzr2-1"
})(["color:#ffffff;font-size:1.1rem;font-weight:600;margin-bottom:16px;border-bottom:2px solid #dc2626;padding-bottom:8px;"]), Sa = /* @__PURE__ */ c.div.withConfig({
  displayName: "MatrixGrid",
  componentId: "sc-5duzr2-2"
})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px;margin-bottom:20px;"]), Ye = /* @__PURE__ */ c.div.withConfig({
  displayName: "ElementSection",
  componentId: "sc-5duzr2-3"
})(["background:#262626;border:1px solid #4b5563;border-radius:6px;padding:16px;"]), $e = /* @__PURE__ */ c.h4.withConfig({
  displayName: "ElementTitle",
  componentId: "sc-5duzr2-4"
})(["color:#ffffff;font-size:0.9rem;font-weight:600;margin-bottom:12px;text-transform:uppercase;letter-spacing:0.5px;"]), We = /* @__PURE__ */ c.select.withConfig({
  displayName: "Select",
  componentId: "sc-5duzr2-5"
})(["width:100%;padding:8px 12px;background:#0f0f0f;border:1px solid #4b5563;border-radius:4px;color:#ffffff;font-size:0.9rem;&:focus{outline:none;border-color:#dc2626;box-shadow:0 0 0 2px rgba(220,38,38,0.2);}option{background:#0f0f0f;color:#ffffff;}"]), Ca = /* @__PURE__ */ c.div.withConfig({
  displayName: "PreviewContainer",
  componentId: "sc-5duzr2-6"
})(["background:#0f0f0f;border:1px solid #4b5563;border-radius:6px;padding:16px;margin-top:16px;"]), Ea = /* @__PURE__ */ c.div.withConfig({
  displayName: "PreviewText",
  componentId: "sc-5duzr2-7"
})(["color:#ffffff;font-family:'Monaco','Menlo','Ubuntu Mono',monospace;font-size:0.9rem;line-height:1.4;min-height:20px;"]), xr = /* @__PURE__ */ c.span.withConfig({
  displayName: "RequiredIndicator",
  componentId: "sc-5duzr2-8"
})(["color:#dc2626;margin-left:4px;"]), yr = /* @__PURE__ */ c.span.withConfig({
  displayName: "OptionalIndicator",
  componentId: "sc-5duzr2-9"
})(["color:#9ca3af;font-size:0.8rem;margin-left:4px;"]), Ta = ({
  onSetupChange: e,
  initialComponents: t
}) => {
  const [r, o] = H({
    constant: (t == null ? void 0 : t.constant) || "",
    action: (t == null ? void 0 : t.action) || "None",
    variable: (t == null ? void 0 : t.variable) || "None",
    entry: (t == null ? void 0 : t.entry) || ""
  });
  ae(() => {
    r.constant && r.entry && e(r);
  }, [r, e]);
  const i = (s, l) => {
    o((d) => ({
      ...d,
      [s]: l
    }));
  }, a = () => {
    const {
      constant: s,
      action: l,
      variable: d,
      entry: f
    } = r;
    if (!s || !f)
      return "Select required elements to see setup preview...";
    let u = s;
    return l && l !== "None" && (u += ` → ${l}`), d && d !== "None" && (u += ` → ${d}`), u += ` [${f}]`, u;
  };
  return /* @__PURE__ */ n.jsxs(va, { children: [
    /* @__PURE__ */ n.jsx(wa, { children: "Setup Construction Matrix" }),
    /* @__PURE__ */ n.jsxs(Sa, { children: [
      /* @__PURE__ */ n.jsxs(Ye, { children: [
        /* @__PURE__ */ n.jsxs($e, { children: [
          "Constant Element",
          /* @__PURE__ */ n.jsx(xr, { children: "*" })
        ] }),
        /* @__PURE__ */ n.jsxs(We, { value: r.constant, onChange: (s) => i("constant", s.target.value), children: [
          /* @__PURE__ */ n.jsx("option", { value: "", children: "Select Constant" }),
          ke.constant.parentArrays.map((s) => /* @__PURE__ */ n.jsx("option", { value: s, children: s }, s)),
          ke.constant.fvgTypes.map((s) => /* @__PURE__ */ n.jsx("option", { value: s, children: s }, s))
        ] })
      ] }),
      /* @__PURE__ */ n.jsxs(Ye, { children: [
        /* @__PURE__ */ n.jsxs($e, { children: [
          "Action Element",
          /* @__PURE__ */ n.jsx(yr, { children: "(optional)" })
        ] }),
        /* @__PURE__ */ n.jsxs(We, { value: r.action, onChange: (s) => i("action", s.target.value), children: [
          /* @__PURE__ */ n.jsx("option", { value: "None", children: "None" }),
          ke.action.liquidityEvents.map((s) => /* @__PURE__ */ n.jsx("option", { value: s, children: s }, s))
        ] })
      ] }),
      /* @__PURE__ */ n.jsxs(Ye, { children: [
        /* @__PURE__ */ n.jsxs($e, { children: [
          "Variable Element",
          /* @__PURE__ */ n.jsx(yr, { children: "(optional)" })
        ] }),
        /* @__PURE__ */ n.jsxs(We, { value: r.variable, onChange: (s) => i("variable", s.target.value), children: [
          /* @__PURE__ */ n.jsx("option", { value: "None", children: "None" }),
          ke.variable.rdTypes.map((s) => /* @__PURE__ */ n.jsx("option", { value: s, children: s }, s))
        ] })
      ] }),
      /* @__PURE__ */ n.jsxs(Ye, { children: [
        /* @__PURE__ */ n.jsxs($e, { children: [
          "Entry Method",
          /* @__PURE__ */ n.jsx(xr, { children: "*" })
        ] }),
        /* @__PURE__ */ n.jsxs(We, { value: r.entry, onChange: (s) => i("entry", s.target.value), children: [
          /* @__PURE__ */ n.jsx("option", { value: "", children: "Select Entry Method" }),
          ke.entry.methods.map((s) => /* @__PURE__ */ n.jsx("option", { value: s, children: s }, s))
        ] })
      ] })
    ] }),
    /* @__PURE__ */ n.jsxs(Ca, { children: [
      /* @__PURE__ */ n.jsx($e, { children: "Setup Preview" }),
      /* @__PURE__ */ n.jsx(Ea, { children: a() })
    ] })
  ] });
}, El = Ta, br = /* @__PURE__ */ c.div.withConfig({
  displayName: "MetricsContainer",
  componentId: "sc-opkdti-0"
})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:", ";"], ({
  theme: e
}) => e.spacing.md), vr = /* @__PURE__ */ c.div.withConfig({
  displayName: "MetricCard",
  componentId: "sc-opkdti-1"
})(["background:", ";border:1px solid ", ";border-radius:", ";padding:", ";"], ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.spacing.md), wr = /* @__PURE__ */ c.div.withConfig({
  displayName: "MetricLabel",
  componentId: "sc-opkdti-2"
})(["color:", ";font-size:", ";margin-bottom:", ";"], ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.spacing.xs), Sr = /* @__PURE__ */ c.div.withConfig({
  displayName: "MetricValue",
  componentId: "sc-opkdti-3"
})(["color:", ";font-size:", ";font-weight:600;"], ({
  theme: e,
  positive: t,
  negative: r
}) => t ? e.colors.success : r ? e.colors.danger : e.colors.textPrimary, ({
  theme: e
}) => e.fontSizes.lg), Ia = ({
  metrics: e,
  isLoading: t
}) => t ? /* @__PURE__ */ n.jsx(br, { children: Array.from({
  length: 4
}).map((r, o) => /* @__PURE__ */ n.jsxs(vr, { children: [
  /* @__PURE__ */ n.jsx(wr, { children: "Loading..." }),
  /* @__PURE__ */ n.jsx(Sr, { children: "--" })
] }, o)) }) : /* @__PURE__ */ n.jsx(br, { children: e.map((r, o) => /* @__PURE__ */ n.jsxs(vr, { children: [
  /* @__PURE__ */ n.jsx(wr, { children: r.label }),
  /* @__PURE__ */ n.jsx(Sr, { positive: r.positive, negative: r.negative, children: r.value })
] }, o)) }), Tl = Ia, Ra = /* @__PURE__ */ c.div.withConfig({
  displayName: "AnalysisContainer",
  componentId: "sc-tp1ymt-0"
})(["background:", ";border:1px solid ", ";border-radius:", ";padding:", ";"], ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.spacing.lg), ja = /* @__PURE__ */ c.h3.withConfig({
  displayName: "AnalysisTitle",
  componentId: "sc-tp1ymt-1"
})(["color:", ";font-size:", ";font-weight:600;margin-bottom:", ";"], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.fontSizes.lg, ({
  theme: e
}) => e.spacing.md), Na = /* @__PURE__ */ c.div.withConfig({
  displayName: "AnalysisContent",
  componentId: "sc-tp1ymt-2"
})(["color:", ";line-height:1.6;"], ({
  theme: e
}) => e.colors.textSecondary), Da = ({
  title: e = "Trade Analysis",
  children: t,
  isLoading: r
}) => /* @__PURE__ */ n.jsxs(Ra, { children: [
  /* @__PURE__ */ n.jsx(ja, { children: e }),
  /* @__PURE__ */ n.jsx(Na, { children: r ? /* @__PURE__ */ n.jsx("div", { children: "Loading analysis..." }) : t || /* @__PURE__ */ n.jsx("div", { children: "No analysis data available" }) })
] }), Il = Da, w = {
  // F1 Racing Team Colors
  f1Red: "#e10600",
  // Ferrari Red - CRITICAL ALERTS ONLY
  f1RedDark: "#c10500",
  f1RedLight: "#ff3b36",
  f1Blue: "#0600EF",
  // Racing Blue - Information & Neutral
  f1BlueDark: "#0500CC",
  f1BlueLight: "#4169E1",
  // F1 Racing Performance Colors
  f1MercedesGreen: "#00D2BE",
  // Active, Success, Optimal
  f1MercedesGreenDark: "#00A896",
  f1MercedesGreenLight: "#00FFE5",
  f1McLarenOrange: "#FF8700",
  // Warnings, Transitions
  f1McLarenOrangeDark: "#E67600",
  f1McLarenOrangeLight: "#FFA500",
  f1RacingYellow: "#FFD320",
  // Caution, Pending
  f1RacingYellowDark: "#E6BE1D",
  f1RacingYellowLight: "#FFDC4A",
  f1Carbon: "#1A1A1A",
  // Base background
  f1Silver: "#C0C0C0",
  // Secondary text
  // Neutrals
  white: "#ffffff",
  black: "#000000",
  gray50: "#f9fafb",
  gray100: "#f3f4f6",
  gray200: "#e5e7eb",
  gray300: "#d1d5db",
  gray400: "#9ca3af",
  gray500: "#6b7280",
  gray600: "#4b5563",
  gray700: "#374151",
  gray800: "#1f2937",
  gray900: "#111827",
  // Status colors
  green: "#4caf50",
  greenDark: "#388e3c",
  greenLight: "#81c784",
  yellow: "#ffeb3b",
  yellowDark: "#fbc02d",
  yellowLight: "#fff59d",
  orange: "#ff9800",
  orangeDark: "#f57c00",
  orangeLight: "#ffb74d",
  red: "#f44336",
  redDark: "#d32f2f",
  redLight: "#e57373",
  blue: "#2196f3",
  blueDark: "#1976d2",
  blueLight: "#64b5f6",
  purple: "#9c27b0",
  purpleDark: "#7b1fa2",
  purpleLight: "#ba68c8",
  // Transparent colors
  whiteTransparent10: "rgba(255, 255, 255, 0.1)",
  blackTransparent10: "rgba(0, 0, 0, 0.1)"
}, ie = {
  background: "#0f0f0f",
  surface: "#1a1a1a",
  cardBackground: "#1a1a1a",
  border: "#333333",
  divider: "rgba(255, 255, 255, 0.1)",
  textPrimary: "#ffffff",
  textSecondary: "#aaaaaa",
  textDisabled: "#666666",
  textInverse: "#1a1f2c",
  success: w.green,
  warning: w.yellow,
  error: w.red,
  info: w.blue,
  // Chart colors
  chartGrid: "rgba(255, 255, 255, 0.1)",
  chartLine: w.f1Red,
  // Trading specific colors
  profit: w.green,
  loss: w.red,
  neutral: w.gray400,
  // Component specific colors
  tooltipBackground: "rgba(37, 42, 55, 0.9)",
  modalBackground: "rgba(26, 31, 44, 0.8)"
}, Rl = {
  background: "#f5f5f5",
  surface: "#ffffff",
  cardBackground: "#ffffff",
  border: "#e0e0e0",
  divider: "rgba(0, 0, 0, 0.1)",
  textPrimary: "#333333",
  textSecondary: "#666666",
  textDisabled: "#999999",
  textInverse: "#ffffff",
  success: w.green,
  warning: w.yellow,
  error: w.red,
  info: w.blue,
  // Chart colors
  chartGrid: "rgba(0, 0, 0, 0.1)",
  chartLine: w.f1Red,
  // Trading specific colors
  profit: w.green,
  loss: w.red,
  neutral: w.gray400,
  // Component specific colors
  tooltipBackground: "rgba(255, 255, 255, 0.9)",
  modalBackground: "rgba(255, 255, 255, 0.8)"
}, X = {
  xxs: "4px",
  xs: "8px",
  sm: "12px",
  md: "16px",
  lg: "24px",
  xl: "32px",
  xxl: "48px"
}, pe = {
  xs: "0.75rem",
  sm: "0.875rem",
  md: "1rem",
  lg: "1.125rem",
  xl: "1.25rem",
  xxl: "1.5rem",
  xxxl: "2.5rem",
  // Added missing xxxl size
  h1: "2.5rem",
  h2: "2rem",
  h3: "1.75rem",
  h4: "1.5rem",
  h5: "1.25rem",
  h6: "1rem"
}, et = {
  light: 300,
  regular: 400,
  medium: 500,
  semibold: 600,
  bold: 700
}, tt = {
  tight: 1.25,
  normal: 1.5,
  relaxed: 1.75
}, rt = {
  body: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",
  heading: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",
  mono: "SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace"
}, ot = {
  xs: "480px",
  sm: "640px",
  md: "768px",
  lg: "1024px",
  xl: "1280px"
}, nt = {
  xs: "2px",
  sm: "4px",
  md: "6px",
  lg: "8px",
  xl: "12px",
  pill: "9999px",
  circle: "50%"
}, it = {
  sm: "0 1px 3px rgba(0, 0, 0, 0.1)",
  md: "0 4px 6px rgba(0, 0, 0, 0.1)",
  lg: "0 10px 15px rgba(0, 0, 0, 0.1)"
}, st = {
  fast: "0.1s",
  normal: "0.3s",
  slow: "0.5s"
}, at = {
  base: 1,
  overlay: 10,
  modal: 20,
  popover: 30,
  tooltip: 40,
  fixed: 100
}, La = /* @__PURE__ */ c.div.withConfig({
  displayName: "HeaderContainer",
  componentId: "sc-e71xhh-0"
})(["display:flex;justify-content:space-between;align-items:center;padding:", " 0;border-bottom:2px solid #4b5563;margin-bottom:", ";", ""], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.lg) || "16px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.lg) || "16px";
}, ({
  $variant: e
}) => {
  switch (e) {
    case "dashboard":
      return g(["padding:24px 0;margin-bottom:24px;"]);
    case "form":
      return g(["padding:16px 0;margin-bottom:16px;"]);
    default:
      return g(["padding:20px 0;margin-bottom:20px;"]);
  }
}), ka = /* @__PURE__ */ c.div.withConfig({
  displayName: "TitleSection",
  componentId: "sc-e71xhh-1"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xs) || "4px";
}), _a = /* @__PURE__ */ c.h1.withConfig({
  displayName: "MainTitle",
  componentId: "sc-e71xhh-2"
})(["font-weight:700;color:", ";margin:0;letter-spacing:-0.025em;text-transform:uppercase;font-family:'Inter',-apple-system,BlinkMacSystemFont,sans-serif;", " span{color:", ";}"], ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textPrimary) || "#ffffff";
}, ({
  $variant: e
}) => {
  switch (e) {
    case "dashboard":
      return g(["font-size:", ";"], pe.xxxl);
    case "analysis":
      return g(["font-size:", ";"], pe.xxl);
    case "form":
      return g(["font-size:", ";"], pe.xl);
    default:
      return g(["font-size:", ";"], pe.xxl);
  }
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626";
}), Ma = /* @__PURE__ */ c.div.withConfig({
  displayName: "Subtitle",
  componentId: "sc-e71xhh-3"
})(["font-size:", ";color:#9ca3af;font-weight:500;text-transform:uppercase;letter-spacing:0.05em;"], pe.sm), Pa = /* @__PURE__ */ c.div.withConfig({
  displayName: "ActionsSection",
  componentId: "sc-e71xhh-4"
})(["display:flex;align-items:center;gap:", ";flex-wrap:wrap;"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.md) || "12px";
}), Oa = /* @__PURE__ */ c.div.withConfig({
  displayName: "StatusIndicator",
  componentId: "sc-e71xhh-5"
})(["display:flex;align-items:center;gap:", ";padding:", " ", ";border-radius:", ";font-size:", ";font-weight:600;text-transform:uppercase;letter-spacing:0.05em;", ""], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xs) || "4px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xs) || "4px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.sm) || "8px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.borderRadius) == null ? void 0 : t.sm) || "4px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.xs) || "0.75rem";
}, ({
  $isLive: e,
  $variant: t,
  theme: r
}) => {
  var o, i, a, s, l, d, f, u, m;
  return e ? g(["background:", "20;border:1px solid ", ";color:", ";"], ((o = r.colors) == null ? void 0 : o.sessionActive) || "#00D2BE", ((i = r.colors) == null ? void 0 : i.sessionActive) || "#00D2BE", ((a = r.colors) == null ? void 0 : a.sessionActive) || "#00D2BE") : t === "active" ? g(["background:", "20;border:1px solid ", ";color:", ";"], ((s = r.colors) == null ? void 0 : s.sessionOptimal) || "#00FFE5", ((l = r.colors) == null ? void 0 : l.sessionOptimal) || "#00FFE5", ((d = r.colors) == null ? void 0 : d.sessionOptimal) || "#00FFE5") : g(["background:", "20;border:1px solid ", ";color:", ";"], ((f = r.colors) == null ? void 0 : f.textSecondary) || "#9ca3af", ((u = r.colors) == null ? void 0 : u.textSecondary) || "#9ca3af", ((m = r.colors) == null ? void 0 : m.textSecondary) || "#9ca3af");
}), Fa = /* @__PURE__ */ c.div.withConfig({
  displayName: "StatusDot",
  componentId: "sc-e71xhh-6"
})(["width:6px;height:6px;border-radius:50%;background:", ";", ""], ({
  $isLive: e,
  theme: t
}) => {
  var r, o;
  return e ? ((r = t.colors) == null ? void 0 : r.sessionActive) || "#00D2BE" : ((o = t.colors) == null ? void 0 : o.sessionOptimal) || "#00FFE5";
}, ({
  $isLive: e
}) => e && g(["animation:mercedesPulse 2s infinite;@keyframes mercedesPulse{0%,100%{opacity:1;transform:scale(1);}50%{opacity:0.7;transform:scale(1.2);}}"])), $a = /* @__PURE__ */ c.button.withConfig({
  displayName: "RefreshButton",
  componentId: "sc-e71xhh-7"
})(["padding:", " ", ";background:transparent;color:", ";border:1px solid #4b5563;border-radius:", ";cursor:pointer;font-weight:500;font-size:", ";transition:all 0.2s ease;min-width:100px;position:relative;&:hover{background:#4b5563;color:", ";border-color:", ";}&:disabled{opacity:0.6;cursor:not-allowed;}", ""], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.sm) || "8px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.md) || "12px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textSecondary) || "#9ca3af";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.borderRadius) == null ? void 0 : t.sm) || "4px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626";
}, ({
  $isRefreshing: e
}) => e && g(["&::after{content:'';position:absolute;top:50%;left:50%;width:16px;height:16px;margin:-8px 0 0 -8px;border:2px solid transparent;border-top:2px solid currentColor;border-radius:50%;animation:spin 1s linear infinite;}@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"])), Aa = /* @__PURE__ */ c.div.withConfig({
  displayName: "CustomActions",
  componentId: "sc-e71xhh-8"
})(["display:flex;align-items:center;gap:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.sm) || "8px";
}), jl = (e) => {
  const {
    title: t,
    subtitle: r,
    isLive: o = !1,
    liveText: i = "LIVE SESSION",
    statusText: a,
    onRefresh: s,
    isRefreshing: l = !1,
    actions: d,
    variant: f = "dashboard",
    className: u
  } = e, m = o ? i : a;
  return /* @__PURE__ */ n.jsxs(La, { $variant: f, className: u, children: [
    /* @__PURE__ */ n.jsxs(ka, { children: [
      /* @__PURE__ */ n.jsx(_a, { $variant: f, children: f === "dashboard" ? /* @__PURE__ */ n.jsxs(n.Fragment, { children: [
        "🏎️ ",
        t.replace("Trading", "TRADING").replace("Dashboard", "DASHBOARD")
      ] }) : t }),
      r && /* @__PURE__ */ n.jsx(Ma, { children: r })
    ] }),
    /* @__PURE__ */ n.jsxs(Pa, { children: [
      m && /* @__PURE__ */ n.jsxs(Oa, { $isLive: o, $variant: !o && a ? "active" : void 0, children: [
        /* @__PURE__ */ n.jsx(Fa, { $isLive: o }),
        m
      ] }),
      s && /* @__PURE__ */ n.jsx($a, { onClick: s, disabled: l, $isRefreshing: l, children: l ? "Refreshing..." : "Refresh" }),
      d && /* @__PURE__ */ n.jsx(Aa, { children: d })
    ] })
  ] });
}, yt = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-vuv4tf-0"
})(["display:flex;flex-direction:column;width:100%;max-width:", ";margin:0 auto;min-height:", ";", " ", " ", " ", ""], ({
  $maxWidth: e
}) => typeof e == "number" ? `${e}px` : e, ({
  $variant: e
}) => e === "dashboard" ? "100vh" : "auto", ({
  $padding: e
}) => {
  const t = {
    sm: X.sm,
    md: X.md,
    lg: X.lg,
    xl: X.xl
  };
  return g(["padding:", ";"], t[e || "lg"]);
}, ({
  $background: e,
  theme: t
}) => {
  const r = {
    default: t.colors.background,
    surface: t.colors.surface,
    elevated: t.colors.elevated
  };
  return g(["background:", ";"], r[e || "default"]);
}, ({
  $variant: e
}) => {
  switch (e) {
    case "dashboard":
      return g(["gap:24px;padding-top:0;"]);
    case "form":
      return g(["gap:16px;max-width:800px;"]);
    case "analysis":
      return g(["gap:20px;max-width:1400px;"]);
    case "settings":
      return g(["gap:16px;max-width:1000px;"]);
    default:
      return g(["gap:16px;"]);
  }
}, ({
  $animated: e
}) => e && g(["transition:all 0.3s ease-in-out;&.entering{opacity:0;transform:translateY(20px);}&.entered{opacity:1;transform:translateY(0);}"])), za = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingContainer",
  componentId: "sc-vuv4tf-1"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:400px;gap:16px;color:#9ca3af;"]), Va = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-vuv4tf-2"
})(["width:40px;height:40px;border:3px solid #4b5563;border-top:3px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]), Ga = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorContainer",
  componentId: "sc-vuv4tf-3"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px;background:rgba(244,67,54,0.1);border:1px solid #f44336;border-radius:8px;color:#f44336;text-align:center;gap:16px;"]), Ba = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorIcon",
  componentId: "sc-vuv4tf-4"
})(["font-size:48px;opacity:0.8;"]), qa = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-vuv4tf-5"
})(["font-size:16px;font-weight:500;"]), Ha = /* @__PURE__ */ c.button.withConfig({
  displayName: "RetryButton",
  componentId: "sc-vuv4tf-6"
})(["padding:8px 16px;background:#f44336;color:white;border:none;border-radius:4px;cursor:pointer;font-weight:500;transition:background 0.2s ease;&:hover{background:#d32f2f;}"]), Cr = () => /* @__PURE__ */ n.jsxs(za, { children: [
  /* @__PURE__ */ n.jsx(Va, {}),
  /* @__PURE__ */ n.jsx("div", { children: "Loading..." })
] }), Ua = ({
  error: e,
  onRetry: t
}) => /* @__PURE__ */ n.jsxs(Ga, { children: [
  /* @__PURE__ */ n.jsx(Ba, { children: "⚠️" }),
  /* @__PURE__ */ n.jsx(qa, { children: e }),
  t && /* @__PURE__ */ n.jsx(Ha, { onClick: t, children: "Retry" })
] }), Nl = (e) => {
  const {
    children: t,
    variant: r = "dashboard",
    maxWidth: o = "100%",
    padding: i = "lg",
    isLoading: a = !1,
    error: s = null,
    loadingFallback: l,
    errorFallback: d,
    className: f,
    animated: u = !0,
    background: m = "default"
  } = e, y = {
    $variant: r,
    $maxWidth: o,
    $padding: i,
    $animated: u,
    $background: m
  };
  return s ? /* @__PURE__ */ n.jsx(yt, { ...y, className: f, children: d || /* @__PURE__ */ n.jsx(Ua, { error: s }) }) : a ? /* @__PURE__ */ n.jsx(yt, { ...y, className: f, children: l || /* @__PURE__ */ n.jsx(Cr, {}) }) : /* @__PURE__ */ n.jsx(yt, { ...y, className: f, children: /* @__PURE__ */ n.jsx(po, { fallback: l || /* @__PURE__ */ n.jsx(Cr, {}), children: t }) });
}, Ya = /* @__PURE__ */ c.form.withConfig({
  displayName: "FormContainer",
  componentId: "sc-1gwzj6e-0"
})(["display:flex;flex-direction:column;gap:", ";background:", ";border-radius:", ";border:1px solid ", ";position:relative;", " ", " ", ""], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.md) || "12px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.surface) || "#1f2937";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.borderRadius) == null ? void 0 : t.lg) || "8px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.border) || "#4b5563";
}, ({
  $variant: e
}) => {
  switch (e) {
    case "quick":
      return g(["padding:", ";max-width:600px;"], X.lg);
    case "detailed":
      return g(["padding:", ";max-width:800px;"], X.xl);
    case "modal":
      return g(["padding:", ";max-width:500px;margin:0 auto;"], X.lg);
    case "inline":
      return g(["padding:", ";background:transparent;border:none;"], X.md);
    default:
      return g(["padding:", ";"], X.lg);
  }
}, ({
  $showAccent: e,
  theme: t
}) => {
  var r, o, i, a, s;
  return e && g(["&::before{content:'';position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient( 90deg,", ",", ",", " );border-radius:", " ", " 0 0;}"], ((r = t.colors) == null ? void 0 : r.primary) || "#dc2626", ((o = t.colors) == null ? void 0 : o.primaryDark) || "#b91c1c", ((i = t.colors) == null ? void 0 : i.primary) || "#dc2626", ((a = t.borderRadius) == null ? void 0 : a.lg) || "8px", ((s = t.borderRadius) == null ? void 0 : s.lg) || "8px");
}, ({
  $disabled: e
}) => e && g(["opacity:0.6;pointer-events:none;"])), Wa = /* @__PURE__ */ c.div.withConfig({
  displayName: "FormHeader",
  componentId: "sc-1gwzj6e-1"
})(["display:flex;flex-direction:column;gap:", ";margin-bottom:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xs) || "4px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.md) || "12px";
}), Ka = /* @__PURE__ */ c.h3.withConfig({
  displayName: "FormTitle",
  componentId: "sc-1gwzj6e-2"
})(["font-size:", ";font-weight:700;color:", ";margin:0;text-transform:uppercase;letter-spacing:0.025em;display:flex;align-items:center;gap:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.lg) || "1.125rem";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.sm) || "8px";
}), Qa = /* @__PURE__ */ c.div.withConfig({
  displayName: "FormSubtitle",
  componentId: "sc-1gwzj6e-3"
})(["font-size:", ";color:", ";font-weight:500;"], ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textSecondary) || "#9ca3af";
}), Xa = /* @__PURE__ */ c.div.withConfig({
  displayName: "FormContent",
  componentId: "sc-1gwzj6e-4"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.md) || "12px";
}), Er = /* @__PURE__ */ c.div.withConfig({
  displayName: "FormMessage",
  componentId: "sc-1gwzj6e-5"
})(["padding:", " ", ";border-radius:", ";font-size:", ";font-weight:500;display:flex;align-items:center;gap:", ";", ""], ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.sm) || "8px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.md) || "12px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.borderRadius) == null ? void 0 : t.sm) || "4px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xs) || "4px";
}, ({
  $type: e
}) => {
  switch (e) {
    case "error":
      return g(["background:rgba(244,67,54,0.1);border:1px solid #f44336;color:#f44336;"]);
    case "success":
      return g(["background:rgba(34,197,94,0.1);border:1px solid #22c55e;color:#22c55e;"]);
    case "info":
      return g(["background:rgba(59,130,246,0.1);border:1px solid #3b82f6;color:#3b82f6;"]);
  }
}), Ja = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-1gwzj6e-6"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;border-radius:", ";z-index:10;"], ({
  theme: e
}) => {
  var t;
  return ((t = e.borderRadius) == null ? void 0 : t.lg) || "8px";
}), Za = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-1gwzj6e-7"
})(["width:32px;height:32px;border:3px solid #4b5563;border-top:3px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]), ec = /* @__PURE__ */ c.div.withConfig({
  displayName: "AutoSaveIndicator",
  componentId: "sc-1gwzj6e-8"
})(["position:absolute;top:8px;right:8px;font-size:", ";color:", ";opacity:", ";transition:opacity 0.3s ease;"], ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textSecondary) || "#9ca3af";
}, ({
  $visible: e
}) => e ? 1 : 0), Dl = (e) => {
  const {
    children: t,
    onSubmit: r,
    title: o,
    subtitle: i,
    isSubmitting: a = !1,
    error: s = null,
    success: l = null,
    variant: d = "quick",
    showAccent: f = !0,
    className: u,
    disabled: m = !1,
    autoSave: y = !1
  } = e, x = async (h) => {
    h.preventDefault(), r && !a && !m && await r(h);
  };
  return /* @__PURE__ */ n.jsxs(Ya, { $variant: d, $showAccent: f, $disabled: m, className: u, onSubmit: x, noValidate: !0, children: [
    a && /* @__PURE__ */ n.jsx(Ja, { children: /* @__PURE__ */ n.jsx(Za, {}) }),
    y && /* @__PURE__ */ n.jsx(ec, { $visible: !a, children: "Auto-save enabled" }),
    (o || i) && /* @__PURE__ */ n.jsxs(Wa, { children: [
      o && /* @__PURE__ */ n.jsx(Ka, { children: o }),
      i && /* @__PURE__ */ n.jsx(Qa, { children: i })
    ] }),
    s && /* @__PURE__ */ n.jsxs(Er, { $type: "error", children: [
      "⚠️ ",
      s
    ] }),
    l && /* @__PURE__ */ n.jsxs(Er, { $type: "success", children: [
      "✅ ",
      l
    ] }),
    /* @__PURE__ */ n.jsx(Xa, { children: t })
  ] });
}, tc = /* @__PURE__ */ c.div.withConfig({
  displayName: "FieldContainer",
  componentId: "sc-sq94oz-0"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  $size: e
}) => ({
  sm: X.xs,
  md: X.sm,
  lg: X.md
})[e || "md"]), rc = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-sq94oz-1"
})(["font-size:", ";font-weight:600;color:", ";display:flex;align-items:center;gap:", ";", " ", ""], ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xs) || "4px";
}, ({
  $variant: e
}) => {
  switch (e) {
    case "trading":
      return g(["text-transform:uppercase;letter-spacing:0.025em;"]);
    case "analysis":
      return g(["font-weight:500;"]);
    default:
      return g([""]);
  }
}, ({
  $required: e,
  theme: t
}) => {
  var r;
  return e && g(["&::after{content:'*';color:", ";margin-left:2px;}"], ((r = t.colors) == null ? void 0 : r.primary) || "#dc2626");
}), oc = /* @__PURE__ */ c.div.withConfig({
  displayName: "InputContainer",
  componentId: "sc-sq94oz-2"
})(["position:relative;display:flex;align-items:center;", ""], ({
  $disabled: e
}) => e && g(["opacity:0.6;pointer-events:none;"])), Dt = /* @__PURE__ */ g(["width:100%;border:1px solid ", ";border-radius:", ";background:", ";color:", ";font-family:inherit;transition:all 0.2s ease;", " &:focus{outline:none;border-color:", ";box-shadow:0 0 0 2px rgba(220,38,38,0.2);}&:disabled{background:#374151;color:#9ca3af;cursor:not-allowed;}&::placeholder{color:#6b7280;}"], ({
  $hasError: e,
  theme: t
}) => {
  var r;
  return e ? ((r = t.colors) == null ? void 0 : r.error) || "#f44336" : "#4b5563";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.borderRadius) == null ? void 0 : t.sm) || "4px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.background) || "#111827";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textPrimary) || "#ffffff";
}, ({
  $size: e
}) => ({
  sm: g(["padding:", " ", ";font-size:", ";"], X.xs, X.sm, pe.sm),
  md: g(["padding:", " ", ";font-size:", ";"], X.sm, X.md, pe.md),
  lg: g(["padding:", " ", ";font-size:", ";"], X.md, X.lg, pe.lg)
})[e || "md"], ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626";
}), nc = /* @__PURE__ */ c.input.withConfig({
  displayName: "Input",
  componentId: "sc-sq94oz-3"
})(["", ""], Dt), ic = /* @__PURE__ */ c.select.withConfig({
  displayName: "Select",
  componentId: "sc-sq94oz-4"
})(["", " cursor:pointer;option{background:", ";color:", ";}"], Dt, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.background) || "#111827";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textPrimary) || "#ffffff";
}), sc = /* @__PURE__ */ c.textarea.withConfig({
  displayName: "TextArea",
  componentId: "sc-sq94oz-5"
})(["", " resize:vertical;min-height:80px;font-family:inherit;"], Dt), ac = /* @__PURE__ */ c.div.withConfig({
  displayName: "PrefixContainer",
  componentId: "sc-sq94oz-6"
})(["position:absolute;left:12px;display:flex;align-items:center;color:", ";pointer-events:none;z-index:1;"], ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textSecondary) || "#9ca3af";
}), cc = /* @__PURE__ */ c.div.withConfig({
  displayName: "SuffixContainer",
  componentId: "sc-sq94oz-7"
})(["position:absolute;right:12px;display:flex;align-items:center;color:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textSecondary) || "#9ca3af";
}), lc = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-sq94oz-8"
})(["font-size:", ";color:", ";font-weight:500;display:flex;align-items:center;gap:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.error) || "#f44336";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.spacing) == null ? void 0 : t.xs) || "4px";
}), dc = /* @__PURE__ */ c.div.withConfig({
  displayName: "HelpText",
  componentId: "sc-sq94oz-9"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var t;
  return ((t = e.fontSizes) == null ? void 0 : t.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.colors) == null ? void 0 : t.textSecondary) || "#9ca3af";
}), uc = /* @__PURE__ */ c.div.withConfig({
  displayName: "ValidationIndicator",
  componentId: "sc-sq94oz-10"
})(["position:absolute;right:8px;display:flex;align-items:center;", " ", ""], ({
  $validating: e
}) => e && g(["&::after{content:'';width:12px;height:12px;border:2px solid #4b5563;border-top:2px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;}@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]), ({
  $valid: e,
  $validating: t
}) => !t && g(["color:", ";&::after{content:'", "';}"], e ? "#22c55e" : "#f44336", e ? "✓" : "✗")), Ll = (e) => {
  const {
    label: t,
    field: r,
    type: o = "text",
    placeholder: i,
    required: a = !1,
    disabled: s = !1,
    helpText: l,
    options: d = [],
    inputProps: f = {},
    className: u,
    size: m = "md",
    variant: y = "default",
    prefix: x,
    suffix: h
  } = e, b = !!(r.error && r.touched), E = r.touched && !r.validating, S = () => {
    const C = {
      id: f.id || t.toLowerCase().replace(/\s+/g, "-"),
      value: r.value,
      onChange: r.setValue,
      onBlur: () => r.setTouched(!0),
      disabled: s,
      placeholder: i,
      $hasError: b,
      $size: m,
      ...f
    };
    switch (o) {
      case "select":
        return /* @__PURE__ */ n.jsxs(ic, { ...C, children: [
          i && /* @__PURE__ */ n.jsx("option", { value: "", disabled: !0, children: i }),
          d.map((k) => /* @__PURE__ */ n.jsx("option", { value: k.value, children: k.label }, k.value))
        ] });
      case "textarea":
        return /* @__PURE__ */ n.jsx(sc, { ...C });
      default:
        return /* @__PURE__ */ n.jsx(nc, { ...C, type: o });
    }
  };
  return /* @__PURE__ */ n.jsxs(tc, { $size: m, className: u, children: [
    /* @__PURE__ */ n.jsx(rc, { $required: a, $variant: y, htmlFor: f.id || t.toLowerCase().replace(/\s+/g, "-"), children: t }),
    /* @__PURE__ */ n.jsxs(oc, { $hasError: b, $disabled: s, children: [
      x && /* @__PURE__ */ n.jsx(ac, { children: x }),
      S(),
      h && /* @__PURE__ */ n.jsx(cc, { children: h }),
      E && /* @__PURE__ */ n.jsx(uc, { $valid: r.valid, $validating: r.validating })
    ] }),
    b && /* @__PURE__ */ n.jsxs(lc, { children: [
      "⚠️ ",
      r.error
    ] }),
    l && !b && /* @__PURE__ */ n.jsx(dc, { children: l })
  ] });
}, pc = (e = !1) => {
  const [t, r] = H(e), [o, i] = H(null), [a, s] = H(!1), l = z((x) => {
    r(x), x && (i(null), s(!1));
  }, []), d = z((x) => {
    i(x), r(!1), s(!1);
  }, []), f = z(() => {
    i(null);
  }, []), u = z(() => {
    r(!1), i(null), s(!1);
  }, []), m = z(async (x) => {
    l(!0);
    try {
      const h = await x();
      return s(!0), r(!1), h;
    } catch (h) {
      const b = h instanceof Error ? h.message : "An unexpected error occurred";
      throw d(b), h;
    }
  }, [l, d]), y = z((x) => async (...h) => {
    try {
      await m(() => x(...h));
    } catch (b) {
      console.error("Operation failed:", b);
    }
  }, [m]);
  return {
    // State
    isLoading: t,
    error: o,
    isSuccess: a,
    isError: o !== null,
    // Actions
    setLoading: l,
    setError: d,
    clearError: f,
    reset: u,
    withLoading: m,
    withLoadingCallback: y
  };
};
function kl(e, t = {}) {
  const {
    fetchOnMount: r = !0,
    dependencies: o = []
  } = t, [i, a] = H({
    data: null,
    isLoading: !1,
    error: null,
    isInitialized: !1
  }), s = z(async (...l) => {
    a((d) => ({
      ...d,
      isLoading: !0,
      error: null
    }));
    try {
      const d = await e(...l);
      return a({
        data: d,
        isLoading: !1,
        error: null,
        isInitialized: !0
      }), d;
    } catch (d) {
      const f = d instanceof Error ? d : new Error(String(d));
      throw a((u) => ({
        ...u,
        isLoading: !1,
        error: f,
        isInitialized: !0
      })), f;
    }
  }, [e]);
  return ae(() => {
    r && s();
  }, [r, s, ...o]), {
    ...i,
    fetchData: s,
    refetch: () => s()
  };
}
function _l(e, t) {
  const [r, o] = H(e);
  return ae(() => {
    const i = setTimeout(() => {
      o(e);
    }, t);
    return () => {
      clearTimeout(i);
    };
  }, [e, t]), r;
}
function Ml(e = {}) {
  const {
    componentName: t,
    logToConsole: r = !0,
    reportToMonitoring: o = !0,
    onError: i
  } = e, [a, s] = H(null), [l, d] = H(!1), f = z((y) => {
    if (s(y), d(!0), r) {
      const x = t ? `[${t}]` : "";
      console.error(`Error caught by useErrorHandler${x}:`, y);
    }
    i && i(y);
  }, [t, r, o, i]), u = z(() => {
    s(null), d(!1);
  }, []), m = z(async (y) => {
    try {
      return await y();
    } catch (x) {
      f(x);
      return;
    }
  }, [f]);
  return ae(() => () => {
    s(null), d(!1);
  }, []), {
    error: a,
    hasError: l,
    handleError: f,
    resetError: u,
    tryExecute: m
  };
}
function Tr(e, t) {
  const r = () => {
    if (typeof window > "u")
      return t;
    try {
      const s = window.localStorage.getItem(e);
      return s ? JSON.parse(s) : t;
    } catch (s) {
      return console.warn(`Error reading localStorage key "${e}":`, s), t;
    }
  }, [o, i] = H(r), a = (s) => {
    try {
      const l = s instanceof Function ? s(o) : s;
      i(l), typeof window < "u" && window.localStorage.setItem(e, JSON.stringify(l));
    } catch (l) {
      console.warn(`Error setting localStorage key "${e}":`, l);
    }
  };
  return ae(() => {
    const s = (l) => {
      l.key === e && l.newValue && i(JSON.parse(l.newValue));
    };
    return window.addEventListener("storage", s), () => window.removeEventListener("storage", s);
  }, [e]), [o, a];
}
function Pl(e) {
  const {
    totalItems: t,
    itemsPerPage: r = 10,
    initialPage: o = 1,
    persistKey: i
  } = e, [a, s] = i ? Tr(`${i}_page`, o) : H(o), [l, d] = i ? Tr(`${i}_itemsPerPage`, r) : H(r), f = q(() => Math.max(1, Math.ceil(t / l)), [t, l]), u = q(() => Math.min(Math.max(1, a), f), [a, f]);
  u !== a && s(u);
  const m = (u - 1) * l, y = Math.min(m + l - 1, t - 1), x = u > 1, h = u < f, b = q(() => {
    const j = [];
    if (f <= 5)
      for (let N = 1; N <= f; N++)
        j.push(N);
    else {
      let N = Math.max(1, u - Math.floor(2.5));
      const _ = Math.min(f, N + 5 - 1);
      _ === f && (N = Math.max(1, _ - 5 + 1));
      for (let R = N; R <= _; R++)
        j.push(R);
    }
    return j;
  }, [u, f]), E = z(() => {
    h && s(u + 1);
  }, [h, u, s]), S = z(() => {
    x && s(u - 1);
  }, [x, u, s]), C = z(($) => {
    const j = Math.min(Math.max(1, $), f);
    s(j);
  }, [f, s]), k = z(($) => {
    d($), s(1);
  }, [d, s]);
  return {
    currentPage: u,
    itemsPerPage: l,
    totalPages: f,
    hasPreviousPage: x,
    hasNextPage: h,
    startIndex: m,
    endIndex: y,
    pageRange: b,
    nextPage: E,
    previousPage: S,
    goToPage: C,
    setItemsPerPage: k
  };
}
const fc = (e, t = "$", r = !1) => {
  const i = Math.abs(e).toLocaleString("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
  return e > 0 ? r ? `+${t}${i}` : `${t}${i}` : e < 0 ? `-${t}${i}` : `${t}${i}`;
}, Ol = (e, t = {}) => {
  const {
    currency: r = "$",
    showPositiveSign: o = !1,
    customAriaLabel: i
  } = t;
  return q(() => {
    if (e == null)
      return {
        formattedAmount: "",
        isProfit: !1,
        isLoss: !1,
        isNeutral: !1,
        isEmpty: !0,
        ariaLabel: i || "No profit/loss data available"
      };
    const a = e > 0, s = e < 0, l = e === 0, d = fc(e, r, o), f = `${a ? "Profit" : s ? "Loss" : "Breakeven"} of ${d}`;
    return {
      formattedAmount: d,
      isProfit: a,
      isLoss: s,
      isNeutral: l,
      isEmpty: !1,
      ariaLabel: i || f
    };
  }, [e, r, o, i]);
}, gc = (e) => e == null ? !0 : Array.isArray(e) ? e.length === 0 : typeof e == "object" ? Object.keys(e).length === 0 : typeof e == "string" ? e.trim().length === 0 : !1, Fl = (e) => {
  const {
    fetchData: t,
    initialData: r = null,
    fetchOnMount: o = !0,
    refreshInterval: i,
    isEmpty: a = gc,
    transformError: s,
    dependencies: l = []
  } = e, [d, f] = H(r), [u, m] = H(null), y = pc(), x = q(() => d === null || a(d), [d, a]), h = z(async () => {
    try {
      const C = await y.withLoading(t);
      f(C), m(/* @__PURE__ */ new Date());
    } catch (C) {
      const k = s && C instanceof Error ? s(C) : C instanceof Error ? C.message : "Failed to fetch data";
      y.setError(k), console.error("Data fetch failed:", C);
    }
  }, [t, y, s]), b = z(async () => {
    await h();
  }, [h]), E = z(() => {
    f(r), m(null), y.reset();
  }, [r, y]), S = z((C) => {
    f(C), m(/* @__PURE__ */ new Date()), y.clearError();
  }, [y]);
  return ae(() => {
    o && h();
  }, [o, h]), ae(() => {
    l.length > 0 && u !== null && h();
  }, l), ae(() => {
    if (!i || i <= 0)
      return;
    const C = setInterval(() => {
      !y.isLoading && !y.error && h();
    }, i);
    return () => clearInterval(C);
  }, [i, y.isLoading, y.error, h]), {
    // State
    data: d,
    isLoading: y.isLoading,
    error: y.error,
    isEmpty: x,
    isSuccess: y.isSuccess,
    isError: y.isError,
    lastFetched: u,
    // Actions
    refresh: b,
    clearError: y.clearError,
    reset: E,
    setData: S
  };
}, $l = (e = "en-US") => q(() => ({
  formatCurrency: (d, f = {}) => {
    const {
      currency: u = "USD",
      locale: m = e,
      minimumFractionDigits: y = 2,
      maximumFractionDigits: x = 2,
      showPositiveSign: h = !1
    } = f, E = new Intl.NumberFormat(m, {
      style: "currency",
      currency: u,
      minimumFractionDigits: y,
      maximumFractionDigits: x
    }).format(Math.abs(d));
    return d > 0 && h ? `+${E}` : d < 0 ? `-${E}` : E;
  },
  formatPercent: (d, f = {}) => {
    const {
      locale: u = e,
      minimumFractionDigits: m = 2,
      maximumFractionDigits: y = 2,
      showPositiveSign: x = !1
    } = f, h = new Intl.NumberFormat(u, {
      style: "percent",
      minimumFractionDigits: m,
      maximumFractionDigits: y
    }), b = d > 1 ? d / 100 : d, E = h.format(Math.abs(b));
    return b > 0 && x ? `+${E}` : b < 0 ? `-${E}` : E;
  },
  formatNumber: (d, f = {}) => {
    const {
      locale: u = e,
      minimumFractionDigits: m = 0,
      maximumFractionDigits: y = 2,
      useGrouping: x = !0
    } = f;
    return new Intl.NumberFormat(u, {
      minimumFractionDigits: m,
      maximumFractionDigits: y,
      useGrouping: x
    }).format(d);
  },
  formatDate: (d, f = "medium") => {
    const u = typeof d == "string" ? new Date(d) : d;
    return new Intl.DateTimeFormat(e, {
      dateStyle: f
    }).format(u);
  },
  formatTime: (d, f = "short") => {
    const u = typeof d == "string" ? new Date(d) : d;
    return new Intl.DateTimeFormat(e, {
      timeStyle: f
    }).format(u);
  },
  formatRelativeTime: (d) => {
    const f = typeof d == "string" ? new Date(d) : d, m = Math.floor(((/* @__PURE__ */ new Date()).getTime() - f.getTime()) / 1e3);
    if (typeof Intl.RelativeTimeFormat < "u") {
      const b = new Intl.RelativeTimeFormat(e, {
        numeric: "auto"
      }), E = [{
        unit: "year",
        seconds: 31536e3
      }, {
        unit: "month",
        seconds: 2592e3
      }, {
        unit: "day",
        seconds: 86400
      }, {
        unit: "hour",
        seconds: 3600
      }, {
        unit: "minute",
        seconds: 60
      }, {
        unit: "second",
        seconds: 1
      }];
      for (const S of E) {
        const C = Math.floor(Math.abs(m) / S.seconds);
        if (C >= 1)
          return b.format(m > 0 ? -C : C, S.unit);
      }
      return b.format(0, "second");
    }
    const y = Math.abs(m), x = m < 0;
    if (y < 60)
      return x ? "in a few seconds" : "a few seconds ago";
    if (y < 3600) {
      const b = Math.floor(y / 60);
      return x ? `in ${b} minute${b > 1 ? "s" : ""}` : `${b} minute${b > 1 ? "s" : ""} ago`;
    }
    if (y < 86400) {
      const b = Math.floor(y / 3600);
      return x ? `in ${b} hour${b > 1 ? "s" : ""}` : `${b} hour${b > 1 ? "s" : ""} ago`;
    }
    const h = Math.floor(y / 86400);
    return x ? `in ${h} day${h > 1 ? "s" : ""}` : `${h} day${h > 1 ? "s" : ""} ago`;
  }
}), [e]), mc = {
  small: g(["font-size:", ";padding:", " ", ";"], ({
    theme: e
  }) => {
    var t;
    return ((t = e.fontSizes) == null ? void 0 : t.xs) || "12px";
  }, ({
    theme: e
  }) => {
    var t;
    return ((t = e.spacing) == null ? void 0 : t.xxs) || "2px";
  }, ({
    theme: e
  }) => {
    var t;
    return ((t = e.spacing) == null ? void 0 : t.xs) || "4px";
  }),
  medium: g(["font-size:", ";padding:", " ", ";"], ({
    theme: e
  }) => {
    var t;
    return ((t = e.fontSizes) == null ? void 0 : t.sm) || "14px";
  }, ({
    theme: e
  }) => {
    var t;
    return ((t = e.spacing) == null ? void 0 : t.xs) || "4px";
  }, ({
    theme: e
  }) => {
    var t;
    return ((t = e.spacing) == null ? void 0 : t.sm) || "8px";
  }),
  large: g(["font-size:", ";padding:", " ", ";"], ({
    theme: e
  }) => {
    var t;
    return ((t = e.fontSizes) == null ? void 0 : t.lg) || "18px";
  }, ({
    theme: e
  }) => {
    var t;
    return ((t = e.spacing) == null ? void 0 : t.sm) || "8px";
  }, ({
    theme: e
  }) => {
    var t;
    return ((t = e.spacing) == null ? void 0 : t.md) || "12px";
  })
}, hc = {
  profit: g(["color:", ";background-color:", ";border:1px solid ", ";"], ({
    theme: e
  }) => {
    var t, r;
    return ((t = e.colors) == null ? void 0 : t.profit) || ((r = e.colors) == null ? void 0 : r.success) || "#4caf50";
  }, ({
    theme: e
  }) => {
    var t;
    return (t = e.colors) != null && t.profit ? `${e.colors.profit}15` : "rgba(76, 175, 80, 0.1)";
  }, ({
    theme: e
  }) => {
    var t;
    return (t = e.colors) != null && t.profit ? `${e.colors.profit}30` : "rgba(76, 175, 80, 0.2)";
  }),
  loss: g(["color:", ";background-color:", ";border:1px solid ", ";"], ({
    theme: e
  }) => {
    var t, r;
    return ((t = e.colors) == null ? void 0 : t.loss) || ((r = e.colors) == null ? void 0 : r.error) || "#f44336";
  }, ({
    theme: e
  }) => {
    var t;
    return (t = e.colors) != null && t.loss ? `${e.colors.loss}15` : "rgba(244, 67, 54, 0.1)";
  }, ({
    theme: e
  }) => {
    var t;
    return (t = e.colors) != null && t.loss ? `${e.colors.loss}30` : "rgba(244, 67, 54, 0.2)";
  }),
  neutral: g(["color:", ";background-color:", ";border:1px solid ", ";"], ({
    theme: e
  }) => {
    var t, r;
    return ((t = e.colors) == null ? void 0 : t.neutral) || ((r = e.colors) == null ? void 0 : r.textSecondary) || "#757575";
  }, ({
    theme: e
  }) => {
    var t;
    return (t = e.colors) != null && t.neutral ? `${e.colors.neutral}15` : "rgba(117, 117, 117, 0.1)";
  }, ({
    theme: e
  }) => {
    var t;
    return (t = e.colors) != null && t.neutral ? `${e.colors.neutral}30` : "rgba(117, 117, 117, 0.2)";
  }),
  default: g(["color:", ";background-color:transparent;border:1px solid transparent;"], ({
    theme: e
  }) => {
    var t;
    return ((t = e.colors) == null ? void 0 : t.textPrimary) || "#ffffff";
  })
}, Al = /* @__PURE__ */ g(["display:inline-flex;align-items:center;justify-content:flex-end;font-weight:", ";font-family:", ";transition:", ";border-radius:", ";&:hover{transform:translateY(-1px);box-shadow:", ";}"], ({
  theme: e
}) => {
  var t;
  return ((t = e.fontWeights) == null ? void 0 : t.semibold) || "600";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.fontFamilies) == null ? void 0 : t.mono) || "monospace";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.transitions) == null ? void 0 : t.fast) || "all 0.2s ease";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.borderRadius) == null ? void 0 : t.sm) || "4px";
}, ({
  theme: e
}) => {
  var t;
  return ((t = e.shadows) == null ? void 0 : t.sm) || "0 2px 4px rgba(0, 0, 0, 0.1)";
}), zl = /* @__PURE__ */ g(["opacity:0.6;position:relative;&::after{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);animation:shimmer 1.5s infinite;}@keyframes shimmer{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}"]), Vl = (e) => mc[e], Gl = (e, t, r) => e ? "profit" : t ? "loss" : r ? "neutral" : "default", Bl = (e) => hc[e], Lt = {
  name: "mercedes-green",
  colors: {
    // Primary colors - Mercedes Green for active/positive states
    primary: w.f1MercedesGreen,
    primaryDark: w.f1MercedesGreenDark,
    primaryLight: w.f1MercedesGreenLight,
    // Secondary colors - Racing Blue for information
    secondary: w.f1Blue,
    secondaryDark: w.f1BlueDark,
    secondaryLight: w.f1BlueLight,
    // Accent colors - McLaren Orange for transitions
    accent: w.f1McLarenOrange,
    accentDark: w.f1McLarenOrangeDark,
    accentLight: w.f1McLarenOrangeLight,
    // F1 Racing Status Colors
    success: w.f1MercedesGreen,
    // Green flag - optimal performance
    warning: w.f1McLarenOrange,
    // Orange flag - caution/transitions
    error: w.f1Red,
    // Red flag - critical alerts only
    danger: w.f1Red,
    // Ferrari red for genuine danger
    info: w.f1Blue,
    // Racing blue for information
    // Neutral colors
    background: ie.background,
    surface: ie.surface,
    elevated: w.gray700,
    // Added elevated color for F1 theme
    cardBackground: ie.surface,
    border: ie.border,
    divider: "rgba(255, 255, 255, 0.1)",
    // Text colors
    textPrimary: ie.textPrimary,
    textSecondary: ie.textSecondary,
    textDisabled: ie.textDisabled,
    textInverse: ie.textInverse,
    // Chart colors
    chartGrid: ie.chartGrid,
    chartLine: ie.chartLine,
    chartAxis: w.gray400,
    chartTooltip: ie.tooltipBackground,
    // F1 Racing Trading Colors
    profit: w.f1MercedesGreen,
    // Green flag for profitable trades
    loss: w.f1Red,
    // Red flag for losses only
    neutral: w.f1Silver,
    // Silver for neutral data
    // F1 Racing Tab Colors
    tabActive: w.f1MercedesGreen,
    // Active tabs use Mercedes green
    tabInactive: w.gray600,
    // Component specific colors
    tooltipBackground: ie.tooltipBackground,
    modalBackground: ie.modalBackground,
    sidebarBackground: w.gray800,
    headerBackground: "rgba(0, 0, 0, 0.2)",
    // F1 Racing Session States
    sessionActive: w.f1MercedesGreen,
    // Active sessions - green flag
    sessionOptimal: w.f1MercedesGreenLight,
    // Optimal windows - bright green
    sessionCaution: w.f1RacingYellow,
    // Caution periods - yellow flag
    sessionTransition: w.f1McLarenOrange,
    // Transition periods - orange
    sessionInactive: w.gray600,
    // Inactive sessions - neutral
    // F1 Racing Performance States
    performanceExcellent: w.f1MercedesGreen,
    performanceGood: w.f1Blue,
    performanceAverage: w.f1Silver,
    performancePoor: w.f1McLarenOrange,
    performanceAvoid: w.f1Red
  },
  spacing: X,
  breakpoints: ot,
  fontSizes: pe,
  fontWeights: et,
  lineHeights: tt,
  fontFamilies: rt,
  borderRadius: nt,
  shadows: it,
  transitions: st,
  zIndex: at
}, xc = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  f1Theme: Lt
}, Symbol.toStringTag, { value: "Module" })), St = {
  name: "f1-official",
  colors: {
    // F1 Brand Colors - Official Red Primary
    primary: "#e10600",
    primaryDark: "#b30500",
    primaryLight: "#ff1e1e",
    // F1 Secondary Colors - Deep Navy
    secondary: "#15151e",
    secondaryDark: "#0f0f17",
    secondaryLight: "#1e1e2e",
    // F1 Accent Colors - Championship Gold
    accent: "#ffd700",
    accentDark: "#e6be1d",
    accentLight: "#ffdc4a",
    // F1 Status Colors - Timing Screen Colors
    success: "#00ff41",
    // F1 timing green (sector improvements)
    warning: "#ffd700",
    // F1 yellow flags
    error: "#ff1e1e",
    // F1 timing red (sector losses)
    danger: "#ff1e1e",
    // F1 danger red (same as error for F1 theme)
    info: "#00b4d8",
    // F1 information blue
    // Background Colors - F1 App Style
    background: "#15151e",
    surface: "#1e1e2e",
    cardBackground: "#2a2a3a",
    elevated: "#353545",
    // Border Colors
    border: "#3a3a4a",
    divider: "#4a4a5a",
    // Text Colors - High Contrast F1 Style
    textPrimary: "#ffffff",
    textSecondary: "#b8b8c8",
    textDisabled: "#8b8b9b",
    textInverse: "#15151e",
    // Chart Colors
    chartGrid: "rgba(255, 255, 255, 0.1)",
    chartLine: "#e10600",
    chartAxis: "#b8b8c8",
    chartTooltip: "rgba(42, 42, 58, 0.9)",
    // Trading Colors
    profit: "#00ff41",
    // F1 timing green
    loss: "#ff1e1e",
    // F1 timing red
    neutral: "#b8b8c8",
    // Tab Colors
    tabActive: "#e10600",
    tabInactive: "#8b8b9b",
    // Component Colors
    tooltipBackground: "rgba(42, 42, 58, 0.9)",
    modalBackground: "rgba(21, 21, 30, 0.8)",
    sidebarBackground: "#1e1e2e",
    headerBackground: "rgba(21, 21, 30, 0.9)",
    // F1 Session States
    sessionActive: "#e10600",
    // F1 red for active sessions
    sessionOptimal: "#ffd700",
    // Gold for optimal windows
    sessionCaution: "#ff8700",
    // Orange for caution periods
    sessionTransition: "#00b4d8",
    // Blue for transitions
    sessionInactive: "#8b8b9b",
    // Muted for inactive
    // F1 Performance States
    performanceExcellent: "#00ff41",
    // Timing green
    performanceGood: "#ffd700",
    // Championship gold
    performanceAverage: "#ff8700",
    // Warning orange
    performancePoor: "#ff1e1e",
    // Timing red
    performanceAvoid: "#8b8b9b"
    // Muted gray
  },
  spacing: X,
  breakpoints: ot,
  fontSizes: pe,
  fontWeights: et,
  lineHeights: tt,
  fontFamilies: rt,
  borderRadius: nt,
  shadows: it,
  transitions: st,
  zIndex: at
}, yc = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: St,
  f1OfficialTheme: St
}, Symbol.toStringTag, { value: "Module" })), bc = {
  name: "f1-official",
  colors: {
    // F1 Official Primary Colors
    primary: w.f1Red,
    // Official F1 red
    primaryDark: w.f1RedDark,
    primaryLight: w.f1RedLight,
    // F1 Racing Blue for information
    secondary: w.f1Blue,
    secondaryDark: w.f1BlueDark,
    secondaryLight: w.f1BlueLight,
    // F1 Yellow accent colors
    accent: "#FFD700",
    // F1 yellow flags
    accentDark: "#E6C200",
    accentLight: "#FFF700",
    // F1 Racing Status Colors (like live timing)
    success: "#00FF41",
    // Bright green like F1 timing gains
    warning: "#FFD700",
    // F1 yellow flags
    error: "#FF1E1E",
    // Bright red like F1 timing losses
    danger: "#FF1E1E",
    // F1 red flags
    info: "#00B4D8",
    // F1 blue information
    // F1 Official Background Colors (deep charcoal like driver tracker)
    background: "#15151E",
    // Deep F1 black
    surface: "#1E1E2E",
    // Slightly lighter panels
    elevated: "#2A2A3A",
    // Card backgrounds
    cardBackground: "#2A2A3A",
    // F1 data cards
    border: "#3A3A4A",
    // Subtle borders
    divider: "rgba(255, 255, 255, 0.1)",
    // F1 Official Text Colors
    textPrimary: "#FFFFFF",
    // Primary white text
    textSecondary: "#B8B8C8",
    // Secondary gray text
    textDisabled: "#8B8B9B",
    // Muted text
    textInverse: "#15151E",
    // Inverse text for light backgrounds
    // F1 Official Chart Colors (like live timing displays)
    chartGrid: "rgba(255, 255, 255, 0.1)",
    chartLine: w.f1Red,
    chartAxis: "#B8B8C8",
    chartTooltip: "rgba(21, 21, 30, 0.95)",
    // F1 Racing Trading Colors (like timing screens)
    profit: "#00FF41",
    // Bright green like F1 timing gains
    loss: "#FF1E1E",
    // Bright red like F1 timing losses
    neutral: "#FFD700",
    // F1 yellow for neutral
    // F1 Official Tab Colors
    tabActive: w.f1Red,
    // F1 red for active tabs
    tabInactive: "#8B8B9B",
    // Muted gray for inactive
    // F1 Official Component Colors
    tooltipBackground: "rgba(21, 21, 30, 0.95)",
    modalBackground: "rgba(21, 21, 30, 0.9)",
    sidebarBackground: "#1A1A24",
    // F1 sidebar background
    headerBackground: "rgba(21, 21, 30, 0.95)",
    // F1 Official Session States (like race status)
    sessionActive: "#00FF41",
    // Bright green like F1 timing
    sessionOptimal: "#9D4EDD",
    // Purple for fastest like F1
    sessionCaution: "#FFD700",
    // F1 yellow flags
    sessionTransition: "#00B4D8",
    // F1 blue for transitions
    sessionInactive: "#8B8B9B",
    // Muted gray
    // F1 Official Performance States (like driver performance)
    performanceExcellent: "#00FF41",
    // Bright green like fastest lap
    performanceGood: "#00B4D8",
    // F1 blue for good performance
    performanceAverage: "#FFD700",
    // F1 yellow for average
    performancePoor: "#FF8700",
    // Orange for poor performance
    performanceAvoid: "#FF1E1E"
    // Bright red for avoid
  },
  spacing: X,
  breakpoints: ot,
  fontSizes: pe,
  fontWeights: et,
  lineHeights: tt,
  fontFamilies: rt,
  borderRadius: nt,
  shadows: it,
  transitions: st,
  zIndex: at
}, ql = bc, Mr = {
  name: "mercedes-dark",
  colors: {
    // Mercedes-inspired primary colors (Silver/Teal accents)
    primary: w.f1Silver,
    primaryDark: w.gray500,
    primaryLight: w.gray300,
    // Secondary colors - Subtle Mercedes teal
    secondary: w.f1MercedesGreenDark,
    secondaryDark: "#006B5D",
    secondaryLight: w.f1MercedesGreen,
    // Accent colors - Mercedes silver
    accent: w.f1Silver,
    accentDark: w.gray500,
    accentLight: w.gray300,
    // Mercedes-themed status colors
    success: w.f1MercedesGreen,
    // Mercedes green for success
    warning: w.f1Silver,
    // Silver for warnings
    error: w.red,
    // Keep red for errors
    danger: w.red,
    // Keep red for danger
    info: w.f1MercedesGreenDark,
    // Dark teal for info
    // Neutral colors
    background: w.gray900,
    // Slightly different from F1 theme
    surface: w.gray800,
    elevated: w.gray700,
    // Added elevated color for dark theme
    cardBackground: w.gray800,
    border: w.gray700,
    divider: "rgba(255, 255, 255, 0.1)",
    // Text colors - Improved contrast for dark theme
    textPrimary: w.white,
    // #ffffff - High contrast
    textSecondary: w.gray200,
    // #e5e7eb - Better contrast than gray300
    textDisabled: w.gray400,
    // #9ca3af - More visible than gray500
    textInverse: w.gray900,
    // Chart colors
    chartGrid: ie.chartGrid,
    chartLine: w.f1Blue,
    // Using blue instead of red
    chartAxis: w.gray400,
    chartTooltip: ie.tooltipBackground,
    // Mercedes-themed trading colors
    profit: w.f1MercedesGreen,
    // Mercedes green for profits
    loss: w.red,
    // Keep red for losses
    neutral: w.f1Silver,
    // Mercedes silver for neutral
    // Mercedes tab colors
    tabActive: w.f1Silver,
    // Silver for active tabs
    tabInactive: w.gray600,
    // Component specific colors
    tooltipBackground: "rgba(26, 32, 44, 0.9)",
    // Slightly different from F1 theme
    modalBackground: "rgba(26, 32, 44, 0.8)",
    sidebarBackground: w.gray900,
    headerBackground: "rgba(0, 0, 0, 0.3)",
    // Mercedes-themed session states
    sessionActive: w.f1MercedesGreen,
    // Mercedes green for active
    sessionOptimal: w.f1MercedesGreenLight,
    // Bright Mercedes green
    sessionCaution: w.f1Silver,
    // Silver for caution
    sessionTransition: w.gray300,
    // Light gray for transitions
    sessionInactive: w.gray600,
    // Gray for inactive
    // Mercedes performance states
    performanceExcellent: w.f1MercedesGreen,
    performanceGood: w.f1Silver,
    performanceAverage: w.gray400,
    performancePoor: w.gray500,
    performanceAvoid: w.red
  },
  spacing: X,
  breakpoints: ot,
  fontSizes: pe,
  fontWeights: et,
  lineHeights: tt,
  fontFamilies: rt,
  borderRadius: nt,
  shadows: it,
  transitions: st,
  zIndex: at
}, vc = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  darkTheme: Mr
}, Symbol.toStringTag, { value: "Module" })), wc = /* @__PURE__ */ go(["*,*::before,*::after{box-sizing:border-box;}html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block;}body{line-height:1.5;font-family:", ";background-color:", ";color:", ";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}ol,ul{list-style:none;}blockquote,q{quotes:none;}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}table{border-collapse:collapse;border-spacing:0;}a{color:", ";text-decoration:none;&:hover{text-decoration:underline;}}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit;}::-webkit-scrollbar{width:8px;height:8px;}::-webkit-scrollbar-track{background:", ";}::-webkit-scrollbar-thumb{background:", ";border-radius:4px;}::-webkit-scrollbar-thumb:hover{background:", ";}:focus{outline:2px solid ", ";outline-offset:2px;}::selection{background-color:", ";color:", ";}"], ({
  theme: e
}) => e.fontFamilies.body, ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.textInverse), Sc = wc, Cc = {
  "mercedes-green": Lt,
  "f1-official": St,
  dark: Mr
}, kt = Lt, Ec = (e) => e === "f1" || e === "formula1" || e === "formula-1" ? "mercedes-green" : e === "light" ? "f1-official" : e, bt = (e) => {
  const t = Ec(e);
  return Cc[t] || kt;
}, Pr = Rr({
  theme: kt,
  setTheme: () => {
  }
}), Hl = () => jr(Pr), Ul = ({
  initialTheme: e = kt,
  persistTheme: t = !0,
  storageKey: r = "adhd-dashboard-theme",
  children: o
}) => {
  const [i, a] = H(() => {
    if (t && typeof window < "u") {
      const f = window.localStorage.getItem(r);
      if (f)
        try {
          const u = bt(f);
          return u || JSON.parse(f);
        } catch (u) {
          console.error("Failed to parse stored theme:", u);
        }
    }
    return typeof e == "string" ? bt(e) : e;
  });
  ae(() => {
    typeof document < "u" && document.documentElement.setAttribute("data-theme", i.name);
  }, [i.name]);
  const s = (d) => {
    const f = typeof d == "string" ? bt(d) : d;
    a(f), t && typeof window < "u" && window.localStorage.setItem(r, f.name || JSON.stringify(f));
  }, l = ({
    children: d
  }) => /* @__PURE__ */ n.jsxs(mo, { theme: i, children: [
    /* @__PURE__ */ n.jsx(Sc, {}),
    d
  ] });
  return /* @__PURE__ */ n.jsx(Pr.Provider, { value: {
    theme: i,
    setTheme: s
  }, children: /* @__PURE__ */ n.jsx(l, { children: o }) });
};
function Tc(e) {
  const t = [];
  return t.push(`[data-theme="${e.name}"] {`), Object.entries(e.colors).forEach(([r, o]) => {
    t.push(`  --color-${Rc(r)}: ${o};`);
  }), Object.entries(e.spacing).forEach(([r, o]) => {
    t.push(`  --spacing-${r}: ${o};`);
  }), Object.entries(e.fontSizes).forEach(([r, o]) => {
    t.push(`  --font-size-${r}: ${o};`);
  }), Object.entries(e.fontWeights).forEach(([r, o]) => {
    t.push(`  --font-weight-${r}: ${o};`);
  }), Object.entries(e.fontFamilies).forEach(([r, o]) => {
    t.push(`  --font-family-${r}: ${o};`);
  }), Object.entries(e.borderRadius).forEach(([r, o]) => {
    t.push(`  --border-radius-${r}: ${o};`);
  }), Object.entries(e.shadows).forEach(([r, o]) => {
    t.push(`  --shadow-${r}: ${o};`);
  }), Object.entries(e.transitions).forEach(([r, o]) => {
    t.push(`  --transition-${r}: ${o};`);
  }), Object.entries(e.zIndex).forEach(([r, o]) => {
    t.push(`  --z-index-${r}: ${o};`);
  }), t.push("}"), t.join(`
`);
}
function Ic(e) {
  const t = [];
  return t.push(`[data-theme="${e.name}"] {`), t.push("  /* Component Semantic Variables */"), t.push("  --primary-color: var(--color-primary);"), t.push("  --secondary-color: var(--color-secondary);"), t.push("  --accent-color: var(--color-accent);"), t.push("  --success-color: var(--color-success);"), t.push("  --warning-color: var(--color-warning);"), t.push("  --error-color: var(--color-error);"), t.push("  --info-color: var(--color-info);"), t.push("  --bg-primary: var(--color-background);"), t.push("  --bg-secondary: var(--color-surface);"), t.push("  --bg-card: var(--color-card-background);"), t.push("  --bg-elevated: var(--color-elevated);"), t.push("  --text-primary: var(--color-text-primary);"), t.push("  --text-secondary: var(--color-text-secondary);"), t.push("  --text-disabled: var(--color-text-disabled);"), t.push("  --text-inverse: var(--color-text-inverse);"), t.push("  --border-primary: var(--color-border);"), t.push("  --border-secondary: var(--color-divider);"), t.push("  --session-card-bg: var(--bg-card);"), t.push("  --session-card-border: var(--border-primary);"), t.push("  --session-card-accent: var(--primary-color);"), t.push("  --session-active: var(--color-session-active);"), t.push("  --session-optimal: var(--color-session-optimal);"), t.push("  --session-caution: var(--color-session-caution);"), t.push("  --session-transition: var(--color-session-transition);"), t.push("  --session-inactive: var(--color-session-inactive);"), t.push("}"), t.join(`
`);
}
function Rc(e) {
  return e.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, "$1-$2").toLowerCase();
}
function Yl(e) {
  const t = [];
  return t.push(`/**
 * Generated Theme CSS Variables
 * 
 * This file is auto-generated from theme definitions.
 * Do not edit manually - changes will be overwritten.
 */`), Object.values(e).forEach((r) => {
    t.push(""), t.push(`/* ${r.name} Theme */`), t.push(Tc(r)), t.push(""), t.push(Ic(r));
  }), t.join(`
`);
}
const Wl = {
  "mercedes-green": () => Promise.resolve().then(() => xc).then((e) => e.f1Theme),
  "f1-official": () => Promise.resolve().then(() => yc).then((e) => e.f1OfficialTheme),
  dark: () => Promise.resolve().then(() => vc).then((e) => e.darkTheme)
};
function Kl(e, t, r = "StoreContext") {
  const o = Rr(void 0);
  o.displayName = r;
  const i = ({
    children: f,
    initialState: u
  }) => {
    const [m, y] = fo(e, u || t), x = q(() => ({
      state: m,
      dispatch: y
    }), [m]);
    return /* @__PURE__ */ n.jsx(o.Provider, { value: x, children: f });
  };
  function a() {
    const f = jr(o);
    if (f === void 0)
      throw new Error(`use${r} must be used within a ${r}Provider`);
    return f;
  }
  function s(f) {
    const {
      state: u
    } = a();
    return f(u);
  }
  function l(f) {
    const {
      dispatch: u
    } = a();
    return q(() => (...m) => {
      u(f(...m));
    }, [u, f]);
  }
  function d(f) {
    const {
      dispatch: u
    } = a();
    return q(() => {
      const m = {};
      for (const y in f)
        m[y] = (...x) => {
          u(f[y](...x));
        };
      return m;
    }, [u, f]);
  }
  return {
    Context: o,
    Provider: i,
    useStore: a,
    useSelector: s,
    useAction: l,
    useActions: d
  };
}
function Ql(...e) {
  const t = e.pop(), r = e;
  let o = null, i = null;
  return (a) => {
    const s = r.map((l) => l(a));
    return (o === null || s.length !== o.length || s.some((l, d) => l !== o[d])) && (i = t(...s), o = s), i;
  };
}
function Xl(e, t) {
  const {
    key: r,
    initialState: o,
    version: i = 1,
    migrate: a,
    serialize: s = JSON.stringify,
    deserialize: l = JSON.parse,
    filter: d = (S) => S,
    merge: f = (S, C) => ({
      ...C,
      ...S
    }),
    debug: u = !1
  } = t, m = () => {
    try {
      const S = localStorage.getItem(r);
      if (S === null)
        return null;
      const {
        state: C,
        version: k
      } = l(S);
      return k !== i && a ? (u && console.log(`Migrating state from version ${k} to ${i}`), a(C, k)) : C;
    } catch (S) {
      return u && console.error("Error loading state from local storage:", S), null;
    }
  }, y = (S) => {
    try {
      const C = d(S), k = s({
        state: C,
        version: i
      });
      localStorage.setItem(r, k);
    } catch (C) {
      u && console.error("Error saving state to local storage:", C);
    }
  }, x = () => {
    try {
      localStorage.removeItem(r);
    } catch (S) {
      u && console.error("Error clearing state from local storage:", S);
    }
  }, h = m(), b = h ? f(h, o) : o;
  return u && h && (console.log("Loaded persisted state:", h), console.log("Merged initial state:", b)), {
    reducer: (S, C) => {
      const k = e(S, C);
      return y(k), k;
    },
    initialState: b,
    clear: x
  };
}
function Jl(e, t = "$") {
  return `${t}${e.toFixed(2)}`;
}
function Zl(e, t = 1) {
  return `${(e * 100).toFixed(t)}%`;
}
function ed(e, t = "short") {
  const r = typeof e == "string" ? new Date(e) : e;
  switch (t) {
    case "medium":
      return r.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric"
      });
    case "long":
      return r.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric"
      });
    case "short":
    default:
      return r.toLocaleDateString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit"
      });
  }
}
function td(e, t = 50) {
  return e.length <= t ? e : `${e.substring(0, t - 3)}...`;
}
function rd() {
  return Math.random().toString(36).substring(2, 9);
}
function od(e, t) {
  let r = null;
  return function(...o) {
    const i = () => {
      r = null, e(...o);
    };
    r && clearTimeout(r), r = setTimeout(i, t);
  };
}
function nd(e, t) {
  let r = !1;
  return function(...o) {
    r || (e(...o), r = !0, setTimeout(() => {
      r = !1;
    }, t));
  };
}
function id(e = {}) {
  console.log("Monitoring service initialized", e);
}
function sd(e, t) {
  console.error("Error captured by monitoring service:", e, t);
}
function ad(e) {
  console.log("User set for monitoring service:", e);
}
function cd(e, t) {
  const r = performance.now();
  return {
    name: e,
    startTime: r,
    finish: () => {
      const i = performance.now() - r;
      console.log(`Transaction "${e}" finished in ${i.toFixed(2)}ms`, t);
    }
  };
}
const Y = {
  MODEL_TYPE: "model_type",
  WIN_LOSS: "win_loss",
  R_MULTIPLE: "r_multiple",
  DATE: "date",
  SESSION: "session",
  DIRECTION: "direction",
  MARKET: "market",
  ACHIEVED_PL: "achieved_pl",
  PATTERN_QUALITY_RATING: "pattern_quality_rating"
};
class jc {
  constructor() {
    Se(this, "dbName", "adhd-trading-dashboard");
    Se(this, "version", 2);
    // Increment version for schema changes
    Se(this, "db", null);
    // Store names for different tables
    Se(this, "stores", {
      trades: "trades",
      fvg_details: "trade_fvg_details",
      setups: "trade_setups",
      analysis: "trade_analysis",
      sessions: "trading_sessions"
    });
  }
  /**
   * Initialize the database with new schema
   * @returns A promise that resolves when the database is initialized
   */
  async initDB() {
    return this.db ? this.db : new Promise((t, r) => {
      const o = indexedDB.open(this.dbName, this.version);
      o.onupgradeneeded = (i) => {
        var s;
        const a = i.target.result;
        if (!a.objectStoreNames.contains(this.stores.trades)) {
          const l = a.createObjectStore(this.stores.trades, {
            keyPath: "id",
            autoIncrement: !0
          });
          l.createIndex(Y.DATE, Y.DATE, {
            unique: !1
          }), l.createIndex(Y.MODEL_TYPE, Y.MODEL_TYPE, {
            unique: !1
          }), l.createIndex(Y.SESSION, Y.SESSION, {
            unique: !1
          }), l.createIndex(Y.WIN_LOSS, Y.WIN_LOSS, {
            unique: !1
          }), l.createIndex(Y.R_MULTIPLE, Y.R_MULTIPLE, {
            unique: !1
          });
        }
        if (a.objectStoreNames.contains(this.stores.fvg_details) || a.createObjectStore(this.stores.fvg_details, {
          keyPath: "id",
          autoIncrement: !0
        }).createIndex("trade_id", "trade_id", {
          unique: !1
        }), a.objectStoreNames.contains(this.stores.setups) || a.createObjectStore(this.stores.setups, {
          keyPath: "id",
          autoIncrement: !0
        }).createIndex("trade_id", "trade_id", {
          unique: !1
        }), a.objectStoreNames.contains(this.stores.analysis) || a.createObjectStore(this.stores.analysis, {
          keyPath: "id",
          autoIncrement: !0
        }).createIndex("trade_id", "trade_id", {
          unique: !1
        }), !a.objectStoreNames.contains(this.stores.sessions)) {
          a.createObjectStore(this.stores.sessions, {
            keyPath: "id",
            autoIncrement: !0
          }).createIndex("name", "name", {
            unique: !0
          });
          const d = [{
            name: "Pre-Market",
            start_time: "04:00:00",
            end_time: "09:30:00",
            description: "Pre-market trading hours"
          }, {
            name: "NY Open",
            start_time: "09:30:00",
            end_time: "10:30:00",
            description: "New York opening hour"
          }, {
            name: "10:50-11:10",
            start_time: "10:50:00",
            end_time: "11:10:00",
            description: "Mid-morning macro window"
          }, {
            name: "11:50-12:10",
            start_time: "11:50:00",
            end_time: "12:10:00",
            description: "Pre-lunch macro window"
          }, {
            name: "Lunch Macro",
            start_time: "12:00:00",
            end_time: "13:30:00",
            description: "Lunch time trading"
          }, {
            name: "13:50-14:10",
            start_time: "13:50:00",
            end_time: "14:10:00",
            description: "Post-lunch macro window"
          }, {
            name: "14:50-15:10",
            start_time: "14:50:00",
            end_time: "15:10:00",
            description: "Pre-close macro window"
          }, {
            name: "15:15-15:45",
            start_time: "15:15:00",
            end_time: "15:45:00",
            description: "Late afternoon window"
          }, {
            name: "MOC",
            start_time: "15:45:00",
            end_time: "16:00:00",
            description: "Market on close"
          }, {
            name: "Post MOC",
            start_time: "16:00:00",
            end_time: "20:00:00",
            description: "After hours trading"
          }];
          (s = o.transaction) == null || s.addEventListener("complete", () => {
            const u = a.transaction([this.stores.sessions], "readwrite").objectStore(this.stores.sessions);
            d.forEach((m) => u.add(m));
          });
        }
      }, o.onsuccess = (i) => {
        this.db = i.target.result, t(this.db);
      }, o.onerror = (i) => {
        console.error("Error opening IndexedDB:", i), r(new Error("Failed to open IndexedDB"));
      };
    });
  }
  /**
   * Save a complete trade with all related details
   * @param tradeData Complete trade data including all related tables
   * @returns A promise that resolves with the saved trade ID
   */
  async saveTradeWithDetails(t) {
    try {
      const r = await this.initDB();
      return new Promise((o, i) => {
        const a = r.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readwrite");
        a.onerror = (f) => {
          console.error("Transaction error:", f), i(new Error("Failed to save trade with details"));
        };
        const s = a.objectStore(this.stores.trades), l = {
          ...t.trade,
          created_at: (/* @__PURE__ */ new Date()).toISOString(),
          updated_at: (/* @__PURE__ */ new Date()).toISOString()
        }, d = s.add(l);
        d.onsuccess = () => {
          const f = d.result, u = [];
          if (t.fvg_details) {
            const m = a.objectStore(this.stores.fvg_details), y = {
              ...t.fvg_details,
              trade_id: f
            };
            u.push(new Promise((x, h) => {
              const b = m.add(y);
              b.onsuccess = () => x(), b.onerror = () => h(new Error("Failed to save FVG details"));
            }));
          }
          if (t.setup) {
            const m = a.objectStore(this.stores.setups), y = {
              ...t.setup,
              trade_id: f
            };
            u.push(new Promise((x, h) => {
              const b = m.add(y);
              b.onsuccess = () => x(), b.onerror = () => h(new Error("Failed to save setup data"));
            }));
          }
          if (t.analysis) {
            const m = a.objectStore(this.stores.analysis), y = {
              ...t.analysis,
              trade_id: f
            };
            u.push(new Promise((x, h) => {
              const b = m.add(y);
              b.onsuccess = () => x(), b.onerror = () => h(new Error("Failed to save analysis data"));
            }));
          }
          a.oncomplete = () => {
            o(f);
          };
        }, d.onerror = (f) => {
          console.error("Error saving trade:", f), i(new Error("Failed to save trade"));
        };
      });
    } catch (r) {
      throw console.error("Error in saveTradeWithDetails:", r), new Error("Failed to save trade with details");
    }
  }
  /**
   * Get a complete trade by ID with all related data
   * @param id The ID of the trade to get
   * @returns A promise that resolves with the complete trade data
   */
  async getTradeById(t) {
    try {
      const r = await this.initDB();
      return new Promise((o, i) => {
        const a = r.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readonly"), l = a.objectStore(this.stores.trades).get(t);
        l.onsuccess = () => {
          const d = l.result;
          if (!d) {
            o(null);
            return;
          }
          const f = {
            trade: d
          }, y = a.objectStore(this.stores.fvg_details).index("trade_id").get(t);
          y.onsuccess = () => {
            y.result && (f.fvg_details = y.result);
            const b = a.objectStore(this.stores.setups).index("trade_id").get(t);
            b.onsuccess = () => {
              b.result && (f.setup = b.result);
              const C = a.objectStore(this.stores.analysis).index("trade_id").get(t);
              C.onsuccess = () => {
                C.result && (f.analysis = C.result), o(f);
              }, C.onerror = (k) => {
                console.error("Error getting analysis data:", k), o(f);
              };
            }, b.onerror = (E) => {
              console.error("Error getting setup data:", E), o(f);
            };
          }, y.onerror = (x) => {
            console.error("Error getting FVG details:", x), o(f);
          };
        }, l.onerror = (d) => {
          console.error("Error getting trade:", d), i(new Error("Failed to get trade"));
        };
      });
    } catch (r) {
      return console.error("Error in getTradeById:", r), null;
    }
  }
  /**
   * Get performance metrics from all trades
   * @returns A promise that resolves with performance metrics
   */
  async getPerformanceMetrics() {
    try {
      const t = await this.initDB();
      return new Promise((r, o) => {
        const s = t.transaction([this.stores.trades], "readonly").objectStore(this.stores.trades).getAll();
        s.onsuccess = () => {
          const l = s.result;
          if (l.length === 0) {
            r({
              totalTrades: 0,
              winningTrades: 0,
              losingTrades: 0,
              winRate: 0,
              profitFactor: 0,
              averageWin: 0,
              averageLoss: 0,
              largestWin: 0,
              largestLoss: 0,
              totalPnl: 0,
              maxDrawdown: 0,
              maxDrawdownPercent: 0,
              sharpeRatio: 0,
              sortinoRatio: 0,
              calmarRatio: 0,
              averageRMultiple: 0,
              expectancy: 0,
              sqn: 0,
              period: "all",
              startDate: "",
              endDate: ""
            });
            return;
          }
          const d = l.length, f = l.filter((O) => O[Y.WIN_LOSS] === "Win").length, u = l.filter((O) => O[Y.WIN_LOSS] === "Loss").length, m = d > 0 ? f / d * 100 : 0, y = l.filter((O) => O.achieved_pl !== void 0).map((O) => O.achieved_pl), x = y.reduce((O, J) => O + J, 0), h = y.filter((O) => O > 0), b = y.filter((O) => O < 0), E = h.length > 0 ? h.reduce((O, J) => O + J, 0) / h.length : 0, S = b.length > 0 ? Math.abs(b.reduce((O, J) => O + J, 0) / b.length) : 0, C = h.length > 0 ? Math.max(...h) : 0, k = b.length > 0 ? Math.abs(Math.min(...b)) : 0, $ = h.reduce((O, J) => O + J, 0), j = Math.abs(b.reduce((O, J) => O + J, 0)), N = j > 0 ? $ / j : 0, _ = l.filter((O) => O[Y.R_MULTIPLE] !== void 0).map((O) => O[Y.R_MULTIPLE]), R = _.length > 0 ? _.reduce((O, J) => O + J, 0) / _.length : 0, D = R * (m / 100);
          let K = 0, W = 0, L = 0;
          for (const O of l)
            if (O.achieved_pl !== void 0) {
              K += O.achieved_pl, K > W && (W = K);
              const J = W - K;
              J > L && (L = J);
            }
          const B = W > 0 ? L / W * 100 : 0, te = _.length > 0 ? Math.sqrt(_.length) * R / Math.sqrt(_.reduce((O, J) => O + Math.pow(J - R, 2), 0) / _.length) : 0, re = l.map((O) => O.date).sort(), fe = re.length > 0 ? re[0] : "", oe = re.length > 0 ? re[re.length - 1] : "";
          r({
            totalTrades: d,
            winningTrades: f,
            losingTrades: u,
            winRate: m,
            profitFactor: N,
            averageWin: E,
            averageLoss: S,
            largestWin: C,
            largestLoss: k,
            totalPnl: x,
            maxDrawdown: L,
            maxDrawdownPercent: B,
            sharpeRatio: 0,
            // Would need daily returns to calculate
            sortinoRatio: 0,
            // Would need daily returns to calculate
            calmarRatio: 0,
            // Would need daily returns to calculate
            averageRMultiple: R,
            expectancy: D,
            sqn: te,
            period: "all",
            startDate: fe,
            endDate: oe
          });
        }, s.onerror = (l) => {
          console.error("Error getting performance metrics:", l), o(new Error("Failed to get performance metrics"));
        };
      });
    } catch (t) {
      throw console.error("Error in getPerformanceMetrics:", t), new Error("Failed to get performance metrics");
    }
  }
  /**
   * Filter trades based on criteria
   * @param filters The filter criteria
   * @returns A promise that resolves with filtered trades
   */
  async filterTrades(t) {
    try {
      const r = await this.initDB();
      return new Promise((o, i) => {
        const a = r.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readonly"), l = a.objectStore(this.stores.trades).getAll();
        l.onsuccess = async () => {
          let d = l.result;
          t.dateFrom && (d = d.filter((u) => u.date >= t.dateFrom)), t.dateTo && (d = d.filter((u) => u.date <= t.dateTo)), t.model_type && (d = d.filter((u) => u[Y.MODEL_TYPE] === t.model_type)), t.session && (d = d.filter((u) => u[Y.SESSION] === t.session)), t.direction && (d = d.filter((u) => u[Y.DIRECTION] === t.direction)), t.win_loss && (d = d.filter((u) => u[Y.WIN_LOSS] === t.win_loss)), t.market && (d = d.filter((u) => u[Y.MARKET] === t.market)), t.min_r_multiple !== void 0 && (d = d.filter((u) => u[Y.R_MULTIPLE] !== void 0 && u[Y.R_MULTIPLE] >= t.min_r_multiple)), t.max_r_multiple !== void 0 && (d = d.filter((u) => u[Y.R_MULTIPLE] !== void 0 && u[Y.R_MULTIPLE] <= t.max_r_multiple)), t.min_pattern_quality !== void 0 && (d = d.filter((u) => u[Y.PATTERN_QUALITY_RATING] !== void 0 && u[Y.PATTERN_QUALITY_RATING] >= t.min_pattern_quality)), t.max_pattern_quality !== void 0 && (d = d.filter((u) => u[Y.PATTERN_QUALITY_RATING] !== void 0 && u[Y.PATTERN_QUALITY_RATING] <= t.max_pattern_quality));
          const f = [];
          for (const u of d) {
            const m = {
              trade: u
            }, h = a.objectStore(this.stores.fvg_details).index("trade_id").get(u.id);
            await new Promise((j) => {
              h.onsuccess = () => {
                h.result && (m.fvg_details = h.result), j();
              }, h.onerror = () => j();
            });
            const S = a.objectStore(this.stores.setups).index("trade_id").get(u.id);
            await new Promise((j) => {
              S.onsuccess = () => {
                S.result && (m.setup = S.result), j();
              }, S.onerror = () => j();
            });
            const $ = a.objectStore(this.stores.analysis).index("trade_id").get(u.id);
            await new Promise((j) => {
              $.onsuccess = () => {
                $.result && (m.analysis = $.result), j();
              }, $.onerror = () => j();
            }), f.push(m);
          }
          o(f);
        }, l.onerror = (d) => {
          console.error("Error filtering trades:", d), i(new Error("Failed to filter trades"));
        };
      });
    } catch (r) {
      throw console.error("Error in filterTrades:", r), new Error("Failed to filter trades");
    }
  }
  /**
   * Get all trades (simplified version for backward compatibility)
   * @returns A promise that resolves with all trades
   */
  async getAllTrades() {
    try {
      return await this.filterTrades({});
    } catch (t) {
      return console.error("Error in getAllTrades:", t), [];
    }
  }
  /**
   * Delete a trade and all related data
   * @param id The ID of the trade to delete
   * @returns A promise that resolves when the trade is deleted
   */
  async deleteTrade(t) {
    try {
      const r = await this.initDB();
      return new Promise((o, i) => {
        const a = r.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readwrite");
        a.onerror = (S) => {
          console.error("Transaction error:", S), i(new Error("Failed to delete trade"));
        };
        const d = a.objectStore(this.stores.fvg_details).index("trade_id").openCursor(IDBKeyRange.only(t));
        d.onsuccess = (S) => {
          const C = S.target.result;
          C && (C.delete(), C.continue());
        };
        const m = a.objectStore(this.stores.setups).index("trade_id").openCursor(IDBKeyRange.only(t));
        m.onsuccess = (S) => {
          const C = S.target.result;
          C && (C.delete(), C.continue());
        };
        const h = a.objectStore(this.stores.analysis).index("trade_id").openCursor(IDBKeyRange.only(t));
        h.onsuccess = (S) => {
          const C = S.target.result;
          C && (C.delete(), C.continue());
        };
        const E = a.objectStore(this.stores.trades).delete(t);
        a.oncomplete = () => {
          o();
        }, E.onerror = (S) => {
          console.error("Error deleting trade:", S), i(new Error("Failed to delete trade"));
        };
      });
    } catch (r) {
      throw console.error("Error in deleteTrade:", r), new Error("Failed to delete trade");
    }
  }
  /**
   * Update a trade with all related data
   * @param id The trade ID to update
   * @param tradeData Updated trade data
   * @returns A promise that resolves when the trade is updated
   */
  async updateTradeWithDetails(t, r) {
    try {
      const o = await this.initDB();
      return new Promise((i, a) => {
        const s = o.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readwrite");
        s.onerror = (u) => {
          console.error("Transaction error:", u), a(new Error("Failed to update trade"));
        };
        const l = s.objectStore(this.stores.trades), d = {
          ...r.trade,
          id: t,
          updated_at: (/* @__PURE__ */ new Date()).toISOString()
        }, f = l.put(d);
        f.onsuccess = () => {
          if (r.fvg_details) {
            const u = s.objectStore(this.stores.fvg_details), m = {
              ...r.fvg_details,
              trade_id: t
            };
            u.put(m);
          }
          if (r.setup) {
            const u = s.objectStore(this.stores.setups), m = {
              ...r.setup,
              trade_id: t
            };
            u.put(m);
          }
          if (r.analysis) {
            const u = s.objectStore(this.stores.analysis), m = {
              ...r.analysis,
              trade_id: t
            };
            u.put(m);
          }
        }, s.oncomplete = () => {
          i();
        }, f.onerror = (u) => {
          console.error("Error updating trade:", u), a(new Error("Failed to update trade"));
        };
      });
    } catch (o) {
      throw console.error("Error in updateTradeWithDetails:", o), new Error("Failed to update trade");
    }
  }
}
const Or = new jc(), ld = Or, dd = Or;
export {
  Wl as AVAILABLE_THEMES,
  dl as AppErrorBoundary,
  Je as AtomicDesignValidationEngine,
  Ze as Badge,
  le as Button,
  ii as Card,
  Sl as DashboardSection,
  Cl as DashboardTemplate,
  wl as DataCard,
  sl as DualTimeDisplay,
  or as EmptyState,
  fl as EnhancedFormField,
  yi as ErrorBoundary,
  Nl as F1Container,
  Dl as F1Form,
  Ll as F1FormField,
  jl as F1Header,
  ul as FeatureErrorBoundary,
  hl as FormField,
  bl as HierarchicalSessionSelector,
  Ie as Input,
  cl as LoadingCell,
  nn as LoadingPlaceholder,
  ll as LoadingSpinner,
  P as MacroPeriodType,
  xl as Modal,
  vo as OrderSide,
  wo as OrderStatus,
  bo as OrderType,
  ke as SETUP_ELEMENTS,
  Pe as Select,
  al as SelectDropdown,
  Q as SessionType,
  se as SessionUtils,
  El as SetupBuilder,
  ml as SortableTable,
  Xc as StatusIndicator,
  I as TRADE_COLUMN_IDS,
  Ct as TRADING_ATOMS,
  be as TRADING_MOLECULES,
  ze as TRADING_ORGANISMS,
  pl as TabPanel,
  yl as Table,
  Jc as Tag,
  Pr as ThemeContext,
  Ul as ThemeProvider,
  So as TimeInForce,
  Zc as TimePicker,
  Il as TradeAnalysis,
  Wt as TradeConverters,
  xo as TradeDirection,
  Tl as TradeMetrics,
  yo as TradeStatus,
  vl as TradeTable,
  Ks as TradeTableFilters,
  Hs as TradeTableRow,
  Lr as UnifiedErrorBoundary,
  Qc as VALID_TRADING_MODELS,
  w as baseColors,
  nt as borderRadius,
  ot as breakpoints,
  sd as captureError,
  el as convertLocalToNY,
  er as convertNYToLocal,
  Tt as convertSessionToDualTime,
  Ql as createSelector,
  Kl as createStoreContext,
  ie as darkModeColors,
  Mr as darkTheme,
  od as debounce,
  St as f1OfficialTheme,
  Lt as f1Theme,
  rt as fontFamilies,
  pe as fontSizes,
  et as fontWeights,
  Jl as formatCurrency,
  ed as formatDate,
  Zl as formatPercentage,
  dr as formatTime,
  nl as formatTimeForDesktop,
  Nn as formatTimeForMobile,
  In as formatTimeInterval,
  Yl as generateAllThemeCSS,
  Tc as generateCSSVariables,
  rd as generateId,
  Ic as generateSemanticVariables,
  Kc as generateSmartSuggestions,
  Mc as getAtomById,
  _c as getAtomsByCategory,
  Pc as getAtomsForModel,
  Ps as getCompactTradeTableColumns,
  wt as getCurrentDualTime,
  tl as getCurrentNYMinutes,
  rl as getCurrentNYTime,
  zc as getExpressionsForAtomType,
  $c as getMoleculeById,
  Fc as getMoleculesByAtom,
  Oc as getMoleculesByType,
  Ac as getMoleculesForModel,
  Gc as getOrganismById,
  Wc as getOrganismMatches,
  qc as getOrganismsByConfidence,
  Bc as getOrganismsByModel,
  Os as getPerformanceTradeTableColumns,
  Bl as getProfitLossColors,
  Vl as getProfitLossSize,
  Gl as getProfitLossVariant,
  il as getSessionStatus,
  Hc as getSuggestedSetupName,
  Qe as getTimeUntilNYTime,
  Ms as getTradeTableColumns,
  Et as getUserTimezone,
  id as initMonitoring,
  jn as isCurrentTimeInNYWindow,
  Rl as lightModeColors,
  ql as lightTheme,
  tt as lineHeights,
  ko as matchMoleculesToOrganisms,
  Lt as mercedesGreenTheme,
  ol as minutesToTime,
  Xl as persistState,
  Al as profitLossBaseStyles,
  hc as profitLossColors,
  zl as profitLossLoadingStyles,
  mc as profitLossSizes,
  ad as setUser,
  it as shadows,
  gl as sortFunctions,
  X as spacing,
  cd as startTransaction,
  nd as throttle,
  Ke as timeToMinutes,
  dd as tradeStorage,
  ld as tradeStorageService,
  st as transitions,
  td as truncateText,
  kl as useAsyncData,
  $l as useDataFormatting,
  Fl as useDataSection,
  _l as useDebounce,
  Ml as useErrorHandler,
  Ti as useFormField,
  pc as useLoadingState,
  Tr as useLocalStorage,
  Pl as usePagination,
  Ol as useProfitLossFormatting,
  bs as useSessionSelection,
  Mi as useSortableTable,
  Hl as useTheme,
  Uc as validateMoleculeForModel,
  Vc as validateMoleculeForModelBasic,
  Yc as validateMoleculeSelection,
  Ei as validationRules,
  at as zIndex
};
