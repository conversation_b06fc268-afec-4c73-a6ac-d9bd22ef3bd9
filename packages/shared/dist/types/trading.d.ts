/**
 * Trading Data Types
 *
 * This module exports type definitions for trading data.
 */
/**
 * Trade Direction
 */
export declare enum TradeDirection {
    LONG = "LONG",
    SHORT = "SHORT"
}
/**
 * Trade Status
 */
export declare enum TradeStatus {
    OPEN = "OPEN",
    CLOSED = "CLOSED",
    CANCELED = "CANCELED",
    REJECTED = "REJECTED",
    PENDING = "PENDING"
}
/**
 * Order Type
 */
export declare enum OrderType {
    MARKET = "MARKET",
    LIMIT = "LIMIT",
    STOP = "STOP",
    STOP_LIMIT = "STOP_LIMIT"
}
/**
 * Order Side
 */
export declare enum OrderSide {
    BUY = "BUY",
    SELL = "SELL"
}
/**
 * Order Status
 */
export declare enum OrderStatus {
    PENDING = "PENDING",
    FILLED = "FILLED",
    PARTIALLY_FILLED = "PARTIALLY_FILLED",
    CANCELED = "CANCELED",
    REJECTED = "REJECTED"
}
/**
 * Time in Force
 */
export declare enum TimeInForce {
    GTC = "GTC",// Good Till Canceled
    IOC = "IOC",// Immediate or Cancel
    FOK = "FOK",// Fill or Kill
    DAY = "DAY"
}
/**
 * Position
 */
export interface Position {
    /** Unique identifier for the position */
    id: string;
    /** Symbol of the instrument */
    symbol: string;
    /** Quantity of the position */
    quantity: number;
    /** Average entry price */
    entryPrice: number;
    /** Current market price */
    currentPrice: number;
    /** Direction of the position (LONG or SHORT) */
    direction: TradeDirection;
    /** Unrealized profit/loss */
    unrealizedPnl: number;
    /** Realized profit/loss */
    realizedPnl: number;
    /** Initial margin required */
    initialMargin: number;
    /** Maintenance margin required */
    maintenanceMargin: number;
    /** Timestamp when the position was opened */
    openTime: string;
    /** Timestamp when the position was last updated */
    updateTime: string;
    /** Associated orders (entry, take profit, stop loss) */
    orders: string[];
    /** Risk to reward ratio */
    riskRewardRatio?: number;
    /** Risk per trade */
    risk?: number;
    /** Tags for the position */
    tags?: string[];
    /** Notes for the position */
    notes?: string;
}
/**
 * Order
 */
export interface Order {
    /** Unique identifier for the order */
    id: string;
    /** Symbol of the instrument */
    symbol: string;
    /** Order type (MARKET, LIMIT, STOP, STOP_LIMIT) */
    type: OrderType;
    /** Order side (BUY, SELL) */
    side: OrderSide;
    /** Order status */
    status: OrderStatus;
    /** Quantity of the order */
    quantity: number;
    /** Filled quantity */
    filledQuantity: number;
    /** Price of the order (for LIMIT and STOP_LIMIT orders) */
    price?: number;
    /** Stop price (for STOP and STOP_LIMIT orders) */
    stopPrice?: number;
    /** Time in force */
    timeInForce: TimeInForce;
    /** Whether the order is a reduce-only order */
    reduceOnly: boolean;
    /** Whether the order is a post-only order */
    postOnly: boolean;
    /** Timestamp when the order was created */
    createTime: string;
    /** Timestamp when the order was last updated */
    updateTime: string;
    /** Timestamp when the order was filled */
    fillTime?: string;
    /** Average fill price */
    avgFillPrice?: number;
    /** Commission paid */
    commission?: number;
    /** Associated position ID */
    positionId?: string;
    /** Parent order ID (for OCO orders) */
    parentOrderId?: string;
    /** Client order ID */
    clientOrderId?: string;
    /** Order purpose (ENTRY, TAKE_PROFIT, STOP_LOSS) */
    purpose?: 'ENTRY' | 'TAKE_PROFIT' | 'STOP_LOSS';
}
/**
 * Market Data
 */
export interface MarketData {
    /** Symbol of the instrument */
    symbol: string;
    /** Last price */
    lastPrice: number;
    /** Bid price */
    bidPrice: number;
    /** Ask price */
    askPrice: number;
    /** 24-hour high price */
    highPrice: number;
    /** 24-hour low price */
    lowPrice: number;
    /** 24-hour volume */
    volume: number;
    /** 24-hour price change */
    priceChange: number;
    /** 24-hour price change percentage */
    priceChangePercent: number;
    /** Timestamp of the data */
    timestamp: string;
}
/**
 * Performance Metrics
 */
export interface PerformanceMetrics {
    /** Total number of trades */
    totalTrades: number;
    /** Number of winning trades */
    winningTrades: number;
    /** Number of losing trades */
    losingTrades: number;
    /** Win rate (percentage) */
    winRate: number;
    /** Profit factor (gross profit / gross loss) */
    profitFactor: number;
    /** Average win amount */
    averageWin: number;
    /** Average loss amount */
    averageLoss: number;
    /** Largest win amount */
    largestWin: number;
    /** Largest loss amount */
    largestLoss: number;
    /** Total profit/loss */
    totalPnl: number;
    /** Maximum drawdown */
    maxDrawdown: number;
    /** Maximum drawdown percentage */
    maxDrawdownPercent: number;
    /** Sharpe ratio */
    sharpeRatio: number;
    /** Sortino ratio */
    sortinoRatio: number;
    /** Calmar ratio */
    calmarRatio: number;
    /** Average R multiple */
    averageRMultiple: number;
    /** Expectancy (average R multiple * win rate) */
    expectancy: number;
    /** System quality number (SQN) */
    sqn: number;
    /** Time period for the metrics */
    period: 'day' | 'week' | 'month' | 'year' | 'all';
    /** Start date of the period */
    startDate: string;
    /** End date of the period */
    endDate: string;
}
/**
 * Trading Session Record (Database Schema)
 */
export interface TradingSessionRecord {
    /** Unique identifier for the session */
    id: string;
    /** Session name */
    name: string;
    /** Session date */
    date: string;
    /** Session start time */
    startTime: string;
    /** Session end time */
    endTime?: string;
    /** Session status (ACTIVE, COMPLETED) */
    status: 'ACTIVE' | 'COMPLETED';
    /** Session notes */
    notes?: string;
    /** Session tags */
    tags?: string[];
    /** Session metrics */
    metrics?: {
        totalTrades: number;
        winningTrades: number;
        losingTrades: number;
        winRate: number;
        totalPnl: number;
    };
    /** Associated trade IDs */
    tradeIds: string[];
}
/**
 * Setup Components Interface
 *
 * Modular setup construction components that replace complex dropdown-based
 * setup classification with atomic elements that can be combined infinitely.
 */
export interface SetupComponents {
    /** Constant element: Parent arrays or FVG types */
    constant: string;
    /** Action element: Liquidity events (optional) */
    action?: string;
    /** Variable element: RD types (optional) */
    variable?: string;
    /** Entry element: Entry methods */
    entry: string;
}
/**
 * Atomic Design Trading System Types
 *
 * These types define the atomic design system for trading strategy documentation.
 * This is separate from UI component atomic design patterns.
 */
/**
 * Atom Categories for Trading Strategy Atomic Design
 */
export type AtomCategory = 'session-fvg' | 'htf-pd-array' | 'strength-fvg' | 'liquidity-pool-time' | 'liquidity-pool-htf' | 'historical-pd-array';
/**
 * Molecule Types for Trading Strategy Atomic Design
 */
export type MoleculeType = 'state' | 'behavior' | 'target';
/**
 * Model Templates for Trading Strategy Atomic Design
 */
export type ModelTemplate = 'FVG-RD' | 'RD-Cont';
/**
 * Confidence Ratings for Organisms
 */
export type ConfidenceRating = 'high' | 'medium' | 'low';
/**
 * Validation Status for Organisms
 */
export type ValidationStatus = 'untested' | 'backtested' | 'live-proven';
/**
 * Atom Interface for Trading Strategy Atomic Design
 */
export interface TradingAtom {
    /** Unique identifier */
    id: string;
    /** Display name */
    name: string;
    /** Atom category */
    category: AtomCategory;
    /** Description */
    description?: string;
    /** Model eligibility */
    modelEligibility: {
        'FVG-RD': boolean;
        'RD-Cont': boolean;
    };
}
/**
 * Molecule Interface for Trading Strategy Atomic Design
 */
export interface TradingMolecule {
    /** Unique identifier */
    id: string;
    /** Associated atom */
    atom: string;
    /** Molecule type */
    type: MoleculeType;
    /** Expression/behavior */
    expression: string;
    /** Description */
    description?: string;
    /** Model eligibility */
    modelEligibility: {
        'FVG-RD': boolean;
        'RD-Cont': boolean;
    };
}
/**
 * Bias Assistance for Organisms
 */
export interface BiasAssistance {
    /** Atom providing bias */
    atom: string;
    /** Timeframe if applicable */
    timeframe?: string;
    /** Expression/state */
    expression: string;
    /** Influence description */
    influence: string;
}
/**
 * Organism Interface for Trading Strategy Atomic Design
 */
export interface TradingOrganism {
    /** Unique identifier */
    id: string;
    /** Setup name */
    name: string;
    /** Model template */
    modelTemplate: ModelTemplate;
    /** Model alias for display */
    modelAlias: string;
    /** Required molecules */
    molecules: Array<{
        atom: string;
        type: MoleculeType;
        expression: string;
    }>;
    /** Bias assistance */
    biasAssistance?: BiasAssistance[];
    /** Confidence rating */
    confidenceRating: ConfidenceRating;
    /** Validation status */
    validationStatus: ValidationStatus;
    /** Trigger condition description */
    triggerCondition: string;
}
/**
 * Atomic Design Selection for Trade Records
 */
export interface AtomicDesignSelection {
    /** Selected molecules */
    molecules: Array<{
        atom: string;
        type: MoleculeType;
        expression: string;
    }>;
    /** Matched organism (if any) */
    matchedOrganism?: {
        id: string;
        name: string;
        confidence: 'exact' | 'partial' | 'none';
    };
    /** Manual setup override */
    manualSetupName?: string;
    /** Model template */
    modelTemplate?: ModelTemplate;
}
/**
 * Trade Record (Database Schema)
 */
export interface TradeRecord {
    id?: number;
    date: string;
    model_type: string;
    session?: string;
    setup?: string;
    entry_time?: string;
    exit_time?: string;
    fvg_time?: string;
    balance_time?: string;
    rd_time?: string;
    target_hit_time?: string;
    time_relative_to_model?: number;
    direction: 'Long' | 'Short';
    market?: string;
    r_multiple?: number;
    entry_price?: number;
    exit_price?: number;
    risk_points?: number;
    achieved_pl?: number;
    no_of_contracts?: number;
    win_loss?: 'Win' | 'Loss';
    pattern_quality_rating?: number;
    model?: string;
    dol_target?: string;
    rd_type?: string;
    draw_on_liquidity?: string;
    setupComponents?: SetupComponents;
    atomicDesign?: AtomicDesignSelection;
    notes?: string;
    news?: string;
    created_at?: string;
    updated_at?: string;
}
/**
 * Trade FVG Details
 */
export interface TradeFvgDetails {
    id?: number;
    trade_id: number;
    fvg_date?: string;
    rd_type?: string;
    entry_version?: string;
    draw_on_liquidity?: string;
}
/**
 * Trade Setup
 */
export interface TradeSetup {
    id?: number;
    trade_id: number;
    primary_setup?: string;
    secondary_setup?: string;
    liquidity_taken?: string;
    additional_fvgs?: string;
    dol?: string;
}
/**
 * Trade Analysis Record (Database Schema)
 */
export interface TradeAnalysisRecord {
    id?: number;
    trade_id: number;
    tradingview_link?: string;
    dol_target_type?: string;
    beyond_target?: string;
    clustering?: string;
    path_quality?: string;
    idr_context?: string;
    sequential_fvg_rd?: string;
    dol_notes?: string;
}
/**
 * Complete Trade Data
 */
export interface CompleteTradeData {
    trade: TradeRecord;
    fvg_details?: TradeFvgDetails;
    setup?: TradeSetup;
    analysis?: TradeAnalysisRecord;
}
/**
 * Trade Filters
 */
export interface TradeFilters {
    dateFrom?: string;
    dateTo?: string;
    model_type?: string;
    session?: string;
    direction?: 'Long' | 'Short';
    win_loss?: 'Win' | 'Loss';
    market?: string;
    min_r_multiple?: number;
    max_r_multiple?: number;
    min_pattern_quality?: number;
    max_pattern_quality?: number;
}
/**
 * Legacy Trade Interface (for backward compatibility)
 */
export interface Trade {
    id: string;
    symbol: string;
    date: string;
    direction: 'Long' | 'Short';
    size: number;
    entry: number;
    exit: number;
    stopLoss: number;
    takeProfit: number;
    profitLoss: number;
    strategy: string;
    notes: string;
    tags: string[];
    images?: string[];
}
/**
 * Type Conversion Utilities
 */
export declare const TradeConverters: {
    /**
     * Convert CompleteTradeData to legacy Trade interface
     */
    completeTradeToLegacy: (completeTradeData: CompleteTradeData) => Trade;
    /**
     * Convert legacy Trade to CompleteTradeData
     */
    legacyToCompleteTrade: (trade: Trade) => CompleteTradeData;
    /**
     * Convert array of CompleteTradeData to legacy Trade array
     */
    completeTradeArrayToLegacy: (completeTrades: CompleteTradeData[]) => Trade[];
    /**
     * Convert array of legacy Trade to CompleteTradeData array
     */
    legacyArrayToCompleteTrade: (trades: Trade[]) => CompleteTradeData[];
};
/**
 * Trade Form Data (for forms)
 */
export interface TradeFormData {
    date: string;
    symbol: string;
    direction: 'long' | 'short';
    quantity: string;
    entryPrice: string;
    exitPrice: string;
    stopLoss?: string;
    takeProfit?: string;
    profit: string;
    modelType?: string;
    session?: string;
    setup?: string;
    entryTime?: string;
    exitTime?: string;
    rdTime?: string;
    market?: string;
    rMultiple?: string;
    entryVersion?: string;
    riskPoints?: string;
    patternQuality?: string;
    model?: string;
    dolTarget?: string;
    rdType?: string;
    drawOnLiquidity?: string;
    setupComponents?: SetupComponents;
    atomicDesignMolecules?: Array<{
        atom: string;
        type: MoleculeType;
        expression: string;
    }>;
    atomicDesignSuggestedSetup?: string;
    atomicDesignManualSetup?: string;
    atomicDesignModelTemplate?: ModelTemplate;
    patternQualityClarity?: string;
    patternQualityConfluence?: string;
    patternQualityContext?: string;
    patternQualityRisk?: string;
    patternQualityReward?: string;
    patternQualityTimeframe?: string;
    patternQualityVolume?: string;
    patternQualityNotes?: string;
    dolType?: string;
    dolStrength?: string;
    dolReaction?: string;
    dolContext?: string[];
    dolPriceAction?: string;
    dolVolumeProfile?: string;
    dolTimeOfDay?: string;
    dolMarketStructure?: string;
    dolEffectiveness?: string;
    dolNotes?: string;
    primarySetupCategory?: string;
    primarySetupType?: string;
    secondarySetupCategory?: string;
    secondarySetupType?: string;
    liquidityTaken?: string;
    additionalFVGs?: string[];
    dolTargetType?: string;
    specificDOLType?: string;
    parentPDArray?: string;
    notes: string;
    tags?: string[];
    result: 'win' | 'loss' | 'breakeven';
}
//# sourceMappingURL=trading.d.ts.map