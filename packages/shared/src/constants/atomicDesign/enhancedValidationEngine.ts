/**
 * Enhanced Validation Engine for Atomic Design Trading System
 *
 * Advanced validation engine with bias assistance integration, smart completion,
 * and F1 racing theme performance analytics integration.
 */

import type {
  BiasAssistance,
  ModelTemplate,
  MoleculeType,
  TradingOrganism,
} from '../../types/trading';
import { AtomicDesignValidationEngine, OrganismMatch, SmartSuggestion } from './validationEngine';

export interface EnhancedOrganismMatch extends OrganismMatch {
  biasAssistance?: BiasAssistance[];
  confidenceBoost?: number; // 0-100% boost from bias assistance
  recommendedNextMolecules?: Array<{
    atom: string;
    type: MoleculeType;
    expression: string;
    priority: 'high' | 'medium' | 'low';
    reason: string;
  }>;
}

export interface EnhancedSmartSuggestion extends SmartSuggestion {
  priority: 'critical' | 'high' | 'medium' | 'low';
  confidence: number; // 0-100%
  biasSupport?: boolean;
  f1Theme?: {
    color: 'mercedes-green' | 'mclaren-orange' | 'ferrari-red' | 'racing-blue';
    animation?: 'pulse' | 'glow' | 'stripe';
  };
}

/**
 * Enhanced Validation Engine Class
 */
export class EnhancedAtomicDesignValidationEngine extends AtomicDesignValidationEngine {
  /**
   * Get enhanced organism matches with bias assistance analysis
   */
  static getEnhancedOrganismMatches(
    molecules: Array<{ atom: string; type: MoleculeType; expression: string }>
  ): EnhancedOrganismMatch[] {
    const baseMatches = this.getOrganismMatches(molecules);

    return baseMatches.map(match => {
      const organism = match.organism;
      const biasAssistance = organism.biasAssistance || [];

      // Calculate confidence boost from bias assistance
      let confidenceBoost = 0;
      const activeBiasAssistance: BiasAssistance[] = [];

      biasAssistance.forEach(bias => {
        // Check if any selected molecules could serve as bias assistance
        const hasBiasSupport = molecules.some(
          mol => mol.atom === bias.atom && mol.expression === bias.expression
        );

        if (hasBiasSupport) {
          activeBiasAssistance.push(bias);
          confidenceBoost += 15; // Each bias assistance adds 15% confidence
        }
      });

      // Generate recommended next molecules
      const recommendedNextMolecules = this.generateRecommendedMolecules(
        molecules,
        organism,
        match.missingMolecules
      );

      return {
        ...match,
        biasAssistance: activeBiasAssistance,
        confidenceBoost: Math.min(confidenceBoost, 50), // Cap at 50%
        recommendedNextMolecules,
      };
    });
  }

  /**
   * Generate recommended next molecules based on organism requirements
   */
  private static generateRecommendedMolecules(
    currentMolecules: Array<{ atom: string; type: MoleculeType; expression: string }>,
    organism: TradingOrganism,
    missingMolecules: Array<{ atom: string; type: MoleculeType; expression: string }>
  ) {
    const recommendations: Array<{
      atom: string;
      type: MoleculeType;
      expression: string;
      priority: 'high' | 'medium' | 'low';
      reason: string;
    }> = [];

    // Prioritize missing required molecules
    missingMolecules.forEach(missing => {
      recommendations.push({
        ...missing,
        priority: 'high',
        reason: `Required for ${organism.name} completion`,
      });
    });

    // Add bias assistance recommendations
    organism.biasAssistance?.forEach(bias => {
      const alreadySelected = currentMolecules.some(
        mol => mol.atom === bias.atom && mol.expression === bias.expression
      );

      if (!alreadySelected) {
        recommendations.push({
          atom: bias.atom,
          type: 'behavior', // Bias assistance is typically behavior
          expression: bias.expression,
          priority: 'medium',
          reason: `Bias assistance: ${bias.influence}`,
        });
      }
    });

    return recommendations.slice(0, 3); // Limit to top 3 recommendations
  }

  /**
   * Generate enhanced smart suggestions with F1 theme integration
   */
  static generateEnhancedSmartSuggestions(
    molecules: Array<{ atom: string; type: MoleculeType; expression: string }>,
    modelTemplate?: ModelTemplate
  ): EnhancedSmartSuggestion[] {
    const baseSuggestions = this.generateSmartSuggestions(molecules, modelTemplate);
    const enhancedMatches = this.getEnhancedOrganismMatches(molecules);

    const enhancedSuggestions: EnhancedSmartSuggestion[] = [];

    // Convert base suggestions to enhanced format
    baseSuggestions.forEach(suggestion => {
      enhancedSuggestions.push({
        ...suggestion,
        priority: this.calculateSuggestionPriority(suggestion, enhancedMatches),
        confidence: this.calculateSuggestionConfidence(suggestion, enhancedMatches),
        biasSupport: this.checkBiasSupport(suggestion, enhancedMatches),
        f1Theme: this.getF1ThemeForSuggestion(suggestion),
      });
    });

    // Add bias assistance suggestions
    enhancedMatches.forEach(match => {
      if (match.biasAssistance && match.biasAssistance.length > 0) {
        match.biasAssistance.forEach(bias => {
          enhancedSuggestions.push({
            type: 'completion',
            message: `🏎️ Add ${bias.atom} for bias assistance: ${bias.influence}`,
            priority: 'medium',
            confidence: 75,
            biasSupport: true,
            f1Theme: {
              color: 'racing-blue',
              animation: 'glow',
            },
            action: {
              atom: bias.atom,
              type: 'behavior',
              expression: bias.expression,
            },
          });
        });
      }
    });

    // Sort by priority and confidence
    return enhancedSuggestions
      .sort((a, b) => {
        const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        if (priorityDiff !== 0) return priorityDiff;
        return b.confidence - a.confidence;
      })
      .slice(0, 4); // Limit to top 4 suggestions
  }

  /**
   * Calculate suggestion priority based on context
   */
  private static calculateSuggestionPriority(
    suggestion: SmartSuggestion,
    matches: EnhancedOrganismMatch[]
  ): 'critical' | 'high' | 'medium' | 'low' {
    if (suggestion.type === 'completion' && matches.some(m => m.status === 'partial')) {
      return 'high';
    }
    if (suggestion.type === 'model-change') {
      return 'medium';
    }
    return 'low';
  }

  /**
   * Calculate suggestion confidence score
   */
  private static calculateSuggestionConfidence(
    suggestion: SmartSuggestion,
    matches: EnhancedOrganismMatch[]
  ): number {
    let baseConfidence = 60;

    if (suggestion.type === 'completion') baseConfidence += 20;
    if (matches.some(m => m.completeness > 50)) baseConfidence += 15;
    if (matches.some(m => m.biasAssistance && m.biasAssistance.length > 0)) baseConfidence += 10;

    return Math.min(baseConfidence, 95);
  }

  /**
   * Check if suggestion has bias support
   */
  private static checkBiasSupport(
    suggestion: SmartSuggestion,
    matches: EnhancedOrganismMatch[]
  ): boolean {
    return matches.some(
      match =>
        match.biasAssistance &&
        match.biasAssistance.some(bias => bias.atom === suggestion.action?.atom)
    );
  }

  /**
   * Get F1 theme styling for suggestion
   */
  private static getF1ThemeForSuggestion(suggestion: SmartSuggestion): {
    color: 'mercedes-green' | 'mclaren-orange' | 'ferrari-red' | 'racing-blue';
    animation?: 'pulse' | 'glow' | 'stripe';
  } {
    switch (suggestion.type) {
      case 'completion':
        return { color: 'mercedes-green', animation: 'glow' };
      case 'alternative':
        return { color: 'mclaren-orange', animation: 'stripe' };
      case 'model-change':
        return { color: 'racing-blue', animation: 'pulse' };
      default:
        return { color: 'racing-blue' };
    }
  }
}

/**
 * Export enhanced validation functions
 */
export const getEnhancedOrganismMatches =
  EnhancedAtomicDesignValidationEngine.getEnhancedOrganismMatches;
export const generateEnhancedSmartSuggestions =
  EnhancedAtomicDesignValidationEngine.generateEnhancedSmartSuggestions;
